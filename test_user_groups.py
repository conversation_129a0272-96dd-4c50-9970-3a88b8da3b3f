#!/usr/bin/env python3
"""
用户组功能测试脚本
测试用户组的创建、列表、更新等功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"
AUTH_TOKEN = "sk-123456"

def test_auth():
    """测试认证"""
    print("🔐 测试认证...")
    response = requests.post(f"{BASE_URL}/auth", data={"token": AUTH_TOKEN})
    print(f"认证状态: {response.status_code}")
    return response.status_code in [200, 302]

def test_list_groups():
    """测试获取用户组列表"""
    print("📋 测试获取用户组列表...")
    headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}
    response = requests.get(f"{BASE_URL}/admin/groups?limit=10&offset=0", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        groups = data.get('user_groups', [])
        print(f"✅ 获取用户组列表成功，共 {len(groups)} 个用户组")
        for group in groups:
            print(f"   - {group.get('group_id')}: {group.get('group_name')}")
        return True
    else:
        print(f"❌ 获取用户组列表失败: {response.status_code}")
        return False

def test_create_group():
    """测试创建用户组"""
    print("➕ 测试创建用户组...")
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}",
        "Content-Type": "application/json"
    }
    
    group_data = {
        "group_id": f"test_group_{int(time.time())}",
        "group_name": "测试用户组",
        "description": "这是一个测试用户组",
        "priority": 5,
        "status": "ACTIVE",
        "token_limit": 10000,
        "expiry_date": "2025-12-31",
        "auto_fallback": True,
        "context_isolation": True,
        "no_fallback_models": []
    }
    
    response = requests.post(f"{BASE_URL}/admin/groups", headers=headers, json=group_data)
    
    if response.status_code == 201:
        result = response.json()
        print(f"✅ 创建用户组成功: {result.get('group_id')}")
        return True, group_data['group_id']
    else:
        print(f"❌ 创建用户组失败: {response.status_code}")
        try:
            error = response.json()
            print(f"   错误信息: {error}")
        except:
            print(f"   响应内容: {response.text}")
        return False, None

def test_get_statistics():
    """测试获取统计信息"""
    print("📊 测试获取统计信息...")
    headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}
    response = requests.get(f"{BASE_URL}/admin/groups/statistics/overview", headers=headers)
    
    if response.status_code == 200:
        try:
            stats = response.json()
            print("✅ 获取统计信息成功")
            print(f"   总用户组: {stats.get('total_groups')}")
            print(f"   活跃用户组: {stats.get('active_groups')}")
            return True
        except Exception as e:
            print(f"❌ 解析统计信息失败: {e}")
            print(f"   响应内容: {response.text}")
            return False
    else:
        print(f"❌ 获取统计信息失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始用户组功能测试...")
    print("=" * 50)
    
    # 测试认证
    if not test_auth():
        print("❌ 认证失败，停止测试")
        return
    
    print()
    
    # 测试获取统计信息
    test_get_statistics()
    print()
    
    # 测试获取用户组列表
    test_list_groups()
    print()
    
    # 测试创建用户组
    success, group_id = test_create_group()
    print()
    
    if success:
        print("🎉 所有测试通过！")
        print(f"新创建的用户组ID: {group_id}")
    else:
        print("⚠️ 部分测试失败")
    
    print("=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
