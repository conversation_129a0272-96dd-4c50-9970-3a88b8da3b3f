@echo off
chcp 65001 >nul
title Gemini Balance 服务管理器

echo.
echo ========================================
echo    🔧 Gemini Balance 服务管理器
echo ========================================
echo.

:menu
echo 请选择操作:
echo.
echo [1] 安装服务
echo [2] 启动服务
echo [3] 停止服务
echo [4] 重启服务
echo [5] 卸载服务
echo [6] 查看服务状态
echo [7] 查看服务日志
echo [0] 退出
echo.
set /p choice=请输入选项 (0-7): 

if "%choice%"=="1" goto install
if "%choice%"=="2" goto start
if "%choice%"=="3" goto stop
if "%choice%"=="4" goto restart
if "%choice%"=="5" goto uninstall
if "%choice%"=="6" goto status
if "%choice%"=="7" goto logs
if "%choice%"=="0" goto exit
goto menu

:install
echo.
echo 📦 正在安装 Gemini Balance 服务...
echo.

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 需要管理员权限来安装服务
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    goto menu
)

:: 检查 pywin32 是否安装
py -c "import win32serviceutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 正在安装 pywin32 依赖...
    py -m pip install pywin32
    if %errorlevel% neq 0 (
        echo ❌ pywin32 安装失败
        pause
        goto menu
    )
)

:: 安装服务
py gemini_balance_service.py install
if %errorlevel% equ 0 (
    echo.
    echo ✅ 服务安装成功！
    echo.
    echo 服务信息:
    echo   服务名称: GeminiBalance
    echo   显示名称: Gemini Balance API Service
    echo   描述: Gemini API 代理和负载均衡服务
    echo.
    echo 💡 提示: 您现在可以通过 Windows 服务管理器或此脚本来管理服务
) else (
    echo ❌ 服务安装失败
)
pause
goto menu

:start
echo.
echo 🚀 正在启动 Gemini Balance 服务...
py gemini_balance_service.py start
pause
goto menu

:stop
echo.
echo 🛑 正在停止 Gemini Balance 服务...
py gemini_balance_service.py stop
pause
goto menu

:restart
echo.
echo 🔄 正在重启 Gemini Balance 服务...
py gemini_balance_service.py restart
pause
goto menu

:uninstall
echo.
echo ⚠️  确定要卸载 Gemini Balance 服务吗？
set /p confirm=输入 Y 确认卸载: 
if /i "%confirm%"=="Y" (
    echo.
    echo 🗑️  正在卸载 Gemini Balance 服务...
    
    :: 检查是否以管理员身份运行
    net session >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 错误: 需要管理员权限来卸载服务
        echo 请右键点击此脚本，选择"以管理员身份运行"
        pause
        goto menu
    )
    
    py gemini_balance_service.py uninstall
) else (
    echo 取消卸载
)
pause
goto menu

:status
echo.
echo 📊 查看服务状态...
echo.
sc query GeminiBalance
if %errorlevel% neq 0 (
    echo ❌ 服务未安装或查询失败
) else (
    echo.
    echo 💡 服务状态说明:
    echo   STOPPED - 已停止
    echo   RUNNING - 正在运行
    echo   PENDING - 状态变更中
)
pause
goto menu

:logs
echo.
echo 📋 查看服务日志...
echo.
if exist "logs\service.log" (
    echo 最近的日志内容:
    echo ----------------------------------------
    powershell "Get-Content logs\service.log -Tail 20"
    echo ----------------------------------------
    echo.
    echo 💡 完整日志文件位置: logs\service.log
) else (
    echo ❌ 日志文件不存在: logs\service.log
    echo 请确保服务已运行过至少一次
)
pause
goto menu

:exit
echo.
echo 👋 感谢使用 Gemini Balance 服务管理器！
echo.
exit /b 0
