#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini Balance 服务监控脚本
用于监控服务状态和健康检查
"""

import time
import socket
import requests
import subprocess
from datetime import datetime
from pathlib import Path

def check_service_status():
    """检查Windows服务状态"""
    try:
        result = subprocess.run(
            ['sc', 'query', 'GeminiBalance'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout
            if 'RUNNING' in output:
                return 'RUNNING'
            elif 'STOPPED' in output:
                return 'STOPPED'
            elif 'PENDING' in output:
                return 'PENDING'
            else:
                return 'UNKNOWN'
        else:
            return 'NOT_INSTALLED'
    except Exception as e:
        return f'ERROR: {e}'

def check_port_listening(port=8001):
    """检查端口是否在监听"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(2)
            result = s.connect_ex(('localhost', port))
            return result == 0
    except:
        return False

def check_api_health():
    """检查API健康状态"""
    try:
        response = requests.get('http://localhost:8001/', timeout=5)
        return response.status_code == 200
    except:
        return False

def get_process_info():
    """获取相关进程信息"""
    try:
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 有进程信息
                return len(lines) - 1  # 减去标题行
        return 0
    except:
        return -1

def log_status(message):
    """记录状态到日志文件"""
    log_dir = Path(__file__).parent / "logs"
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / "monitor.log"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"{timestamp} - {message}\n")

def main():
    """主监控循环"""
    print("Gemini Balance 服务监控器")
    print("=" * 50)
    print("按 Ctrl+C 停止监控")
    print()
    
    try:
        while True:
            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 检查各项状态
            service_status = check_service_status()
            port_listening = check_port_listening()
            api_healthy = check_api_health()
            python_processes = get_process_info()
            
            # 显示状态
            print(f"\r[{current_time}] ", end="")
            print(f"服务: {service_status:10} | ", end="")
            print(f"端口: {'✓' if port_listening else '✗':1} | ", end="")
            print(f"API: {'✓' if api_healthy else '✗':1} | ", end="")
            print(f"进程: {python_processes:2}", end="", flush=True)
            
            # 检查异常情况并记录
            if service_status == 'RUNNING' and not port_listening:
                log_status("警告: 服务运行中但端口未监听")
            elif service_status == 'RUNNING' and not api_healthy:
                log_status("警告: 服务运行中但API不健康")
            elif service_status == 'STOPPED' and port_listening:
                log_status("警告: 服务已停止但端口仍在监听")
            
            time.sleep(5)  # 每5秒检查一次
            
    except KeyboardInterrupt:
        print("\n\n监控已停止")
    except Exception as e:
        print(f"\n监控出错: {e}")
        log_status(f"监控出错: {e}")

if __name__ == '__main__':
    main()
