version: '3.8'

services:
  # 主应用服务
  gemini-balance-local:
    build: .
    container_name: gemini-balance-local
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
      - PORT=8001
      - DATABASE_URL=sqlite:///./data/gemini_balance_local.db
      - REDIS_URL=redis://redis-local:6379/0
      - LOG_LEVEL=DEBUG
      - DEBUG=true
      - ENVIRONMENT=development
      - RELOAD=true
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
      - ./.env.local:/app/.env
      # 开发时挂载源码以支持热重载
      - ./app:/app/app
    depends_on:
      - redis-local
      - mysql-local
    networks:
      - gemini-local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis-local:
    image: redis:7-alpine
    container_name: gemini-balance-redis-local
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_local_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - gemini-local
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库服务 (可选)
  mysql-local:
    image: mysql:8.0
    container_name: gemini-balance-mysql-local
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: localdev123
      MYSQL_DATABASE: gemini_balance
      MYSQL_USER: gemini_user
      MYSQL_PASSWORD: gemini_pass
    volumes:
      - mysql_local_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - gemini-local
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-plocaldev123"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL数据库服务 (可选)
  postgres-local:
    image: postgres:15-alpine
    container_name: gemini-balance-postgres-local
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: gemini_balance
      POSTGRES_USER: gemini_user
      POSTGRES_PASSWORD: gemini_pass
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - gemini-local
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gemini_user -d gemini_balance"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理 (可选)
  nginx-local:
    image: nginx:alpine
    container_name: gemini-balance-nginx-local
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx.local.conf:/etc/nginx/nginx.conf:ro
      - ./static:/usr/share/nginx/html/static:ro
    depends_on:
      - gemini-balance-local
    networks:
      - gemini-local

  # 监控服务 - Prometheus (可选)
  prometheus-local:
    image: prom/prometheus:latest
    container_name: gemini-balance-prometheus-local
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_local_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - gemini-local

  # 监控服务 - Grafana (可选)
  grafana-local:
    image: grafana/grafana:latest
    container_name: gemini-balance-grafana-local
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_local_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus-local
    networks:
      - gemini-local

  # 日志聚合 - ELK Stack (可选)
  elasticsearch-local:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: gemini-balance-elasticsearch-local
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_local_data:/usr/share/elasticsearch/data
    networks:
      - gemini-local

  kibana-local:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: gemini-balance-kibana-local
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch-local:9200
    depends_on:
      - elasticsearch-local
    networks:
      - gemini-local

volumes:
  redis_local_data:
  mysql_local_data:
  postgres_local_data:
  prometheus_local_data:
  grafana_local_data:
  elasticsearch_local_data:

networks:
  gemini-local:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
