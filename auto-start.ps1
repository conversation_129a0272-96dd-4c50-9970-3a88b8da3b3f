# Gemini Balance 自动启动脚本 (PowerShell)
# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "Gemini Balance - 自动启动"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🚀 Gemini Balance 自动启动脚本" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 Python 是否安装
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    Write-Host "✅ Python 版本检查: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到 Python，请先安装 Python 3.8+" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查是否在正确的目录
if (-not (Test-Path "start.py")) {
    Write-Host "❌ 错误: 未找到 start.py 文件，请确保在正确的项目目录中运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查并创建虚拟环境
if (-not (Test-Path "venv")) {
    Write-Host "📦 创建虚拟环境..." -ForegroundColor Yellow
    python -m venv venv
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 虚拟环境创建失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    Write-Host "✅ 虚拟环境创建成功" -ForegroundColor Green
}

# 激活虚拟环境
Write-Host "🔄 激活虚拟环境..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 虚拟环境激活失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 升级 pip
Write-Host "📦 升级 pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip --quiet

# 安装依赖
Write-Host "📦 安装项目依赖..." -ForegroundColor Yellow
python -m pip install -r requirements.txt --quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 依赖安装失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 初始化项目
Write-Host "⚙️ 初始化项目设置..." -ForegroundColor Yellow
python start.py --setup
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 项目初始化失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 启动开发服务器
Write-Host ""
Write-Host "🚀 启动开发服务器..." -ForegroundColor Green
Write-Host "📍 服务器地址: http://localhost:8001" -ForegroundColor Cyan
Write-Host "📍 管理面板: http://localhost:8001/admin" -ForegroundColor Cyan
Write-Host "📍 API 文档: http://localhost:8001/docs" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 提示: 按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

try {
    python start.py --reload --host 0.0.0.0 --port 8001
} catch {
    Write-Host "❌ 服务器启动失败: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "👋 服务器已停止" -ForegroundColor Yellow
    Read-Host "按任意键退出"
}
