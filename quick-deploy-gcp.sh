#!/bin/bash

# Google Cloud Run 快速部署脚本
# 适合新手使用的简化版本

set -e

echo "🚀 Google Cloud Run 快速部署"
echo "================================"

# 检查是否安装了gcloud
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud SDK 未安装"
    echo ""
    echo "请先安装 Google Cloud SDK:"
    echo "Windows: https://cloud.google.com/sdk/docs/install-sdk#windows"
    echo "macOS: brew install google-cloud-sdk"
    echo "Linux: curl https://sdk.cloud.google.com | bash"
    echo ""
    exit 1
fi

echo "✅ Google Cloud SDK 已安装"

# 检查是否已登录
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 请先登录 Google Cloud:"
    gcloud auth login
    gcloud auth application-default login
fi

echo "✅ Google Cloud 认证完成"

# 获取或设置项目ID
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT_ID" ]; then
    echo ""
    echo "📋 请输入您的 Google Cloud 项目ID:"
    echo "   (如果没有项目，请先在 https://console.cloud.google.com/ 创建)"
    read -p "项目ID: " PROJECT_ID
    gcloud config set project "$PROJECT_ID"
fi

echo "✅ 使用项目: $PROJECT_ID"

# 启用必要的API
echo "🔧 启用必要的API服务..."
gcloud services enable run.googleapis.com --quiet
gcloud services enable cloudbuild.googleapis.com --quiet
gcloud services enable containerregistry.googleapis.com --quiet
echo "✅ API服务已启用"

# 配置Docker认证
echo "🐳 配置Docker认证..."
gcloud auth configure-docker --quiet
echo "✅ Docker认证完成"

# 设置服务配置
SERVICE_NAME="gemini-balance"
REGION="asia-east1"  # 香港区域

echo ""
echo "📦 开始构建和部署..."
echo "服务名称: $SERVICE_NAME"
echo "部署区域: $REGION (香港)"
echo ""

# 构建镜像
echo "🏗️  构建Docker镜像..."
gcloud builds submit --tag gcr.io/"$PROJECT_ID"/"$SERVICE_NAME" --quiet

if [ $? -ne 0 ]; then
    echo "❌ 镜像构建失败"
    exit 1
fi

echo "✅ 镜像构建完成"

# 部署到Cloud Run
echo "🚀 部署到Cloud Run..."
gcloud run deploy "$SERVICE_NAME" \
    --image gcr.io/"$PROJECT_ID"/"$SERVICE_NAME" \
    --platform managed \
    --region "$REGION" \
    --allow-unauthenticated \
    --port 8001 \
    --memory 1Gi \
    --cpu 1 \
    --max-instances 10 \
    --set-env-vars "PORT=8001,DATABASE_URL=sqlite:///./data/gemini_balance.db,LOG_LEVEL=INFO,GOOGLE_CLOUD_PROJECT=$PROJECT_ID" \
    --quiet

if [ $? -ne 0 ]; then
    echo "❌ 部署失败"
    exit 1
fi

# 获取服务URL
SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)")

echo ""
echo "🎉 部署成功！"
echo "================================"
echo "📍 服务URL: $SERVICE_URL"
echo "📚 API文档: $SERVICE_URL/docs"
echo "🔍 健康检查: $SERVICE_URL/connection-pool/health"
echo "💾 缓存分析: $SERVICE_URL/cache/enhanced/analytics"
echo "🔀 智能路由: $SERVICE_URL/routing/health"
echo ""

# 测试服务
echo "🧪 测试服务..."
if curl -f "$SERVICE_URL" > /dev/null 2>&1; then
    echo "✅ 服务运行正常"
else
    echo "⚠️  服务可能还在启动中，请稍等片刻"
fi

echo ""
echo "📊 管理命令:"
echo "查看日志: gcloud run logs tail $SERVICE_NAME --region=$REGION"
echo "查看服务: gcloud run services describe $SERVICE_NAME --region=$REGION"
echo "删除服务: gcloud run services delete $SERVICE_NAME --region=$REGION"
echo ""

echo "🌍 配置Cloudflare CDN (可选):"
echo "1. 注册 Cloudflare: https://cloudflare.com/"
echo "2. 添加域名并设置DNS:"
echo "   类型: CNAME"
echo "   名称: api (或 @)"
echo "   目标: ${SERVICE_URL#https://}"
echo "   代理: 启用 (橙色云朵)"
echo ""

echo "💰 费用说明:"
echo "- 免费额度: 每月200万请求"
echo "- 当前配置: 完全在免费额度内"
echo "- 监控使用量: https://console.cloud.google.com/run"
echo ""

echo "🎉 恭喜！您的Gemini Balance已成功部署到Google Cloud Run！"
