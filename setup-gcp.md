# Google Cloud Run 详细部署指南

## 🌐 第一步：创建Google Cloud账户

### 1. 访问Google Cloud
- 网址：https://cloud.google.com/
- 点击右上角 "开始免费使用"

### 2. 注册流程
```
1. 使用Gmail账户登录
2. 选择国家/地区
3. 同意服务条款
4. 验证手机号码
5. 添加信用卡信息（仅验证，不扣费）
6. 获得$300免费试用额度
```

### 3. 免费额度说明
- 💰 $300试用额度（12个月有效）
- 🎯 Cloud Run永久免费额度：
  - 200万请求/月
  - 18万vCPU秒/月
  - 36万GiB秒内存/月
  - 1GB出站流量/月

## 🏗️ 第二步：创建项目

### 1. 在控制台创建项目
- 访问：https://console.cloud.google.com/
- 点击项目选择器
- 点击"新建项目"
- 项目名称：`gemini-balance`
- 项目ID：`gemini-balance-xxxxx`（自动生成）

### 2. 启用必要的API
```bash
# 方法1：通过控制台
1. 访问：https://console.cloud.google.com/apis/library
2. 搜索并启用以下API：
   - Cloud Run API
   - Cloud Build API
   - Container Registry API

# 方法2：通过命令行
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

## 🚀 第三步：部署应用

### 方法1：使用我们的一键脚本（推荐）

#### 1. 安装Google Cloud SDK

**Windows:**
```cmd
# 下载安装包
https://cloud.google.com/sdk/docs/install-sdk#windows

# 或使用Chocolatey
choco install gcloudsdk
```

**macOS:**
```bash
# 使用Homebrew
brew install google-cloud-sdk

# 或下载安装包
curl https://sdk.cloud.google.com | bash
```

**Linux:**
```bash
# Ubuntu/Debian
echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
sudo apt-get update && sudo apt-get install google-cloud-cli

# CentOS/RHEL
sudo tee -a /etc/yum.repos.d/google-cloud-sdk.repo << EOM
[google-cloud-cli]
name=Google Cloud CLI
baseurl=https://packages.cloud.google.com/yum/repos/cloud-sdk-el8-x86_64
enabled=1
gpgcheck=1
repo_gpgcheck=0
gpgkey=https://packages.cloud.google.com/yum/doc/yum-key.gpg
       https://packages.cloud.google.com/yum/doc/rpm-package-key.gpg
EOM
sudo dnf install google-cloud-cli
```

#### 2. 认证和配置
```bash
# 登录Google Cloud
gcloud auth login

# 设置默认项目
gcloud config set project YOUR_PROJECT_ID

# 配置Docker认证
gcloud auth configure-docker
```

#### 3. 运行部署脚本
```bash
# 给脚本执行权限
chmod +x deploy-gcp.sh

# 运行部署脚本
./deploy-gcp.sh

# 按提示输入：
# - 项目ID
# - 选择数据库类型（推荐选择1-SQLite）
# - 确认部署区域（推荐asia-east1香港）
```

### 方法2：通过Web控制台部署

#### 1. 访问Cloud Run控制台
- 网址：https://console.cloud.google.com/run
- 选择您的项目

#### 2. 创建服务
```
1. 点击"创建服务"
2. 选择"从源代码持续部署"
3. 设置源代码仓库：
   - 连接到GitHub
   - 选择您的仓库
   - 分支：main
4. 构建配置：
   - 构建类型：Dockerfile
   - Dockerfile路径：/Dockerfile
```

#### 3. 配置服务
```
服务设置：
- 服务名称：gemini-balance
- 区域：asia-east1（香港）
- CPU分配：仅在处理请求期间分配CPU

容器设置：
- 容器端口：8001
- 内存：1 GiB
- CPU：1
- 最大实例数：10
- 并发：100

环境变量：
- PORT=8001
- DATABASE_URL=sqlite:///./data/gemini_balance.db
- LOG_LEVEL=INFO
```

#### 4. 部署
- 点击"创建"
- 等待构建和部署完成（约5-10分钟）

### 方法3：命令行部署

#### 1. 准备Dockerfile
确保项目根目录有Dockerfile文件（已创建）

#### 2. 构建镜像
```bash
# 构建并推送到Container Registry
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/gemini-balance
```

#### 3. 部署服务
```bash
gcloud run deploy gemini-balance \
  --image gcr.io/YOUR_PROJECT_ID/gemini-balance \
  --platform managed \
  --region asia-east1 \
  --allow-unauthenticated \
  --port 8001 \
  --memory 1Gi \
  --cpu 1 \
  --max-instances 10 \
  --set-env-vars "PORT=8001,DATABASE_URL=sqlite:///./data/gemini_balance.db,LOG_LEVEL=INFO"
```

## ✅ 第四步：验证部署

### 1. 获取服务URL
```bash
# 通过命令行获取
gcloud run services describe gemini-balance \
  --region asia-east1 \
  --format "value(status.url)"

# 或在控制台查看
https://console.cloud.google.com/run
```

### 2. 测试服务
```bash
# 测试主页
curl https://your-service-url.run.app/

# 测试API文档
curl https://your-service-url.run.app/docs

# 测试优化功能
curl https://your-service-url.run.app/connection-pool/health
curl https://your-service-url.run.app/cache/enhanced/analytics
curl https://your-service-url.run.app/routing/health
```

### 3. 查看日志
```bash
# 查看实时日志
gcloud run logs tail gemini-balance --region asia-east1

# 在控制台查看
https://console.cloud.google.com/run/detail/asia-east1/gemini-balance/logs
```

## 🌍 第五步：配置Cloudflare（可选）

### 1. 添加域名到Cloudflare
- 注册Cloudflare账户：https://cloudflare.com/
- 添加您的域名
- 更新域名服务器

### 2. 配置DNS
```
类型：CNAME
名称：api（或@用于根域名）
目标：your-service-url.run.app
代理状态：已代理（橙色云朵）
```

### 3. 优化设置
```
SSL/TLS：Full (strict)
缓存级别：Standard
浏览器缓存TTL：4小时
边缘缓存TTL：2小时
```

## 📊 第六步：监控和管理

### 1. 设置预算告警
```bash
# 创建预算
gcloud billing budgets create \
  --billing-account YOUR_BILLING_ACCOUNT \
  --display-name "Gemini Balance Budget" \
  --budget-amount 10USD \
  --threshold-rules-percent 50,90,100
```

### 2. 监控指标
- 访问：https://console.cloud.google.com/monitoring
- 查看CPU、内存、请求数等指标

### 3. 管理命令
```bash
# 查看服务状态
gcloud run services list

# 更新服务
gcloud run deploy gemini-balance --image gcr.io/PROJECT/gemini-balance:latest

# 删除服务
gcloud run services delete gemini-balance --region asia-east1

# 查看服务详情
gcloud run services describe gemini-balance --region asia-east1
```

## 🎉 完成！

您的Gemini Balance应用现在已经部署到Google Cloud Run！

**访问地址：** https://your-service-url.run.app/
**API文档：** https://your-service-url.run.app/docs
**管理控制台：** https://console.cloud.google.com/run
