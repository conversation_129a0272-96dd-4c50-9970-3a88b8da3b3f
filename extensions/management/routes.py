"""
管理中心路由 - 擴展模塊
獨立的路由定義，與核心項目解耦
"""

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
import os
from datetime import datetime

from app.core.security import verify_auth_token
from app.log.logger import get_logger

# 創建路由器
router = APIRouter(prefix="/web", tags=["management-extension"])

# 設置模板目錄 - 使用主應用的模板目錄
templates_dir = "app/templates"
templates = Jinja2Templates(directory=templates_dir)

logger = get_logger(__name__)


@router.get("/security", response_class=HTMLResponse)
async def security_management_page(request: Request):
    """安全管理頁面 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to security management page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("Security management page accessed successfully")
        return templates.TemplateResponse("security_management.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing security management page: {str(e)}")
        raise

@router.get("/security-management", response_class=HTMLResponse)
async def security_management_page_alt(request: Request):
    """安全管理頁面 - 替代路径"""
    return await security_management_page(request)


@router.get("/cache", response_class=HTMLResponse)
async def cache_management_page(request: Request):
    """緩存管理頁面 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to cache management page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("Cache management page accessed successfully")
        return templates.TemplateResponse("cache_management.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing cache management page: {str(e)}")
        raise

@router.get("/cache-management", response_class=HTMLResponse)
async def cache_management_page_alt(request: Request):
    """緩存管理頁面 - 替代路径"""
    return await cache_management_page(request)


@router.get("/routing", response_class=HTMLResponse)
async def routing_management_page(request: Request):
    """路由管理頁面 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to routing management page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("Routing management page accessed successfully")
        return templates.TemplateResponse("routing_management.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing routing management page: {str(e)}")
        raise


@router.get("/analytics", response_class=HTMLResponse)
async def analytics_center_page(request: Request):
    """分析报告中心页面 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to analytics center page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("Analytics center page accessed successfully")
        return templates.TemplateResponse("analytics_center.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing analytics center page: {str(e)}")
        raise


# Web API端點 (使用cookie認證)
@router.get("/api/groups")
async def web_get_groups(request: Request):
    """Web界面獲取用戶組列表 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to web groups API")
            raise HTTPException(status_code=401, detail="Unauthorized")

        # 導入必要的服務
        from app.service.admin.user_group_service import UserGroupService
        user_group_service = UserGroupService()

        # 獲取用戶組列表
        result = await user_group_service.list_user_groups(status=None, limit=100, offset=0)
        logger.info("Web groups API accessed successfully")
        return result
    except Exception as e:
        logger.error(f"Error in web groups API: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/api/analytics/comprehensive")
async def get_comprehensive_analytics(
    request: Request,
    group_id: str = None,
    period: str = "24h"
):
    """获取综合分析数据 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to analytics API")
            raise HTTPException(status_code=401, detail="Unauthorized")

        # 导入分析聚合服务
        from app.service.analytics.analytics_aggregation_service import analytics_aggregation_service

        # 获取综合分析数据
        analytics = await analytics_aggregation_service.get_comprehensive_analytics(group_id, period)

        # 转换为字典格式
        result = {
            "overview": analytics.overview,
            "usage_trends": analytics.usage_trends,
            "cost_analysis": analytics.cost_analysis,
            "performance_metrics": analytics.performance_metrics,
            "user_group_analysis": analytics.user_group_analysis,
            "recommendations": analytics.recommendations,
            "timestamp": analytics.timestamp.isoformat()
        }

        logger.info(f"Analytics API accessed successfully for group: {group_id}, period: {period}")
        return result

    except Exception as e:
        logger.error(f"Error in analytics API: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/api/analytics/export")
async def export_analytics_report(
    request: Request,
    group_id: str = None,
    period: str = "24h",
    format: str = "json"
):
    """导出分析报告 - 擴展功能"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to analytics export API")
            raise HTTPException(status_code=401, detail="Unauthorized")

        # 导入分析聚合服务
        from app.service.analytics.analytics_aggregation_service import analytics_aggregation_service

        # 获取分析数据
        analytics = await analytics_aggregation_service.get_comprehensive_analytics(group_id, period)

        if format.lower() == "csv":
            # 生成CSV格式（简化版）
            from fastapi.responses import StreamingResponse
            import io
            import csv

            output = io.StringIO()
            writer = csv.writer(output)

            # 写入概览数据
            writer.writerow(["Metric", "Value"])
            for key, value in analytics.overview.items():
                writer.writerow([key, value])

            output.seek(0)
            return StreamingResponse(
                io.BytesIO(output.getvalue().encode()),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=analytics_report_{period}.csv"}
            )
        else:
            # 默认JSON格式
            result = {
                "overview": analytics.overview,
                "usage_trends": analytics.usage_trends,
                "cost_analysis": analytics.cost_analysis,
                "performance_metrics": analytics.performance_metrics,
                "user_group_analysis": analytics.user_group_analysis,
                "recommendations": analytics.recommendations,
                "timestamp": analytics.timestamp.isoformat(),
                "export_format": format,
                "export_time": datetime.now().isoformat()
            }

            logger.info(f"Analytics export API accessed successfully for group: {group_id}, period: {period}, format: {format}")
            return result

    except Exception as e:
        logger.error(f"Error in analytics export API: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
