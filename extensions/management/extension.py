"""
管理中心擴展 - 主要擴展類
提供插件化的管理功能
"""

from fastapi import FastAPI
from .routes import router
import os


class ManagementExtension:
    """管理中心擴展類"""
    
    def __init__(self):
        self.name = "management_center"
        self.version = "1.0.0"
        self.description = "安全管理和緩存管理中心"
        
    def register(self, app: FastAPI):
        """註冊擴展到FastAPI應用"""
        try:
            # 註冊路由
            app.include_router(router)
            
            # 添加靜態文件路由（如果需要）
            static_dir = os.path.join(os.path.dirname(__file__), "static")
            if os.path.exists(static_dir):
                from fastapi.staticfiles import StaticFiles
                app.mount("/extensions/management/static", StaticFiles(directory=static_dir), name="management_static")
            
            print(f"✅ 管理中心擴展 v{self.version} 已成功註冊")
            return True
            
        except Exception as e:
            print(f"❌ 管理中心擴展註冊失敗: {str(e)}")
            return False
    
    def get_navigation_items(self):
        """獲取導航項目，用於主界面集成"""
        return [
            {
                "name": "安全策略中心",
                "url": "/web/security",
                "icon": "fas fa-shield-alt",
                "color": "red",
                "description": "高级安全策略、IP管理、威胁防护"
            },
            {
                "name": "缓存优化中心",
                "url": "/web/cache",
                "icon": "fas fa-database",
                "color": "blue",
                "description": "缓存策略配置、预热管理、性能调优"
            },
            {
                "name": "路由策略中心",
                "url": "/web/routing",
                "icon": "fas fa-route",
                "color": "green",
                "description": "负载均衡策略、端点管理、故障转移"
            },
            {
                "name": "分析报告中心",
                "url": "/web/analytics",
                "icon": "fas fa-chart-bar",
                "color": "purple",
                "description": "综合数据分析、智能报告、成本优化"
            },
            {
                "name": "系统设置中心",
                "url": "/web/settings", 
                "icon": "fas fa-cogs",
                "color": "gray",
                "description": "上下文隔离、系统参数、集成配置"
            }
        ]


# 創建擴展實例
management_extension = ManagementExtension()
