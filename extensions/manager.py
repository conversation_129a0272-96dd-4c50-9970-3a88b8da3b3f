"""
擴展管理器
負責加載和管理所有擴展模塊
"""

from fastapi import FastAPI
from typing import List, Dict, Any
import importlib
import os


class ExtensionManager:
    """擴展管理器"""
    
    def __init__(self):
        self.extensions = {}
        self.navigation_items = []
        
    def load_extensions(self, app: FastAPI):
        """加載所有擴展"""
        try:
            # 加載管理中心擴展
            from .management.extension import management_extension
            
            if management_extension.register(app):
                self.extensions[management_extension.name] = management_extension
                self.navigation_items.extend(management_extension.get_navigation_items())
                
            print(f"✅ 已加載 {len(self.extensions)} 個擴展")
            
        except Exception as e:
            print(f"❌ 擴展加載失敗: {str(e)}")
    
    def get_navigation_items(self) -> List[Dict[str, Any]]:
        """獲取所有擴展的導航項目"""
        return self.navigation_items
    
    def get_extension(self, name: str):
        """獲取指定擴展"""
        return self.extensions.get(name)
    
    def list_extensions(self) -> Dict[str, Any]:
        """列出所有已加載的擴展"""
        return {
            name: {
                "version": ext.version,
                "description": ext.description
            }
            for name, ext in self.extensions.items()
        }


# 創建全局擴展管理器實例
extension_manager = ExtensionManager()
