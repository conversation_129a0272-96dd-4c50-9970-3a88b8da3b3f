# Gemini Balance 擴展模塊

## 概述

本擴展模塊為 Gemini Balance 項目提供專業化管理工具，與核心項目完全解耦，可獨立升級和維護。

## 解耦策略

### 1. 功能分工明確

#### 核心系統（原作者維護）
- **統一監控中心** (`/web/monitoring`): 系統、API、性能全方位監控
- **基礎配置中心** (`/config`): API密鑰、模型、基礎參數設置  
- **用戶組管理** (`/web/user-groups`): 創建和管理用戶組權限

#### 擴展工具（獨立維護）
- **安全策略中心** (`/web/security`): 高級安全策略、IP管理、威脅防護
- **緩存優化中心** (`/web/cache`): 緩存策略配置、預熱管理、性能調優
- **路由策略中心** (`/web/routing`): 負載均衡策略、端點管理、故障轉移

### 2. 無功能重複

- **監控功能**: 所有監控數據統一在原生監控中心展示，擴展工具不重複實現監控界面
- **配置功能**: 基礎配置在原生配置中心，擴展工具只提供專業化配置
- **管理功能**: 擴展工具專注於特定領域的深度管理，不與核心功能重疊

### 3. 技術解耦

#### 目錄結構
```
extensions/                 # 擴展模塊根目錄
├── __init__.py            # 擴展模塊初始化
├── manager.py             # 擴展管理器
├── README.md              # 本文檔
└── management/            # 管理中心擴展
    ├── __init__.py
    ├── extension.py       # 擴展主類
    ├── routes.py          # 路由定義
    └── templates/         # 模板文件（可選）
```

#### 集成方式
- 使用插件化架構，通過 `ExtensionManager` 動態加載
- 擴展失敗不影響核心功能運行
- 可以完全刪除 `extensions/` 目錄而不影響核心系統

#### 模板復用
- 擴展使用主應用的模板目錄 (`app/templates`)
- 避免模板文件重複，保持樣式一致性

## 升級兼容性

### 原作者更新時
1. 更新 `app/` 目錄下的核心文件
2. 保持 `extensions/` 目錄不變
3. 只需確保 `app/core/application.py` 中的擴展加載代碼保持不變

### 擴展更新時
1. 只更新 `extensions/` 目錄
2. 不影響核心功能
3. 可以獨立發布和維護

## 使用說明

### 安裝擴展
1. 將 `extensions/` 目錄放置在項目根目錄
2. 重啟服務器，擴展會自動加載

### 卸載擴展
1. 刪除 `extensions/` 目錄
2. 重啟服務器，系統恢復到原生狀態

### 開發新擴展
1. 在 `extensions/` 下創建新的子模塊
2. 實現擴展類，繼承基本接口
3. 在 `manager.py` 中註冊新擴展

## 版本信息

- 擴展版本: 1.0.0
- 兼容的 Gemini Balance 版本: 2.2.1+
- 最後更新: 2025-07-21

## 注意事項

1. **不要修改核心文件**: 擴展功能應完全在 `extensions/` 目錄中實現
2. **避免功能重複**: 新擴展應專注於特定領域，不與核心功能重疊
3. **保持向後兼容**: 擴展更新時應考慮向後兼容性
4. **測試獨立性**: 確保擴展可以獨立安裝和卸載
