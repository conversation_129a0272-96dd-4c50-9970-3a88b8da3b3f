# Gemini Balance 用户使用指南

## 概述

Gemini Balance 提供了一套完整的管理界面，包括核心功能和专业化管理工具。本指南将帮助您快速上手并充分利用所有功能。

## 访问管理界面

1. 启动 Gemini Balance 服务
2. 在浏览器中访问 `http://localhost:8001`
3. 使用配置的认证令牌登录
4. 进入管理控制台

## 核心功能

### 1. 统一监控中心
- **路径**: `/web/monitoring`
- **功能**: 系统、API、性能全方位监控
- **特点**: 实时数据展示、图表可视化、告警通知

### 2. 用户组管理
- **路径**: `/web/user-groups`
- **功能**: 用户组创建、配额管理、权限控制
- **操作**: 
  - 创建新用户组
  - 设置API配额限制
  - 管理用户组成员

### 3. 配置中心
- **路径**: `/config`
- **功能**: 系统基础配置管理
- **包含**: API密钥、模型配置、代理设置等

## 专业化管理工具

### 1. 安全策略中心
- **路径**: `/web/security`
- **功能**: 
  - IP白名单/黑名单管理
  - 访问频率限制
  - 威胁检测和防护
  - 安全审计日志
- **使用场景**: 
  - 防止恶意访问
  - 控制API访问权限
  - 监控安全事件

### 2. 缓存优化中心
- **路径**: `/web/cache`
- **功能**:
  - 缓存策略配置
  - 缓存预热管理
  - 性能调优设置
  - 缓存统计分析
- **使用场景**:
  - 提升API响应速度
  - 减少重复请求
  - 优化系统性能

### 3. 路由策略中心
- **路径**: `/web/routing`
- **功能**:
  - 负载均衡策略配置
  - API端点管理
  - 故障转移设置
  - 路由健康监控
- **使用场景**:
  - 分布式部署管理
  - 高可用性配置
  - 流量分发优化

### 4. 分析报告中心
- **路径**: `/web/analytics`
- **功能**:
  - 综合数据分析
  - 使用趋势报告
  - 成本分析优化
  - 智能建议生成
- **使用场景**:
  - 了解API使用情况
  - 成本控制和优化
  - 性能瓶颈分析

### 5. 系统设置中心
- **路径**: `/web/settings`
- **功能**:
  - 上下文隔离配置
  - 高级系统参数
  - 外部服务集成
  - 安全选项设置
- **使用场景**:
  - 高级功能配置
  - 系统性能调优
  - 集成第三方服务

## 常用操作流程

### 初始设置
1. 访问配置中心，设置基础API密钥
2. 在用户组管理中创建用户组
3. 在安全策略中心配置访问控制
4. 在缓存优化中心启用缓存策略

### 日常监控
1. 查看统一监控中心的实时数据
2. 检查分析报告中心的使用趋势
3. 关注安全策略中心的安全事件
4. 监控路由策略中心的端点健康状态

### 性能优化
1. 在分析报告中心识别性能瓶颈
2. 在缓存优化中心调整缓存策略
3. 在路由策略中心优化负载均衡
4. 在系统设置中心调整高级参数

### 故障排查
1. 查看统一监控中心的错误日志
2. 检查路由策略中心的端点状态
3. 分析安全策略中心的访问记录
4. 查看分析报告中心的异常趋势

## 最佳实践

### 安全配置
- 定期更新API密钥
- 设置合理的IP访问限制
- 启用访问频率控制
- 定期查看安全审计日志

### 性能优化
- 根据使用模式配置缓存策略
- 设置合适的负载均衡算法
- 监控并调整系统参数
- 定期清理过期数据

### 监控告警
- 设置关键指标的告警阈值
- 配置通知渠道
- 定期查看分析报告
- 关注异常趋势

### 成本控制
- 设置用户组配额限制
- 监控API使用成本
- 优化模型选择策略
- 定期评估使用效率

## 故障排除

### 常见问题
1. **页面无法访问**: 检查服务是否正常启动，端口是否被占用
2. **认证失败**: 确认认证令牌配置正确
3. **API调用失败**: 检查API密钥是否有效，网络连接是否正常
4. **性能问题**: 查看监控数据，检查缓存配置

### 日志查看
- 系统日志: `/error_logs`
- API调用日志: 监控中心
- 安全事件日志: 安全策略中心
- 性能分析日志: 分析报告中心

## 技术支持

如果您在使用过程中遇到问题，可以：
1. 查看本用户指南
2. 检查系统日志和错误信息
3. 参考项目文档和FAQ
4. 在GitHub项目页面提交Issue

## 更新说明

本管理界面会随着项目更新而持续改进，建议定期关注项目更新，获取最新功能和修复。
