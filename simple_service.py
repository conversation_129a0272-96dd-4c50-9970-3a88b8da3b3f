#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的 Gemini Balance Windows 服务
"""

import os
import sys
import time
import subprocess
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
except ImportError:
    print("错误: 需要安装 pywin32 库")
    print("请运行: pip install pywin32")
    sys.exit(1)


class SimpleGeminiService(win32serviceutil.ServiceFramework):
    """简化的 Gemini Balance Windows 服务类"""
    
    _svc_name_ = "GeminiBalanceSimple"
    _svc_display_name_ = "Gemini Balance API Service (Simple)"
    _svc_description_ = "Gemini API 代理和负载均衡服务 (简化版)"
    
    def __init__(self, args):
        """初始化服务"""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True
        self.process = None
        
        # 设置工作目录
        os.chdir(str(PROJECT_ROOT))
        
    def SvcStop(self):
        """停止服务"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False
        
        # 终止子进程
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
            except:
                try:
                    self.process.kill()
                except:
                    pass
                    
    def SvcDoRun(self):
        """运行服务"""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        try:
            self.main()
        except Exception as e:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_ERROR_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, str(e))
            )
            
    def main(self):
        """主服务逻辑"""
        while self.is_alive:
            try:
                # 启动应用程序
                cmd = [sys.executable, "start.py"]
                
                self.process = subprocess.Popen(
                    cmd,
                    cwd=str(PROJECT_ROOT),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                # 监控进程
                while self.is_alive and self.process.poll() is None:
                    result = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                    if result == win32event.WAIT_OBJECT_0:
                        break
                        
                # 如果进程意外退出且服务仍在运行，重启
                if self.process.poll() is not None and self.is_alive:
                    time.sleep(5)  # 等待5秒后重启
                    
            except Exception as e:
                servicemanager.LogMsg(
                    servicemanager.EVENTLOG_ERROR_TYPE,
                    0,
                    f"服务运行错误: {e}"
                )
                if self.is_alive:
                    time.sleep(10)


if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 作为服务运行
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(SimpleGeminiService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 命令行操作
        win32serviceutil.HandleCommandLine(SimpleGeminiService)
