# 🚀 Gemini Balance 自动启动指南

本指南将帮助你设置 Cursor/VSCode 在打开项目时自动启动 Gemini Balance 开发服务器。

## 📋 功能特性

- ✅ 打开 Cursor 时自动启动开发服务器
- ✅ 自动检查和安装依赖
- ✅ 智能环境检测和配置
- ✅ 多种启动方式支持
- ✅ 完整的调试配置
- ✅ 推荐扩展自动安装

## 🎯 使用方法

### 方法 1: 使用工作区文件 (推荐)

1. **打开工作区文件**
   ```bash
   # 双击打开工作区文件
   gemini-balance.code-workspace
   ```

2. **自动启动**
   - 工作区打开后会自动运行启动任务
   - 服务器将在 `http://localhost:8001` 启动

### 方法 2: 使用 VSCode 任务

1. **打开命令面板** (`Ctrl+Shift+P`)
2. **运行任务** 输入 `Tasks: Run Task`
3. **选择任务** `🚀 自动启动开发服务器`

### 方法 3: 使用调试配置

1. **打开调试面板** (`Ctrl+Shift+D`)
2. **选择配置** `🚀 启动 Gemini Balance (开发模式)`
3. **开始调试** (`F5`)

### 方法 4: 使用脚本文件

#### Windows 批处理脚本
```bash
# 双击运行
auto-start.bat
```

#### PowerShell 脚本
```powershell
# 在 PowerShell 中运行
.\auto-start.ps1
```

## ⚙️ 配置说明

### 自动启动任务配置

任务配置在 `.vscode/tasks.json` 中，关键配置：

```json
{
    "runOptions": {
        "runOn": "folderOpen"  // 文件夹打开时自动运行
    }
}
```

### 调试配置

调试配置在 `.vscode/launch.json` 中，包含多个预设：

- 🚀 **启动 Gemini Balance (开发模式)** - 标准开发启动
- 🐛 **调试 FastAPI 应用** - 详细调试模式
- 🧪 **运行测试** - 执行项目测试
- ⚙️ **项目设置** - 初始化项目
- 🏥 **健康检查** - 服务健康检查

### 工作区设置

工作区配置在 `gemini-balance.code-workspace` 中，包含：

- Python 解释器路径
- 自动保存设置
- 代码格式化配置
- 文件关联设置
- 推荐扩展列表

## 🔧 自定义配置

### 修改启动端口

在 `.vscode/tasks.json` 或 `launch.json` 中修改 `--port` 参数：

```json
"args": ["--port", "8080"]  // 改为你想要的端口
```

### 修改启动主机

```json
"args": ["--host", "127.0.0.1"]  // 仅本地访问
```

### 禁用自动启动

在 `.vscode/tasks.json` 中移除或注释掉：

```json
// "runOptions": {
//     "runOn": "folderOpen"
// }
```

## 📦 推荐扩展

项目会自动推荐安装以下扩展：

- **Python 开发**
  - Python
  - Pylance
  - Black Formatter
  - Pylint
  - Flake8

- **Web 开发**
  - JSON
  - YAML
  - TypeScript
  - Tailwind CSS
  - Prettier
  - ESLint

- **工具扩展**
  - Docker
  - Remote Containers
  - GitHub Copilot
  - REST Client
  - Thunder Client

## 🚨 故障排除

### 问题 1: 自动启动失败

**解决方案:**
1. 检查 Python 是否正确安装
2. 确保 `requirements.txt` 存在
3. 检查终端权限设置

### 问题 2: 端口被占用

**解决方案:**
1. 修改启动端口配置
2. 或者停止占用端口的进程：
   ```bash
   # Windows
   netstat -ano | findstr :8001
   taskkill /PID <PID> /F
   
   # Linux/Mac
   lsof -ti:8001 | xargs kill -9
   ```

### 问题 3: 依赖安装失败

**解决方案:**
1. 手动安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
2. 检查网络连接
3. 使用国内镜像源：
   ```bash
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

### 问题 4: PowerShell 执行策略限制

**解决方案:**
```powershell
# 临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 🎉 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd gemini-balance
   ```

2. **打开工作区**
   ```bash
   code gemini-balance.code-workspace
   ```

3. **等待自动启动**
   - 项目会自动检查环境
   - 安装依赖
   - 启动开发服务器

4. **访问应用**
   - 主页: http://localhost:8001
   - API 文档: http://localhost:8001/docs
   - 管理面板: http://localhost:8001/admin

## 📞 支持

如果遇到问题，请：

1. 查看终端输出的错误信息
2. 检查 `logs/` 目录下的日志文件
3. 运行健康检查: `python start.py --health`
4. 提交 Issue 到项目仓库

---

🎯 **提示**: 首次使用建议先运行 `python start.py --setup` 进行完整的项目初始化。
