#!/bin/bash

# Google Cloud Platform 部署脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
PROJECT_ID=""
SERVICE_NAME="gemini-balance"
REGION="asia-east1"  # 香港区域，对中国大陆友好
DATABASE_TYPE="sqlite"  # sqlite, mysql, postgresql

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "Google Cloud SDK 未安装"
        log_info "请访问: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 设置项目
setup_project() {
    log_info "设置Google Cloud项目..."
    
    if [ -z "$PROJECT_ID" ]; then
        echo "请输入您的Google Cloud项目ID:"
        read -r PROJECT_ID
    fi
    
    gcloud config set project "$PROJECT_ID"
    gcloud auth configure-docker
    
    # 启用必要的API
    log_info "启用必要的API服务..."
    gcloud services enable run.googleapis.com
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable sql-component.googleapis.com
    
    log_success "项目设置完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    echo "选择数据库类型:"
    echo "1) SQLite (推荐，免费)"
    echo "2) Cloud SQL MySQL"
    echo "3) Cloud SQL PostgreSQL"
    echo "4) Firestore"
    read -p "请选择 (1-4): " db_choice
    
    case $db_choice in
        1)
            DATABASE_TYPE="sqlite"
            DATABASE_URL="sqlite:///./data/gemini_balance.db"
            log_success "使用SQLite数据库"
            ;;
        2)
            DATABASE_TYPE="mysql"
            setup_cloud_sql "mysql"
            ;;
        3)
            DATABASE_TYPE="postgresql"
            setup_cloud_sql "postgresql"
            ;;
        4)
            DATABASE_TYPE="firestore"
            setup_firestore
            ;;
        *)
            log_warning "无效选择，使用默认SQLite"
            DATABASE_TYPE="sqlite"
            DATABASE_URL="sqlite:///./data/gemini_balance.db"
            ;;
    esac
}

# 设置Cloud SQL
setup_cloud_sql() {
    local db_type=$1
    local instance_name="${SERVICE_NAME}-db"
    
    log_info "创建Cloud SQL实例..."
    
    # 创建实例
    gcloud sql instances create "$instance_name" \
        --database-version="${db_type^^}_8_0" \
        --tier=db-f1-micro \
        --region="$REGION" \
        --storage-type=SSD \
        --storage-size=10GB
    
    # 创建数据库
    gcloud sql databases create gemini_balance --instance="$instance_name"
    
    # 创建用户
    gcloud sql users create gemini_user \
        --instance="$instance_name" \
        --password="$(openssl rand -base64 32)"
    
    # 获取连接信息
    CONNECTION_NAME=$(gcloud sql instances describe "$instance_name" --format="value(connectionName)")
    DATABASE_URL="${db_type}+aiomysql://gemini_user:password@/$instance_name/gemini_balance?unix_socket=/cloudsql/$CONNECTION_NAME"
    
    log_success "Cloud SQL设置完成"
}

# 设置Firestore
setup_firestore() {
    log_info "设置Firestore..."
    
    gcloud firestore databases create --region="$REGION"
    DATABASE_URL="firestore://$PROJECT_ID"
    
    log_success "Firestore设置完成"
}

# 创建环境配置
create_env_config() {
    log_info "创建环境配置..."
    
    cat > .env.production << EOF
# 生产环境配置
PORT=8080
LOG_LEVEL=INFO
DEBUG=false

# 数据库配置
DATABASE_URL=$DATABASE_URL

# Google Cloud配置
GOOGLE_CLOUD_PROJECT=$PROJECT_ID

# API配置
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com
OPENAI_API_BASE_URL=https://api.openai.com/v1

# 安全配置
SECRET_KEY=$(openssl rand -hex 32)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 连接池配置
CONNECTION_POOL_SIZE=50
CONNECTION_TIMEOUT=30

# 监控配置
ENABLE_METRICS=true
EOF
    
    log_success "环境配置创建完成"
}

# 创建Cloud Run配置
create_cloudrun_config() {
    log_info "创建Cloud Run配置..."
    
    cat > cloudrun.yaml << EOF
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: $SERVICE_NAME
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - image: gcr.io/$PROJECT_ID/$SERVICE_NAME
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: DATABASE_URL
          value: "$DATABASE_URL"
        - name: GOOGLE_CLOUD_PROJECT
          value: "$PROJECT_ID"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "0.5"
            memory: "512Mi"
EOF
    
    log_success "Cloud Run配置创建完成"
}

# 构建和部署
deploy_to_cloudrun() {
    log_info "构建Docker镜像..."
    
    # 构建镜像
    gcloud builds submit --tag gcr.io/"$PROJECT_ID"/"$SERVICE_NAME"
    
    log_info "部署到Cloud Run..."
    
    # 部署服务
    gcloud run deploy "$SERVICE_NAME" \
        --image gcr.io/"$PROJECT_ID"/"$SERVICE_NAME" \
        --platform managed \
        --region "$REGION" \
        --allow-unauthenticated \
        --port 8080 \
        --memory 1Gi \
        --cpu 1 \
        --max-instances 10 \
        --set-env-vars "DATABASE_URL=$DATABASE_URL,GOOGLE_CLOUD_PROJECT=$PROJECT_ID"
    
    # 获取服务URL
    SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)")
    
    log_success "部署完成！"
    log_info "服务URL: $SERVICE_URL"
}

# 配置Cloudflare
setup_cloudflare() {
    log_info "Cloudflare配置建议..."
    
    echo "为了优化中国大陆访问，建议配置Cloudflare:"
    echo "1. 将域名DNS托管到Cloudflare"
    echo "2. 添加CNAME记录指向: ${SERVICE_URL#https://}"
    echo "3. 启用以下优化:"
    echo "   - Auto Minify (CSS, JS, HTML)"
    echo "   - Brotli Compression"
    echo "   - Rocket Loader"
    echo "   - Polish (图片优化)"
    echo "4. 设置缓存规则:"
    echo "   - /static/* 缓存1年"
    echo "   - /api/* 缓存5分钟"
    echo ""
    echo "推荐Cloudflare设置:"
    echo "- SSL/TLS: Full (strict)"
    echo "- Security Level: Medium"
    echo "- Browser Cache TTL: 4 hours"
    echo "- Edge Cache TTL: 2 hours"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    if curl -f "$SERVICE_URL" > /dev/null 2>&1; then
        log_success "服务健康检查通过"
    else
        log_error "服务健康检查失败"
        return 1
    fi
    
    # 检查各个组件
    endpoints=(
        "/connection-pool/health"
        "/cache/enhanced/analytics"
        "/routing/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f "$SERVICE_URL$endpoint" > /dev/null 2>&1; then
            log_success "组件检查通过: $endpoint"
        else
            log_warning "组件检查失败: $endpoint"
        fi
    done
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 Google Cloud 部署完成！"
    echo "================================"
    echo "项目ID: $PROJECT_ID"
    echo "服务名称: $SERVICE_NAME"
    echo "区域: $REGION"
    echo "数据库类型: $DATABASE_TYPE"
    echo "服务URL: $SERVICE_URL"
    echo ""
    echo "📊 监控地址:"
    echo "- 主服务: $SERVICE_URL"
    echo "- API文档: $SERVICE_URL/docs"
    echo "- 连接池: $SERVICE_URL/connection-pool/health"
    echo "- 缓存分析: $SERVICE_URL/cache/enhanced/analytics"
    echo "- 智能路由: $SERVICE_URL/routing/health"
    echo ""
    echo "🔧 管理命令:"
    echo "- 查看日志: gcloud run logs tail $SERVICE_NAME --region=$REGION"
    echo "- 更新服务: gcloud run deploy $SERVICE_NAME --region=$REGION"
    echo "- 删除服务: gcloud run services delete $SERVICE_NAME --region=$REGION"
    echo ""
    echo "💰 成本估算 (免费额度内):"
    echo "- Cloud Run: 200万请求/月免费"
    echo "- Cloud Storage: 5GB免费"
    echo "- 出站流量: 1GB/月免费"
    echo ""
    setup_cloudflare
}

# 主函数
main() {
    echo "🚀 Google Cloud Platform 部署脚本"
    echo "=================================="
    
    check_dependencies
    setup_project
    setup_database
    create_env_config
    create_cloudrun_config
    deploy_to_cloudrun
    health_check
    show_deployment_info
}

# 执行主函数
main "$@"
