@echo off
chcp 65001 >nul
title 安装 Gemini Balance 服务 (管理员)

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 切换到脚本目录
cd /d "%~dp0"

echo.
echo ========================================
echo    Gemini Balance 服务安装 (管理员)
echo ========================================
echo.

echo [1/6] 检查管理员权限...
echo ✅ 管理员权限确认

echo.
echo [2/6] 检查 Python 环境...
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" --version
if %errorlevel% neq 0 (
    echo ❌ Python 未找到
    pause
    exit /b 1
)
echo ✅ Python 环境正常

echo.
echo [3/6] 停止现有服务...
sc stop GeminiBalance >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo [4/6] 卸载现有服务...
sc delete GeminiBalance >nul 2>&1

echo.
echo [5/6] 安装新服务...
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" gemini_balance_service.py install

if %errorlevel% equ 0 (
    echo ✅ 服务安装成功
    
    echo.
    echo [6/6] 配置服务...
    
    :: 设置自动启动
    sc config GeminiBalance start= auto
    echo ✅ 设置自动启动
    
    :: 设置服务描述
    sc description GeminiBalance "Gemini API 代理和负载均衡服务"
    
    :: 设置服务恢复策略
    sc failure GeminiBalance reset= 86400 actions= restart/5000/restart/5000/restart/5000
    echo ✅ 设置故障恢复策略
    
    echo.
    echo ========================================
    echo           🎉 安装完成！
    echo ========================================
    echo.
    echo 服务信息:
    echo   服务名称: GeminiBalance
    echo   显示名称: Gemini Balance API Service
    echo   启动类型: 自动
    echo   故障恢复: 自动重启
    echo.
    
    :: 询问是否立即启动
    set /p start_now=是否立即启动服务？(Y/N): 
    if /i "%start_now%"=="Y" (
        echo.
        echo 🚀 正在启动服务...
        sc start GeminiBalance
        
        if %errorlevel% equ 0 (
            echo ✅ 服务启动命令已发送
            echo.
            echo ⏳ 等待服务启动...
            timeout /t 10 /nobreak >nul
            
            :: 检查服务状态
            sc query GeminiBalance | findstr "RUNNING" >nul
            if %errorlevel% equ 0 (
                echo ✅ 服务启动成功！
                echo.
                echo 🌐 服务地址: http://localhost:8001
                echo 📚 API文档: http://localhost:8001/docs
                echo.
                echo 💡 提示: 服务现在会在系统启动时自动运行
            ) else (
                echo ⚠️  服务可能仍在启动中，请稍后检查状态
                echo 检查命令: sc query GeminiBalance
            )
        ) else (
            echo ❌ 服务启动失败
            echo 请检查日志: logs\service.log
        )
    ) else (
        echo.
        echo 💡 服务已安装但未启动
        echo 启动命令: sc start GeminiBalance
    )
    
    echo.
    echo 管理命令:
    echo   查看状态: sc query GeminiBalance
    echo   启动服务: sc start GeminiBalance
    echo   停止服务: sc stop GeminiBalance
    echo   重启服务: sc stop GeminiBalance ^&^& sc start GeminiBalance
    echo   卸载服务: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" gemini_balance_service.py uninstall
    
) else (
    echo ❌ 服务安装失败
    echo 请检查错误信息并重试
)

echo.
pause
