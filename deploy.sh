#!/bin/bash

# Gemini Balance 部署脚本
# 支持Docker和本地部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if command -v docker &> /dev/null; then
        log_success "Docker 已安装: $(docker --version)"
    else
        log_error "Docker 未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose 已安装: $(docker-compose --version)"
    else
        log_error "Docker Compose 未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Python（本地部署）
    if command -v python3 &> /dev/null; then
        log_success "Python3 已安装: $(python3 --version)"
    else
        log_warning "Python3 未安装，仅支持Docker部署"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p data logs config ssl static templates
    
    # 设置权限
    chmod 755 data logs config
    chmod 700 ssl
    
    log_success "目录创建完成"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."
    
    # 创建.env文件（如果不存在）
    if [ ! -f .env ]; then
        cat > .env << EOF
# 基本配置
PORT=8001
LOG_LEVEL=INFO
DEBUG=false

# 数据库配置
DATABASE_URL=sqlite:///./data/gemini_balance.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# API配置
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com
OPENAI_API_BASE_URL=https://api.openai.com/v1

# 安全配置
SECRET_KEY=$(openssl rand -hex 32)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 代理配置（可选）
# HTTP_PROXY=
# HTTPS_PROXY=

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 连接池配置
CONNECTION_POOL_SIZE=50
CONNECTION_TIMEOUT=30
EOF
        log_success "已生成 .env 配置文件"
    else
        log_info ".env 文件已存在，跳过生成"
    fi
}

# Docker部署
deploy_docker() {
    log_info "开始Docker部署..."
    
    # 停止现有容器
    log_info "停止现有容器..."
    docker-compose down || true
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker部署成功！"
        log_info "服务访问地址: http://localhost:8001"
        log_info "查看日志: docker-compose logs -f"
        log_info "停止服务: docker-compose down"
    else
        log_error "Docker部署失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 本地部署
deploy_local() {
    log_info "开始本地部署..."
    
    # 检查Python版本
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
        log_error "需要Python 3.8或更高版本，当前版本: $python_version"
        exit 1
    fi
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    log_info "升级pip..."
    pip install --upgrade pip
    
    # 安装依赖
    log_info "安装Python依赖..."
    pip install -r requirements.txt
    
    # 启动Redis（如果需要）
    if ! pgrep -x "redis-server" > /dev/null; then
        log_warning "Redis未运行，请手动启动Redis服务"
    fi
    
    # 启动应用
    log_info "启动应用..."
    export PYTHONPATH=$(pwd)
    nohup python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 > logs/app.log 2>&1 &
    
    # 保存PID
    echo $! > app.pid
    
    # 等待启动
    sleep 10
    
    # 检查服务
    if curl -f http://localhost:8001/ > /dev/null 2>&1; then
        log_success "本地部署成功！"
        log_info "服务访问地址: http://localhost:8001"
        log_info "查看日志: tail -f logs/app.log"
        log_info "停止服务: kill \$(cat app.pid)"
    else
        log_error "本地部署失败，请检查日志"
        cat logs/app.log
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查主服务
    if curl -f http://localhost:8001/ > /dev/null 2>&1; then
        log_success "主服务正常"
    else
        log_error "主服务异常"
        return 1
    fi
    
    # 检查连接池
    if curl -f http://localhost:8001/connection-pool/health > /dev/null 2>&1; then
        log_success "连接池正常"
    else
        log_warning "连接池检查失败"
    fi
    
    # 检查缓存
    if curl -f http://localhost:8001/cache/enhanced/analytics > /dev/null 2>&1; then
        log_success "缓存系统正常"
    else
        log_warning "缓存系统检查失败"
    fi
    
    # 检查路由
    if curl -f http://localhost:8001/routing/health > /dev/null 2>&1; then
        log_success "智能路由正常"
    else
        log_warning "智能路由检查失败"
    fi
    
    log_success "健康检查完成"
}

# 显示帮助信息
show_help() {
    echo "Gemini Balance 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  docker    使用Docker部署"
    echo "  local     本地部署"
    echo "  health    健康检查"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 docker    # Docker部署"
    echo "  $0 local     # 本地部署"
    echo "  $0 health    # 健康检查"
}

# 主函数
main() {
    echo "🚀 Gemini Balance 部署脚本"
    echo "================================"
    
    case "${1:-docker}" in
        "docker")
            check_dependencies
            create_directories
            generate_config
            deploy_docker
            health_check
            ;;
        "local")
            check_dependencies
            create_directories
            generate_config
            deploy_local
            health_check
            ;;
        "health")
            health_check
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
