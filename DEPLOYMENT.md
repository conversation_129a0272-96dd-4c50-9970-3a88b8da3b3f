# Gemini Balance 部署指南

## 🚀 快速部署

### 方式一：一键Docker部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd gemini-balance

# 2. 一键部署
chmod +x deploy.sh
./deploy.sh docker

# 3. 访问服务
curl http://localhost:8001/
```

### 方式二：Python快速启动

```bash
# 1. 初始化环境
python start.py --setup

# 2. 启动服务
python start.py

# 3. 开发模式（热重载）
python start.py --reload
```

## 📋 系统要求

### 最低要求
- **操作系统**: Linux/macOS/Windows
- **Python**: 3.8+
- **内存**: 2GB RAM
- **存储**: 5GB 可用空间
- **网络**: 互联网连接

### 推荐配置
- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **Python**: 3.11+
- **内存**: 4GB+ RAM
- **CPU**: 2+ 核心
- **存储**: 20GB+ SSD
- **网络**: 稳定的互联网连接

## 🐳 Docker部署

### 1. 使用Docker Compose（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 2. 单独构建Docker镜像

```bash
# 构建镜像
docker build -t gemini-balance:latest .

# 运行容器
docker run -d \
  --name gemini-balance \
  -p 8001:8001 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  gemini-balance:latest
```

## 🔧 本地部署

### 1. 环境准备

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件

创建 `.env` 文件：

```env
# 基本配置
PORT=8001
LOG_LEVEL=INFO
DEBUG=false

# 数据库配置
DATABASE_URL=sqlite:///./data/gemini_balance.db

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0

# API配置
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com
OPENAI_API_BASE_URL=https://api.openai.com/v1

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 3. 启动服务

```bash
# 设置环境变量
export PYTHONPATH=$(pwd)

# 启动服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8001

# 或使用快速启动脚本
python start.py
```

## 🔍 验证部署

### 1. 健康检查

```bash
# 使用脚本检查
python start.py --health

# 或手动检查
curl http://localhost:8001/
curl http://localhost:8001/connection-pool/health
curl http://localhost:8001/cache/enhanced/analytics
curl http://localhost:8001/routing/health
```

### 2. 运行测试

```bash
# 运行所有优化测试
python start.py --test

# 或单独运行
python test_connection_pool.py
python test_cache_optimization.py
python test_intelligent_routing.py
```

## 📊 性能监控

### 1. 内置监控端点

- **服务状态**: `GET /`
- **连接池监控**: `GET /connection-pool/health`
- **缓存分析**: `GET /cache/enhanced/analytics`
- **路由统计**: `GET /routing/stats`
- **系统指标**: `GET /metrics`

### 2. 日志查看

```bash
# Docker环境
docker-compose logs -f gemini-balance

# 本地环境
tail -f logs/app.log
tail -f logs/error.log
```

## 🔒 安全配置

### 1. 环境变量

确保设置以下安全相关的环境变量：

```env
SECRET_KEY=<强密码>
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALLOWED_HOSTS=localhost,127.0.0.1
```

### 2. 防火墙配置

```bash
# 仅开放必要端口
sudo ufw allow 8001/tcp  # API服务
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
```

### 3. SSL/TLS配置

如需HTTPS，请配置SSL证书：

```bash
# 生成自签名证书（测试用）
mkdir -p ssl
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes
```

## 🚀 生产环境部署

### 1. 使用Nginx反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 使用Systemd服务

创建 `/etc/systemd/system/gemini-balance.service`：

```ini
[Unit]
Description=Gemini Balance API Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/gemini-balance
Environment=PYTHONPATH=/opt/gemini-balance
ExecStart=/opt/gemini-balance/venv/bin/python -m uvicorn app.main:app --host 127.0.0.1 --port 8001
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable gemini-balance
sudo systemctl start gemini-balance
```

### 3. 数据库配置

生产环境建议使用MySQL/PostgreSQL：

```env
# MySQL
DATABASE_URL=mysql+aiomysql://user:password@localhost/gemini_balance

# PostgreSQL
DATABASE_URL=postgresql+asyncpg://user:password@localhost/gemini_balance
```

## 🔧 故障排除

### 1. 常见问题

**端口被占用**
```bash
# 查找占用端口的进程
lsof -i :8001
# 或
netstat -tulpn | grep 8001

# 终止进程
kill -9 <PID>
```

**依赖安装失败**
```bash
# 升级pip和setuptools
pip install --upgrade pip setuptools wheel

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt --no-cache-dir
```

**权限问题**
```bash
# 设置正确的文件权限
chmod +x deploy.sh start.py
chmod 755 data logs config
chmod 600 .env
```

### 2. 日志分析

```bash
# 查看错误日志
grep -i error logs/app.log

# 查看性能日志
grep -i "slow\|timeout" logs/app.log

# 实时监控
tail -f logs/app.log | grep -i "error\|warning"
```

## 📈 性能优化

### 1. 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化TCP参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

### 2. 应用级优化

- 启用Redis缓存以提升性能
- 配置连接池参数
- 调整工作进程数量
- 启用Gzip压缩

## 📞 技术支持

如遇到部署问题，请：

1. 查看日志文件
2. 运行健康检查
3. 检查系统资源
4. 参考故障排除指南

## 🎉 部署完成

部署成功后，您可以：

- 访问API文档: `http://localhost:8001/docs`
- 查看系统状态: `http://localhost:8001/`
- 监控性能指标: `http://localhost:8001/metrics`
- 管理缓存: `http://localhost:8001/cache/`
- 配置路由: `http://localhost:8001/routing/`

恭喜！Gemini Balance已成功部署并优化完成！🎉
