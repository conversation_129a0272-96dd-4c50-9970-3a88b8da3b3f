# 🔧 Gemini Balance Windows 服务设置指南

## 📋 概述

本指南将帮助您将 Gemini Balance 设置为 Windows 系统服务，使其能够在系统启动时自动运行，并在后台持续提供服务。

## 🚀 快速安装

### 方法一：使用安装脚本（推荐）

1. **以管理员身份运行**安装脚本：
   ```cmd
   # 右键点击 install_service.bat，选择"以管理员身份运行"
   install_service.bat
   ```

2. 脚本将自动完成：
   - ✅ 检查系统环境
   - ✅ 安装必要依赖
   - ✅ 安装 Windows 服务
   - ✅ 可选择立即启动服务

### 方法二：手动安装

1. **安装依赖**：
   ```cmd
   py -m pip install pywin32
   py -m pip install -r requirements.txt
   ```

2. **安装服务**（需要管理员权限）：
   ```cmd
   py gemini_balance_service.py install
   ```

3. **启动服务**：
   ```cmd
   py gemini_balance_service.py start
   ```

## 🎛️ 服务管理

### 使用服务管理器（推荐）

运行 `service_manager.bat` 获得图形化管理界面：

```cmd
service_manager.bat
```

功能包括：
- 📦 安装/卸载服务
- 🚀 启动/停止/重启服务
- 📊 查看服务状态
- 📋 查看服务日志

### 使用命令行

```cmd
# 安装服务
py gemini_balance_service.py install

# 启动服务
py gemini_balance_service.py start

# 停止服务
py gemini_balance_service.py stop

# 重启服务
py gemini_balance_service.py restart

# 卸载服务
py gemini_balance_service.py uninstall
```

### 使用 Windows 服务管理器

1. 按 `Win + R`，输入 `services.msc`
2. 找到 "Gemini Balance API Service"
3. 右键选择启动/停止/重启

## 📊 服务信息

| 属性 | 值 |
|------|-----|
| 服务名称 | `GeminiBalance` |
| 显示名称 | `Gemini Balance API Service` |
| 描述 | `Gemini API 代理和负载均衡服务` |
| 启动类型 | 手动（可改为自动） |
| 运行账户 | Local System |

## 🔍 监控和日志

### 服务日志

服务日志位于：`logs/service.log`

```cmd
# 查看最新日志
powershell "Get-Content logs\service.log -Tail 20"

# 实时监控日志
powershell "Get-Content logs\service.log -Wait"
```

### 应用日志

应用程序日志位于：`logs/app.log`

### 服务状态检查

```cmd
# 检查服务状态
sc query GeminiBalance

# 检查端口占用
netstat -ano | findstr :8001
```

## ⚙️ 配置选项

### 自动启动设置

设置服务开机自动启动：

```cmd
# 设置为自动启动
sc config GeminiBalance start= auto

# 设置为手动启动
sc config GeminiBalance start= demand

# 设置为禁用
sc config GeminiBalance start= disabled
```

### 服务恢复设置

设置服务失败时的恢复策略：

```cmd
# 设置服务失败时自动重启
sc failure GeminiBalance reset= 86400 actions= restart/5000/restart/5000/restart/5000
```

### 环境变量

服务会自动读取项目目录下的 `.env` 文件。如需修改配置，请编辑该文件后重启服务。

## 🚨 故障排除

### 常见问题

**1. 服务安装失败**
```
错误: 需要管理员权限
解决: 右键选择"以管理员身份运行"
```

**2. 服务启动失败**
```
检查步骤:
1. 查看服务日志: logs/service.log
2. 检查端口占用: netstat -ano | findstr :8001
3. 验证配置文件: .env
4. 检查依赖安装: py -c "import fastapi, uvicorn"
```

**3. pywin32 安装问题**
```cmd
# 重新安装 pywin32
py -m pip uninstall pywin32
py -m pip install pywin32

# 如果仍有问题，尝试：
py -m pip install pywin32 --force-reinstall --no-cache-dir
```

**4. 服务无法停止**
```cmd
# 强制停止服务
sc stop GeminiBalance

# 如果仍无法停止，查找进程并终止
tasklist | findstr python
taskkill /PID <PID> /F
```

### 日志分析

**服务日志关键信息：**
- `正在启动 Gemini Balance 服务` - 服务启动
- `应用程序已启动，PID: xxx` - 应用启动成功
- `端口 8001 已被占用` - 端口冲突
- `应用程序意外退出` - 应用崩溃

**解决步骤：**
1. 查看完整日志文件
2. 检查错误信息
3. 验证配置和环境
4. 重启服务测试

## 🔧 高级配置

### 修改服务配置

编辑 `gemini_balance_service.py` 文件可以修改：
- 服务名称和描述
- 日志配置
- 重启策略
- 监控间隔

### 性能优化

```cmd
# 设置服务优先级（高优先级）
sc config GeminiBalance type= own start= auto

# 设置服务依赖（如需要数据库服务）
sc config GeminiBalance depend= MySQL
```

### 安全设置

```cmd
# 创建专用服务账户（可选）
net user GeminiBalanceService /add
net localgroup "Log on as a service" GeminiBalanceService /add

# 设置服务运行账户
sc config GeminiBalance obj= .\GeminiBalanceService password= YourPassword
```

## 📞 技术支持

如遇到问题：

1. 📋 查看服务日志：`logs/service.log`
2. 📊 检查服务状态：`sc query GeminiBalance`
3. 🔍 验证端口状态：`netstat -ano | findstr :8001`
4. 🧪 测试应用启动：`py start.py`

## 🎉 验证安装

服务安装成功后，访问以下地址验证：

- 🌐 主服务：http://localhost:8001/
- 📚 API文档：http://localhost:8001/docs
- 🔧 服务状态：通过服务管理器查看

**恭喜！您已成功将 Gemini Balance 设置为 Windows 服务！** 🎉
