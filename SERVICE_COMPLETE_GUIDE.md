# 🔧 Gemini Balance Windows 服务完整指南

## 📋 文件说明

我已经为您创建了完整的 Windows 服务解决方案：

### 核心文件
- `gemini_balance_service.py` - 主服务脚本（已优化）
- `service_admin.bat` - 管理员服务管理工具
- `monitor_service.py` - 服务监控脚本

### 服务特性

✅ **智能重启机制** - 应用崩溃时自动重启（最多5次）
✅ **详细日志记录** - 服务日志、应用输出、错误日志分离
✅ **端口健康检查** - 监控端口8001状态
✅ **优雅停止** - 15秒正常终止，超时强制结束
✅ **环境变量支持** - 完整的Python环境配置
✅ **无窗口运行** - 后台静默运行

## 🚀 安装步骤

### 1. 使用管理员工具安装（推荐）

1. **右键点击** `service_admin.bat`
2. 选择 **"以管理员身份运行"**
3. 选择选项 `[1] 安装服务`
4. 选择选项 `[2] 启动服务`

### 2. 手动命令行安装

```cmd
# 以管理员身份打开 PowerShell 或 CMD
cd C:\Users\<USER>\gemini-balance

# 安装服务
py gemini_balance_service.py install

# 设置自动启动
sc config GeminiBalance start= auto

# 启动服务
sc start GeminiBalance
```

## 📊 服务管理

### 使用管理工具

运行 `service_admin.bat`（以管理员身份）获得完整的管理界面：

- **安装服务** - 安装并配置自动启动
- **启动/停止/重启** - 服务控制
- **查看状态** - 详细服务状态
- **查看日志** - 服务、输出、错误日志
- **测试应用** - 直接测试应用程序
- **端口检查** - 检查端口占用情况

### 命令行管理

```cmd
# 服务控制
sc start GeminiBalance      # 启动
sc stop GeminiBalance       # 停止
sc query GeminiBalance      # 查看状态

# 使用服务脚本
py gemini_balance_service.py start    # 启动
py gemini_balance_service.py stop     # 停止
py gemini_balance_service.py restart  # 重启
```

## 📋 监控和日志

### 实时监控

运行监控脚本：
```cmd
py monitor_service.py
```

显示实时状态：
- 服务状态（RUNNING/STOPPED）
- 端口监听状态
- API健康状态
- Python进程数量

### 日志文件

| 日志文件 | 内容 | 位置 |
|----------|------|------|
| `service.log` | 服务运行日志 | `logs/service.log` |
| `app_stdout.log` | 应用程序输出 | `logs/app_stdout.log` |
| `app_stderr.log` | 应用程序错误 | `logs/app_stderr.log` |
| `monitor.log` | 监控日志 | `logs/monitor.log` |

### 查看日志

```cmd
# 查看服务日志
powershell "Get-Content logs\service.log -Tail 20"

# 查看应用输出
powershell "Get-Content logs\app_stdout.log -Tail 20"

# 查看错误日志
powershell "Get-Content logs\app_stderr.log -Tail 20"
```

## 🔧 服务配置

### 服务信息

| 属性 | 值 |
|------|-----|
| 服务名称 | `GeminiBalance` |
| 显示名称 | `Gemini Balance API Service` |
| 启动类型 | `Automatic` |
| 重启策略 | 最多5次自动重启 |
| 端口 | `8001` |

### 高级配置

编辑 `gemini_balance_service.py` 可以修改：

```python
# 最大重启次数
self.max_restarts = 5

# 端口检查超时
port_check_attempts = 30  # 30秒

# 进程终止超时
self.process.wait(timeout=15)  # 15秒
```

## 🚨 故障排除

### 常见问题

**1. 服务无法启动**
```cmd
# 检查服务状态
sc query GeminiBalance

# 查看服务日志
powershell "Get-Content logs\service.log -Tail 20"

# 测试应用程序
py start.py
```

**2. 端口被占用**
```cmd
# 检查端口占用
netstat -ano | findstr :8001

# 终止占用进程
taskkill /PID <PID> /F
```

**3. 服务频繁重启**
```cmd
# 查看错误日志
powershell "Get-Content logs\app_stderr.log -Tail 20"

# 检查配置文件
type .env
```

### 诊断步骤

1. **检查服务状态** - 使用 `service_admin.bat` 选项6
2. **查看日志文件** - 使用 `service_admin.bat` 选项7
3. **测试应用程序** - 使用 `service_admin.bat` 选项8
4. **检查端口状态** - 使用 `service_admin.bat` 选项9
5. **运行监控脚本** - `py monitor_service.py`

## 🎯 验证安装

安装成功后，您应该能够：

1. ✅ 在 Windows 服务管理器中看到 "Gemini Balance API Service"
2. ✅ 服务状态显示为 "正在运行"
3. ✅ 访问 http://localhost:8001 看到服务响应
4. ✅ 访问 http://localhost:8001/docs 看到 API 文档
5. ✅ 在 `logs/` 目录中看到日志文件

## 🎉 完成！

您现在拥有一个完整的、生产就绪的 Windows 服务解决方案：

- 🔄 **自动重启** - 应用崩溃时自动恢复
- 📊 **完整监控** - 实时状态监控和日志记录
- 🛠️ **易于管理** - 图形化管理工具
- 🚀 **开机自启** - 系统启动时自动运行
- 📋 **详细日志** - 完整的运行日志记录

**享受您的高可用 Gemini Balance 服务！** 🎉
