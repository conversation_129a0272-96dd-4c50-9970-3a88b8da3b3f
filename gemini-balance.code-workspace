{"folders": [{"name": "Gemini Balance", "path": "."}], "settings": {"python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "terminal.integrated.cwd": "${workspaceFolder}", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "editor.formatOnSave": true, "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "workbench.startupEditor": "none", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"]}, "Git Bash": {"source": "<PERSON><PERSON>"}}, "terminal.integrated.defaultProfile.windows": "PowerShell", "task.autoDetect": "on", "task.quickOpen.history": 10, "task.quickOpen.detail": true, "task.quickOpen.skip": false, "files.associations": {"*.env": "properties", "*.env.local": "properties", "*.env.example": "properties", "Dockerfile*": "dockerfile", "docker-compose*.yml": "dockercompose", "docker-compose*.yaml": "dockercompose"}, "emmet.includeLanguages": {"jinja-html": "html", "jinja2": "html"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/node_modules": true, "**/.git": false, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.vscode/settings.json": false}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/__pycache__": true, "**/venv": true, "**/env": true, "**/.env": false}, "python.analysis.extraPaths": ["./app", "./extensions"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic"}, "extensions": {"recommendations": ["ms-python.python", "ms-python.vscode-pylance", "ms-python.black-formatter", "ms-python.pylint", "ms-python.flake8", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-docker", "ms-vscode-remote.remote-containers", "github.copilot", "github.copilot-chat", "ms-vscode.live-server", "humao.rest-client", "ms-vscode.thunder-client"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 自动启动开发服务器", "type": "shell", "command": "python", "args": ["start.py", "--reload", "--host", "0.0.0.0", "--port", "8001"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}, "dependsOrder": "sequence", "dependsOn": ["检查环境", "安装依赖"]}, {"label": "检查环境", "type": "shell", "command": "python", "args": ["-c", "import sys; print(f'Python {sys.version}'); import os; print('✅ 环境检查通过') if os.path.exists('requirements.txt') else exit(1)"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "problemMatcher": []}, {"label": "安装依赖", "type": "shell", "command": "python", "args": ["-m", "pip", "install", "-r", "requirements.txt", "--quiet"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "problemMatcher": []}]}}