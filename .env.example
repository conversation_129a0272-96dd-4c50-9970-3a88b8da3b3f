# Gemini Balance 环境配置示例
# 复制此文件为 .env 并修改相应配置

# ==================== 基本配置 ====================
PORT=8001
LOG_LEVEL=INFO
DEBUG=false

# ==================== 数据库配置 ====================
# SQLite (默认，无需额外设置)
DATABASE_URL=sqlite:///./data/gemini_balance.db

# MySQL (本地)
# DATABASE_URL=mysql+aiomysql://user:password@localhost:3306/gemini_balance

# PostgreSQL (本地)
# DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/gemini_balance

# Google Cloud SQL (MySQL)
# DATABASE_URL=mysql+aiomysql://user:password@/database?unix_socket=/cloudsql/project:region:instance

# Google Cloud SQL (PostgreSQL)
# DATABASE_URL=postgresql+asyncpg://user:password@/database?host=/cloudsql/project:region:instance

# ==================== 缓存配置 ====================
# Redis (可选，用于分布式缓存)
# REDIS_URL=redis://localhost:6379/0

# Google Cloud Memorystore
# REDIS_URL=redis://********:6379/0

# ==================== API配置 ====================
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com
OPENAI_API_BASE_URL=https://api.openai.com/v1

# ==================== 安全配置 ====================
# 生成强密钥: openssl rand -hex 32
SECRET_KEY=your-secret-key-here-please-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 允许的主机 (生产环境设置)
# ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# ==================== Google Cloud 配置 ====================
# Google Cloud 项目ID
# GOOGLE_CLOUD_PROJECT=your-project-id

# Google Cloud 区域
# GOOGLE_CLOUD_REGION=asia-east1

# Firestore 配置
# FIRESTORE_PROJECT_ID=your-project-id

# ==================== 代理配置 ====================
# HTTP代理 (可选)
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080

# ==================== 性能配置 ====================
# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 连接池配置
CONNECTION_POOL_SIZE=50
CONNECTION_TIMEOUT=30

# ==================== 监控配置 ====================
# 启用指标收集
ENABLE_METRICS=true

# 指标端口
METRICS_PORT=9090

# ==================== 日志配置 ====================
# 日志文件路径
LOG_FILE_PATH=logs/app.log

# 日志轮转大小 (MB)
LOG_MAX_SIZE=100

# 保留日志文件数量
LOG_BACKUP_COUNT=5

# ==================== 限流配置 ====================
# API限流 (请求/分钟)
RATE_LIMIT_PER_MINUTE=60

# 上传限流 (请求/分钟)
UPLOAD_RATE_LIMIT_PER_MINUTE=10

# ==================== CORS配置 ====================
# 允许的源 (生产环境设置)
# CORS_ORIGINS=https://your-frontend.com,https://your-domain.com

# ==================== SSL/TLS配置 ====================
# SSL证书路径 (HTTPS)
# SSL_CERT_PATH=ssl/cert.pem
# SSL_KEY_PATH=ssl/key.pem

# ==================== 第三方服务配置 ====================
# Cloudflare配置
# CLOUDFLARE_API_TOKEN=your-cloudflare-token
# CLOUDFLARE_ZONE_ID=your-zone-id

# Sentry错误监控
# SENTRY_DSN=https://your-sentry-dsn

# ==================== 开发配置 ====================
# 开发模式热重载
# RELOAD=true

# 调试SQL查询
# DEBUG_SQL=false

# 测试数据库
# TEST_DATABASE_URL=sqlite:///./test.db
