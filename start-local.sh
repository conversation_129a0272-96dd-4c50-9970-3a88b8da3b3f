#!/bin/bash

# 本地开发环境启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🚀 Gemini Balance 本地开发环境"
echo "================================"

# 检查依赖
check_dependencies() {
    log_info "检查开发环境依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查Docker (可选)
    if command -v docker &> /dev/null; then
        log_success "Docker 已安装"
        DOCKER_AVAILABLE=true
    else
        log_warning "Docker 未安装，将使用纯Python模式"
        DOCKER_AVAILABLE=false
    fi
    
    # 检查Docker Compose (可选)
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose 已安装"
        COMPOSE_AVAILABLE=true
    else
        log_warning "Docker Compose 未安装"
        COMPOSE_AVAILABLE=false
    fi
}

# 设置本地环境
setup_local_env() {
    log_info "设置本地开发环境..."
    
    # 创建目录
    mkdir -p data logs config static templates data/uploads data/cache data/backups
    
    # 复制本地配置
    if [ -f ".env.local" ] && [ ! -f ".env" ]; then
        cp .env.local .env
        log_success "已复制本地开发配置"
    fi
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    log_info "安装Python依赖..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    log_success "本地环境设置完成"
}

# 启动Docker服务
start_docker_services() {
    if [ "$DOCKER_AVAILABLE" = true ] && [ "$COMPOSE_AVAILABLE" = true ]; then
        log_info "启动Docker服务..."
        
        echo "选择启动模式:"
        echo "1) 完整服务 (应用+数据库+缓存+监控)"
        echo "2) 基础服务 (应用+数据库+缓存)"
        echo "3) 仅应用服务"
        echo "4) 仅外部服务 (数据库+缓存)"
        read -p "请选择 (1-4): " choice
        
        case $choice in
            1)
                docker-compose -f docker-compose.local.yml up -d
                log_success "完整服务已启动"
                show_service_urls
                ;;
            2)
                docker-compose -f docker-compose.local.yml up -d gemini-balance-local redis-local mysql-local
                log_success "基础服务已启动"
                ;;
            3)
                docker-compose -f docker-compose.local.yml up -d gemini-balance-local
                log_success "应用服务已启动"
                ;;
            4)
                docker-compose -f docker-compose.local.yml up -d redis-local mysql-local
                log_success "外部服务已启动"
                log_info "现在可以运行: python start.py"
                ;;
            *)
                log_warning "无效选择，启动基础服务"
                docker-compose -f docker-compose.local.yml up -d gemini-balance-local redis-local mysql-local
                ;;
        esac
    else
        log_warning "Docker不可用，跳过Docker服务启动"
    fi
}

# 启动Python服务
start_python_service() {
    log_info "启动Python服务..."
    
    # 激活虚拟环境
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # 设置环境变量
    export PYTHONPATH=$(pwd)
    
    echo "选择启动模式:"
    echo "1) 开发模式 (热重载)"
    echo "2) 生产模式"
    echo "3) 调试模式"
    read -p "请选择 (1-3): " mode
    
    case $mode in
        1)
            log_info "启动开发模式..."
            python start.py --reload --host 0.0.0.0 --port 8001
            ;;
        2)
            log_info "启动生产模式..."
            python start.py --host 0.0.0.0 --port 8001
            ;;
        3)
            log_info "启动调试模式..."
            python -m pdb start.py --host 0.0.0.0 --port 8001
            ;;
        *)
            log_warning "无效选择，使用开发模式"
            python start.py --reload --host 0.0.0.0 --port 8001
            ;;
    esac
}

# 显示服务URL
show_service_urls() {
    log_success "服务已启动，访问地址:"
    echo ""
    echo "🌐 主要服务:"
    echo "   应用主页: http://localhost:8001/"
    echo "   API文档: http://localhost:8001/docs"
    echo "   Nginx代理: http://localhost/"
    echo ""
    echo "🔍 监控服务:"
    echo "   连接池监控: http://localhost:8001/connection-pool/health"
    echo "   缓存分析: http://localhost:8001/cache/enhanced/analytics"
    echo "   智能路由: http://localhost:8001/routing/health"
    echo "   系统指标: http://localhost:8001/metrics"
    echo ""
    echo "📊 开发工具:"
    echo "   Prometheus: http://localhost:9090/"
    echo "   Grafana: http://localhost:3000/ (admin/admin123)"
    echo "   Kibana: http://localhost:5601/"
    echo ""
    echo "🗄️ 数据库:"
    echo "   MySQL: localhost:3306 (gemini_user/gemini_pass)"
    echo "   PostgreSQL: localhost:5432 (gemini_user/gemini_pass)"
    echo "   Redis: localhost:6379"
    echo ""
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    services=(
        "http://localhost:8001/ 主应用"
        "http://localhost:8001/docs API文档"
        "http://localhost:8001/connection-pool/health 连接池"
        "http://localhost:8001/cache/enhanced/analytics 缓存"
        "http://localhost:8001/routing/health 路由"
    )
    
    for service in "${services[@]}"; do
        url=$(echo $service | cut -d' ' -f1)
        name=$(echo $service | cut -d' ' -f2-)
        
        if curl -f "$url" > /dev/null 2>&1; then
            log_success "$name 正常"
        else
            log_warning "$name 异常"
        fi
    done
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止Docker服务
    if [ "$DOCKER_AVAILABLE" = true ] && [ "$COMPOSE_AVAILABLE" = true ]; then
        docker-compose -f docker-compose.local.yml down
        log_success "Docker服务已停止"
    fi
    
    # 停止Python服务
    if [ -f "app.pid" ]; then
        kill $(cat app.pid) 2>/dev/null || true
        rm -f app.pid
        log_success "Python服务已停止"
    fi
}

# 查看日志
view_logs() {
    log_info "查看服务日志..."
    
    echo "选择日志类型:"
    echo "1) 应用日志"
    echo "2) Docker日志"
    echo "3) 所有日志"
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            if [ -f "logs/local.log" ]; then
                tail -f logs/local.log
            else
                log_warning "应用日志文件不存在"
            fi
            ;;
        2)
            if [ "$DOCKER_AVAILABLE" = true ]; then
                docker-compose -f docker-compose.local.yml logs -f
            else
                log_warning "Docker不可用"
            fi
            ;;
        3)
            if [ -f "logs/local.log" ] && [ "$DOCKER_AVAILABLE" = true ]; then
                tail -f logs/local.log &
                docker-compose -f docker-compose.local.yml logs -f
            else
                log_warning "部分日志不可用"
            fi
            ;;
        *)
            log_warning "无效选择"
            ;;
    esac
}

# 主菜单
show_menu() {
    echo ""
    echo "🛠️  本地开发菜单:"
    echo "1) 设置环境"
    echo "2) 启动Docker服务"
    echo "3) 启动Python服务"
    echo "4) 检查服务状态"
    echo "5) 查看日志"
    echo "6) 停止服务"
    echo "7) 退出"
    echo ""
}

# 主函数
main() {
    check_dependencies
    
    if [ $# -eq 0 ]; then
        # 交互模式
        while true; do
            show_menu
            read -p "请选择操作 (1-7): " choice
            
            case $choice in
                1) setup_local_env ;;
                2) start_docker_services ;;
                3) start_python_service ;;
                4) check_services ;;
                5) view_logs ;;
                6) stop_services ;;
                7) log_info "退出"; exit 0 ;;
                *) log_warning "无效选择" ;;
            esac
        done
    else
        # 命令行模式
        case $1 in
            "setup") setup_local_env ;;
            "start") start_docker_services ;;
            "python") start_python_service ;;
            "check") check_services ;;
            "logs") view_logs ;;
            "stop") stop_services ;;
            *) 
                echo "用法: $0 [setup|start|python|check|logs|stop]"
                exit 1
                ;;
        esac
    fi
}

# 执行主函数
main "$@"
