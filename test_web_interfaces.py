#!/usr/bin/env python3
"""
Web界面集成测试脚本
测试所有新增管理中心的可访问性、功能性和集成性
"""

import asyncio
import aiohttp
import sys
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass


@dataclass
class TestResult:
    """测试结果"""
    name: str
    success: bool
    message: str
    duration: float


class WebInterfacesTester:
    """Web界面集成测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.auth_token = "sk-123456"
        self.session = None
        self.results: List[TestResult] = []
        
        # 测试页面列表
        self.test_pages = [
            ("/web", "主控制台"),
            ("/web/security", "安全策略中心"),
            ("/web/cache", "缓存优化中心"),
            ("/web/routing", "路由策略中心"),
            ("/web/analytics", "分析报告中心"),
            ("/web/settings", "系统设置中心"),
            ("/web/monitoring", "监控大屏"),
            ("/web/user-groups", "用户组管理"),
            ("/config", "配置中心"),
        ]
        
        # API端点测试
        self.api_endpoints = [
            ("/web/api/groups", "用户组API"),
            ("/web/api/analytics/comprehensive", "分析数据API"),
            ("/api/config", "配置API"),
            ("/api/config/context-isolation", "上下文隔离API"),
        ]
    
    async def setup(self):
        """设置测试环境"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=10)
        )
        
        # 登录获取认证
        await self.authenticate()
    
    async def teardown(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
    
    async def authenticate(self):
        """认证登录"""
        try:
            # 模拟登录过程
            login_data = {"auth_token": self.auth_token}
            async with self.session.post(
                f"{self.base_url}/auth",
                data=login_data,
                allow_redirects=False
            ) as response:
                # 获取认证cookie
                if response.cookies:
                    self.session.cookie_jar.update_cookies(response.cookies)
                    
        except Exception as e:
            print(f"认证失败: {e}")
    
    async def test_page_accessibility(self, path: str, name: str) -> TestResult:
        """测试页面可访问性"""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}{path}") as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    content = await response.text()
                    
                    # 检查页面基本内容
                    if "<!DOCTYPE html>" in content and "<html" in content:
                        return TestResult(
                            name=f"页面访问 - {name}",
                            success=True,
                            message=f"页面正常加载 (状态码: {response.status})",
                            duration=duration
                        )
                    else:
                        return TestResult(
                            name=f"页面访问 - {name}",
                            success=False,
                            message="页面内容格式异常",
                            duration=duration
                        )
                else:
                    return TestResult(
                        name=f"页面访问 - {name}",
                        success=False,
                        message=f"HTTP错误: {response.status}",
                        duration=duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name=f"页面访问 - {name}",
                success=False,
                message=f"请求异常: {str(e)}",
                duration=duration
            )
    
    async def test_api_endpoint(self, path: str, name: str) -> TestResult:
        """测试API端点"""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}{path}") as response:
                duration = time.time() - start_time
                
                if response.status in [200, 401]:  # 401可能是认证问题，但端点存在
                    return TestResult(
                        name=f"API端点 - {name}",
                        success=True,
                        message=f"端点可访问 (状态码: {response.status})",
                        duration=duration
                    )
                else:
                    return TestResult(
                        name=f"API端点 - {name}",
                        success=False,
                        message=f"HTTP错误: {response.status}",
                        duration=duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name=f"API端点 - {name}",
                success=False,
                message=f"请求异常: {str(e)}",
                duration=duration
            )
    
    async def test_navigation_flow(self) -> TestResult:
        """测试导航流程"""
        start_time = time.time()
        
        try:
            # 测试从主控制台到各个管理中心的导航
            navigation_paths = [
                ("/web", "/web/security"),
                ("/web", "/web/cache"),
                ("/web", "/web/routing"),
                ("/web", "/web/analytics"),
                ("/web", "/web/settings"),
            ]
            
            for start_path, target_path in navigation_paths:
                # 访问起始页面
                async with self.session.get(f"{self.base_url}{start_path}") as response:
                    if response.status != 200:
                        raise Exception(f"无法访问起始页面: {start_path}")
                
                # 访问目标页面
                async with self.session.get(f"{self.base_url}{target_path}") as response:
                    if response.status != 200:
                        raise Exception(f"无法访问目标页面: {target_path}")
            
            duration = time.time() - start_time
            return TestResult(
                name="导航流程测试",
                success=True,
                message="所有导航路径正常",
                duration=duration
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name="导航流程测试",
                success=False,
                message=f"导航测试失败: {str(e)}",
                duration=duration
            )
    
    async def test_performance(self) -> TestResult:
        """测试页面性能"""
        start_time = time.time()
        
        try:
            # 测试主要页面的加载时间
            performance_results = []
            
            for path, name in self.test_pages[:6]:  # 测试主要页面
                page_start = time.time()
                async with self.session.get(f"{self.base_url}{path}") as response:
                    if response.status == 200:
                        await response.text()  # 确保完全加载
                        page_duration = time.time() - page_start
                        performance_results.append((name, page_duration))
            
            # 计算平均加载时间
            avg_load_time = sum(duration for _, duration in performance_results) / len(performance_results)
            
            duration = time.time() - start_time
            
            if avg_load_time < 2.0:  # 2秒内为良好性能
                return TestResult(
                    name="性能测试",
                    success=True,
                    message=f"平均加载时间: {avg_load_time:.2f}秒",
                    duration=duration
                )
            else:
                return TestResult(
                    name="性能测试",
                    success=False,
                    message=f"平均加载时间过长: {avg_load_time:.2f}秒",
                    duration=duration
                )
                
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name="性能测试",
                success=False,
                message=f"性能测试失败: {str(e)}",
                duration=duration
            )
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Web界面集成测试...")
        print("=" * 60)
        
        await self.setup()
        
        try:
            # 1. 页面可访问性测试
            print("\n📄 测试页面可访问性...")
            for path, name in self.test_pages:
                result = await self.test_page_accessibility(path, name)
                self.results.append(result)
                self._print_result(result)
            
            # 2. API端点测试
            print("\n🔌 测试API端点...")
            for path, name in self.api_endpoints:
                result = await self.test_api_endpoint(path, name)
                self.results.append(result)
                self._print_result(result)
            
            # 3. 导航流程测试
            print("\n🧭 测试导航流程...")
            result = await self.test_navigation_flow()
            self.results.append(result)
            self._print_result(result)
            
            # 4. 性能测试
            print("\n⚡ 测试页面性能...")
            result = await self.test_performance()
            self.results.append(result)
            self._print_result(result)
            
        finally:
            await self.teardown()
        
        # 输出测试总结
        self._print_summary()
    
    def _print_result(self, result: TestResult):
        """打印测试结果"""
        status = "✅" if result.success else "❌"
        print(f"  {status} {result.name:<30} {result.message} ({result.duration:.2f}s)")
    
    def _print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.results:
                if not result.success:
                    print(f"  - {result.name}: {result.message}")
        
        total_duration = sum(r.duration for r in self.results)
        print(f"\n⏱️  总耗时: {total_duration:.2f}秒")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！Web界面集成正常。")
            return True
        else:
            print(f"\n⚠️  有 {failed_tests} 个测试失败，请检查相关功能。")
            return False


async def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8001"
    
    tester = WebInterfacesTester(base_url)
    success = await tester.run_all_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
