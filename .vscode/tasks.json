{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "python", "args": ["-m", "pip", "install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Setup Project", "type": "shell", "command": "python", "args": ["start.py", "--setup"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start Development Server", "type": "shell", "command": "python", "args": ["start.py", "--reload", "--host", "0.0.0.0", "--port", "8001"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}, "dependsOn": ["Check and Setup Environment"]}, {"label": "Check and Setup Environment", "type": "shell", "command": "python", "args": ["-c", "import os; print('Environment check passed') if os.path.exists('requirements.txt') else exit(1)"], "group": "build", "presentation": {"echo": false, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "problemMatcher": []}, {"label": "Stop All Servers", "type": "shell", "command": "taskkill", "args": ["/F", "/IM", "python.exe"], "windows": {"command": "taskkill", "args": ["/F", "/IM", "python.exe"]}, "linux": {"command": "pkill", "args": ["-f", "u<PERSON><PERSON>"]}, "osx": {"command": "pkill", "args": ["-f", "u<PERSON><PERSON>"]}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "python", "args": ["start.py", "--test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "Health Check", "type": "shell", "command": "python", "args": ["start.py", "--health"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}