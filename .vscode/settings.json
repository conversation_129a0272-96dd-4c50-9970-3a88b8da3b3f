{"commentTranslate.source": "upupnoah.chatgpt-comment-translateX-chatgpt", "python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "terminal.integrated.cwd": "${workspaceFolder}", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "editor.formatOnSave": true, "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "workbench.startupEditor": "none", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"]}, "Git Bash": {"source": "<PERSON><PERSON>"}}, "terminal.integrated.defaultProfile.windows": "PowerShell"}