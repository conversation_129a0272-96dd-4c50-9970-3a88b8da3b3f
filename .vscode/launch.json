{
    // 使用 IntelliSense 了解相关属性。
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "🚀 启动 Gemini Balance (开发模式)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start.py",
            "args": [
                "--reload",
                "--host",
                "0.0.0.0",
                "--port",
                "8001"
            ],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "DEBUG": "true",
                "ENVIRONMENT": "development"
            },
            "jinja": true,
            "justMyCode": false
        },
        {
            "name": "🐛 调试 FastAPI 应用",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "app.main:app",
                "--reload",
                "--host",
                "0.0.0.0",
                "--port",
                "8001",
                "--log-level",
                "debug"
            ],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "DEBUG": "true",
                "LOG_LEVEL": "DEBUG"
            },
            "jinja": true,
            "justMyCode": false
        },
        {
            "name": "🧪 运行测试",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start.py",
            "args": ["--test"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "⚙️ 项目设置",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start.py",
            "args": ["--setup"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🏥 健康检查",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start.py",
            "args": ["--health"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}