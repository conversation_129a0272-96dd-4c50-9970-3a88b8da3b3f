@echo off
REM Gemini Balance Windows 部署脚本

setlocal enabledelayedexpansion

echo 🚀 Gemini Balance 部署脚本 (Windows)
echo ================================

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python 未安装，请先安装Python 3.8+
    pause
    exit /b 1
)

echo [INFO] Python 已安装: 
python --version

REM 检查Docker（可选）
docker --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker 未安装，仅支持本地部署
) else (
    echo [INFO] Docker 已安装:
    docker --version
)

REM 创建必要目录
echo [INFO] 创建必要目录...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "static" mkdir static
if not exist "templates" mkdir templates
echo [SUCCESS] 目录创建完成

REM 生成.env文件
if not exist ".env" (
    echo [INFO] 生成.env配置文件...
    (
        echo # 基本配置
        echo PORT=8001
        echo LOG_LEVEL=INFO
        echo DEBUG=false
        echo.
        echo # 数据库配置
        echo DATABASE_URL=sqlite:///./data/gemini_balance.db
        echo.
        echo # Redis配置（可选）
        echo # REDIS_URL=redis://localhost:6379/0
        echo.
        echo # API配置
        echo GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com
        echo OPENAI_API_BASE_URL=https://api.openai.com/v1
        echo.
        echo # 安全配置
        echo SECRET_KEY=your-secret-key-here
        echo ACCESS_TOKEN_EXPIRE_MINUTES=30
        echo.
        echo # 缓存配置
        echo CACHE_TTL=3600
        echo CACHE_MAX_SIZE=1000
        echo.
        echo # 连接池配置
        echo CONNECTION_POOL_SIZE=50
        echo CONNECTION_TIMEOUT=30
    ) > .env
    echo [SUCCESS] .env文件生成完成
) else (
    echo [INFO] .env文件已存在，跳过生成
)

REM 升级pip
echo [INFO] 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo [INFO] 安装Python依赖...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)
echo [SUCCESS] 依赖安装完成

REM 设置环境变量
set PYTHONPATH=%CD%

REM 启动服务
echo [INFO] 启动Gemini Balance服务器...
echo [INFO] 服务将在 http://localhost:8001 启动
echo [INFO] 按 Ctrl+C 停止服务
echo.

python -m uvicorn app.main:app --host 127.0.0.1 --port 8001

echo.
echo [INFO] 服务已停止
pause
