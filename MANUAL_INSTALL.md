# 🔧 Gemini Balance 服务手动安装指南

## 🚨 重要提示

由于批处理脚本可能遇到编码问题，建议使用以下手动安装方法：

## 📋 手动安装步骤

### 1. 打开管理员命令提示符

1. 按 `Win + X`
2. 选择 **"Windows PowerShell (管理员)"** 或 **"命令提示符 (管理员)"**
3. 如果出现 UAC 提示，点击 **"是"**

### 2. 切换到项目目录

```cmd
cd C:\Users\<USER>\gemini-balance
```

### 3. 验证文件存在

```cmd
dir gemini_balance_service.py
```

应该看到文件存在。

### 4. 安装服务

```cmd
py gemini_balance_service.py install
```

如果成功，会显示：
```
✅ Gemini Balance 服务安装成功！
```

### 5. 启动服务

```cmd
py gemini_balance_service.py start
```

如果成功，会显示：
```
✅ Gemini Balance 服务启动成功！
```

## 🎯 验证安装

### 检查服务状态

```cmd
sc query GeminiBalance
```

应该显示服务状态为 `RUNNING`。

### 访问服务

在浏览器中访问：
- 主服务：http://localhost:8001/
- API文档：http://localhost:8001/docs

## 🔧 服务管理命令

```cmd
# 启动服务
py gemini_balance_service.py start

# 停止服务
py gemini_balance_service.py stop

# 重启服务
py gemini_balance_service.py restart

# 卸载服务
py gemini_balance_service.py uninstall
```

## 🚨 故障排除

### 问题1：权限不足

**错误信息：** `拒绝访问` 或 `Access Denied`

**解决方案：**
1. 确保以管理员身份运行命令提示符
2. 右键点击 PowerShell/CMD，选择"以管理员身份运行"

### 问题2：找不到文件

**错误信息：** `不是内部或外部命令`

**解决方案：**
1. 确保在正确的项目目录：`C:\Users\<USER>\gemini-balance`
2. 使用 `cd` 命令切换目录：`cd C:\Users\<USER>\gemini-balance`
3. 验证文件存在：`dir gemini_balance_service.py`

### 问题3：Python 找不到

**错误信息：** `'py' 不是内部或外部命令`

**解决方案：**
1. 尝试使用 `python` 代替 `py`：
   ```cmd
   python gemini_balance_service.py install
   ```
2. 如果仍然不行，检查 Python 安装

### 问题4：端口被占用

**错误信息：** 服务启动失败

**解决方案：**
1. 检查端口占用：
   ```cmd
   netstat -ano | findstr :8001
   ```
2. 如果有进程占用，终止它：
   ```cmd
   taskkill /PID <PID> /F
   ```

### 问题5：服务无法启动

**解决方案：**
1. 查看服务日志：
   ```cmd
   type logs\service.log
   ```
2. 检查应用日志：
   ```cmd
   type logs\app.log
   ```

## 🎉 成功标志

安装成功后，您应该能够：

1. ✅ 在 Windows 服务管理器中看到 "Gemini Balance API Service"
2. ✅ 访问 http://localhost:8001 看到服务响应
3. ✅ 访问 http://localhost:8001/docs 看到 API 文档
4. ✅ 使用管理命令控制服务

## 📞 需要帮助？

如果遇到问题：

1. 检查是否以管理员身份运行
2. 确认在正确的项目目录
3. 查看错误日志文件
4. 验证 Python 和依赖安装

**记住：所有命令都必须在项目目录 `C:\Users\<USER>\gemini-balance` 中以管理员身份执行！**
