# 本地开发环境配置
# 复制此文件为 .env 用于本地开发

# ==================== 基本配置 ====================
PORT=8001
LOG_LEVEL=DEBUG
DEBUG=true
ENVIRONMENT=development

# ==================== 数据库配置 ====================
# SQLite (本地开发推荐)
DATABASE_URL=sqlite:///./data/gemini_balance_local.db

# 或使用本地MySQL
# DATABASE_URL=mysql+aiomysql://root:password@localhost:3306/gemini_balance

# 或使用本地PostgreSQL
# DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/gemini_balance

# ==================== 缓存配置 ====================
# 本地Redis (可选)
# REDIS_URL=redis://localhost:6379/0

# 内存缓存 (无需Redis)
CACHE_BACKEND=memory
CACHE_TTL=1800
CACHE_MAX_SIZE=500

# ==================== API配置 ====================
GEMINI_API_BASE_URL=https://generativelanguage.googleapis.com
OPENAI_API_BASE_URL=https://api.openai.com/v1

# 本地测试API密钥 (请替换为真实密钥)
# GEMINI_API_KEY=your-gemini-api-key
# OPENAI_API_KEY=your-openai-api-key

# ==================== 安全配置 ====================
SECRET_KEY=local-development-secret-key-not-for-production
ACCESS_TOKEN_EXPIRE_MINUTES=60

# 本地开发允许所有主机
ALLOWED_HOSTS=*

# ==================== 开发配置 ====================
# 启用热重载
RELOAD=true

# 启用调试模式
DEBUG_SQL=true

# 启用详细日志
VERBOSE_LOGGING=true

# ==================== 性能配置 ====================
# 本地开发较小的连接池
CONNECTION_POOL_SIZE=10
CONNECTION_TIMEOUT=30

# 较短的缓存时间便于测试
CACHE_TTL=300

# ==================== 监控配置 ====================
# 启用本地监控
ENABLE_METRICS=true
METRICS_PORT=9090

# ==================== 测试配置 ====================
# 测试数据库
TEST_DATABASE_URL=sqlite:///./data/test.db

# 测试模式
TESTING=false

# ==================== 本地服务配置 ====================
# 本地文件存储路径
UPLOAD_PATH=./data/uploads
STATIC_PATH=./static
TEMPLATE_PATH=./templates

# 本地日志配置
LOG_FILE_PATH=./logs/local.log
LOG_MAX_SIZE=50
LOG_BACKUP_COUNT=3

# ==================== CORS配置 ====================
# 本地开发允许所有源
CORS_ORIGINS=*
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*

# ==================== 开发工具配置 ====================
# 启用API文档
ENABLE_DOCS=true

# 启用调试工具
ENABLE_DEBUG_TOOLBAR=true

# 启用性能分析
ENABLE_PROFILER=false
