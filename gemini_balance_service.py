#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini Balance Windows 服务
将 Gemini Balance API 服务作为 Windows 服务运行
"""

import os
import sys
import time
import socket
import logging
import subprocess
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
except ImportError:
    print("错误: 需要安装 pywin32 库")
    print("请运行: pip install pywin32")
    sys.exit(1)


class GeminiBalanceService(win32serviceutil.ServiceFramework):
    """Gemini Balance Windows 服务类"""
    
    # 服务配置
    _svc_name_ = "GeminiBalance"
    _svc_display_name_ = "Gemini Balance API Service"
    _svc_description_ = "Gemini API 代理和负载均衡服务"
    
    def __init__(self, args):
        """初始化服务"""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True
        self.process = None
        
        # 设置工作目录
        os.chdir(str(PROJECT_ROOT))
        
        # 配置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        log_dir = PROJECT_ROOT / "logs"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "service.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('GeminiBalanceService')
        
    def SvcStop(self):
        """停止服务"""
        self.logger.info("正在停止 Gemini Balance 服务...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False
        
        # 终止子进程
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
                self.logger.info("服务进程已正常终止")
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.logger.warning("服务进程被强制终止")
            except Exception as e:
                self.logger.error(f"终止服务进程时出错: {e}")
                
    def SvcDoRun(self):
        """运行服务"""
        self.logger.info("正在启动 Gemini Balance 服务...")
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        try:
            self.main()
        except Exception as e:
            self.logger.error(f"服务运行时出错: {e}")
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_ERROR_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, str(e))
            )
            
    def main(self):
        """主服务逻辑"""
        self.logger.info("Gemini Balance 服务已启动")
        
        # 检查端口是否可用
        if not self.is_port_available(8001):
            self.logger.error("端口 8001 已被占用，服务无法启动")
            return
            
        # 启动应用程序
        while self.is_alive:
            try:
                # 使用 py 命令启动应用
                cmd = [
                    sys.executable, 
                    str(PROJECT_ROOT / "start.py")
                ]
                
                self.logger.info(f"启动命令: {' '.join(cmd)}")
                
                # 设置环境变量
                env = os.environ.copy()
                env['PYTHONPATH'] = str(PROJECT_ROOT)
                
                self.process = subprocess.Popen(
                    cmd,
                    cwd=str(PROJECT_ROOT),
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    bufsize=1
                )
                
                self.logger.info(f"应用程序已启动，PID: {self.process.pid}")
                
                # 监控进程
                while self.is_alive and self.process.poll() is None:
                    # 等待停止信号或进程结束
                    result = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                    if result == win32event.WAIT_OBJECT_0:
                        break
                        
                # 检查进程退出状态
                if self.process.poll() is not None:
                    stdout, stderr = self.process.communicate()
                    if stdout:
                        self.logger.info(f"应用输出: {stdout}")
                    if stderr:
                        self.logger.error(f"应用错误: {stderr}")
                        
                    if self.is_alive:  # 如果不是主动停止，则重启
                        self.logger.warning("应用程序意外退出，5秒后重启...")
                        time.sleep(5)
                    
            except Exception as e:
                self.logger.error(f"启动应用程序时出错: {e}")
                if self.is_alive:
                    self.logger.info("10秒后重试...")
                    time.sleep(10)
                    
        self.logger.info("Gemini Balance 服务已停止")
        
    def is_port_available(self, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False


def install_service():
    """安装服务"""
    try:
        # 检查是否已安装
        try:
            win32serviceutil.QueryServiceStatus("GeminiBalance")
            print("服务已存在，正在更新...")
            win32serviceutil.RemoveService("GeminiBalance")
        except:
            pass
            
        # 安装服务
        win32serviceutil.InstallService(
            GeminiBalanceService,
            GeminiBalanceService._svc_name_,
            GeminiBalanceService._svc_display_name_,
            description=GeminiBalanceService._svc_description_
        )
        print("✅ Gemini Balance 服务安装成功！")
        print("服务名称: GeminiBalance")
        print("显示名称: Gemini Balance API Service")
        
    except Exception as e:
        print(f"❌ 服务安装失败: {e}")


def uninstall_service():
    """卸载服务"""
    try:
        win32serviceutil.RemoveService("GeminiBalance")
        print("✅ Gemini Balance 服务卸载成功！")
    except Exception as e:
        print(f"❌ 服务卸载失败: {e}")


def start_service():
    """启动服务"""
    try:
        win32serviceutil.StartService("GeminiBalance")
        print("✅ Gemini Balance 服务启动成功！")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")


def stop_service():
    """停止服务"""
    try:
        win32serviceutil.StopService("GeminiBalance")
        print("✅ Gemini Balance 服务停止成功！")
    except Exception as e:
        print(f"❌ 服务停止失败: {e}")


def restart_service():
    """重启服务"""
    try:
        win32serviceutil.RestartService("GeminiBalance")
        print("✅ Gemini Balance 服务重启成功！")
    except Exception as e:
        print(f"❌ 服务重启失败: {e}")


if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 作为服务运行
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(GeminiBalanceService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 命令行操作
        command = sys.argv[1].lower()
        
        if command == 'install':
            install_service()
        elif command == 'uninstall':
            uninstall_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'restart':
            restart_service()
        else:
            print("用法:")
            print("  python gemini_balance_service.py install    - 安装服务")
            print("  python gemini_balance_service.py uninstall  - 卸载服务")
            print("  python gemini_balance_service.py start      - 启动服务")
            print("  python gemini_balance_service.py stop       - 停止服务")
            print("  python gemini_balance_service.py restart    - 重启服务")
