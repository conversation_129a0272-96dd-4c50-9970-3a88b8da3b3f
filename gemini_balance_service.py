#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini Balance Windows 服务
将 Gemini Balance API 服务作为 Windows 服务运行
"""

import os
import sys
import time
import socket
import logging
import subprocess
import threading
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    import win32api
    import win32con
except ImportError:
    print("错误: 需要安装 pywin32 库")
    print("请运行: pip install pywin32")
    sys.exit(1)


class GeminiBalanceService(win32serviceutil.ServiceFramework):
    """Gemini Balance Windows 服务类"""

    # 服务配置
    _svc_name_ = "GeminiBalance"
    _svc_display_name_ = "Gemini Balance API Service"
    _svc_description_ = "Gemini API 代理和负载均衡服务"

    def __init__(self, args):
        """初始化服务"""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True
        self.process = None
        self.restart_count = 0
        self.max_restarts = 5

        # 设置工作目录
        os.chdir(str(PROJECT_ROOT))

        # 配置日志
        self.setup_logging()

    def setup_logging(self):
        """设置日志配置"""
        log_dir = PROJECT_ROOT / "logs"
        log_dir.mkdir(exist_ok=True)

        # 配置服务日志
        log_file = log_dir / "service.log"

        # 创建日志格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)

        # 配置根日志记录器
        self.logger = logging.getLogger('GeminiBalanceService')
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)

        # 防止重复添加处理器
        self.logger.propagate = False
        
    def SvcStop(self):
        """停止服务"""
        self.logger.info("正在停止 Gemini Balance 服务...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False

        # 终止子进程
        if self.process and self.process.poll() is None:
            try:
                self.logger.info("正在终止应用程序进程...")
                self.process.terminate()

                # 等待进程正常终止
                try:
                    self.process.wait(timeout=15)
                    self.logger.info("应用程序进程已正常终止")
                except subprocess.TimeoutExpired:
                    self.logger.warning("进程未在15秒内终止，强制结束...")
                    self.process.kill()
                    self.process.wait(timeout=5)
                    self.logger.warning("应用程序进程已强制终止")

            except Exception as e:
                self.logger.error(f"终止服务进程时出错: {e}")
                try:
                    self.process.kill()
                except:
                    pass

        self.logger.info("Gemini Balance 服务已停止")

    def SvcDoRun(self):
        """运行服务"""
        self.logger.info("正在启动 Gemini Balance 服务...")
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )

        try:
            self.main()
        except Exception as e:
            self.logger.error(f"服务运行时出错: {e}")
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_ERROR_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, str(e))
            )
            
    def main(self):
        """主服务逻辑"""
        self.logger.info("Gemini Balance 服务主循环已启动")

        # 检查端口是否可用
        if not self.is_port_available(8001):
            self.logger.error("端口 8001 已被占用，服务无法启动")
            return

        # 启动应用程序
        while self.is_alive and self.restart_count < self.max_restarts:
            try:
                # 构建启动命令
                python_exe = sys.executable
                start_script = str(PROJECT_ROOT / "start.py")

                cmd = [python_exe, start_script]

                self.logger.info(f"启动命令: {' '.join(cmd)}")
                self.logger.info(f"工作目录: {PROJECT_ROOT}")

                # 设置环境变量
                env = os.environ.copy()
                env['PYTHONPATH'] = str(PROJECT_ROOT)
                env['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓冲

                # 创建日志文件路径
                log_dir = PROJECT_ROOT / "logs"
                stdout_log = log_dir / "app_stdout.log"
                stderr_log = log_dir / "app_stderr.log"

                # 启动进程
                with open(stdout_log, 'a', encoding='utf-8') as stdout_file, \
                     open(stderr_log, 'a', encoding='utf-8') as stderr_file:

                    self.process = subprocess.Popen(
                        cmd,
                        cwd=str(PROJECT_ROOT),
                        env=env,
                        stdout=stdout_file,
                        stderr=stderr_file,
                        universal_newlines=True,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )

                self.logger.info(f"应用程序已启动，PID: {self.process.pid}")

                # 等待一段时间检查进程是否正常启动
                time.sleep(3)
                if self.process.poll() is not None:
                    self.logger.error(f"应用程序启动失败，退出码: {self.process.returncode}")
                    self.restart_count += 1
                    continue

                # 检查端口是否开始监听
                port_check_attempts = 0
                while port_check_attempts < 30 and self.is_alive:  # 等待最多30秒
                    if self.is_port_listening(8001):
                        self.logger.info("应用程序已成功启动，端口8001正在监听")
                        break
                    time.sleep(1)
                    port_check_attempts += 1

                if port_check_attempts >= 30:
                    self.logger.warning("应用程序启动超时，但进程仍在运行")

                # 重置重启计数器（成功启动）
                self.restart_count = 0

                # 监控进程
                while self.is_alive and self.process.poll() is None:
                    # 等待停止信号
                    result = win32event.WaitForSingleObject(self.hWaitStop, 5000)  # 5秒检查一次
                    if result == win32event.WAIT_OBJECT_0:
                        self.logger.info("收到停止信号")
                        break

                    # 定期检查进程健康状态
                    if not self.is_port_listening(8001):
                        self.logger.warning("检测到端口8001不再监听，应用程序可能出现问题")

                # 检查进程退出状态
                if self.process.poll() is not None and self.is_alive:
                    exit_code = self.process.returncode
                    self.logger.warning(f"应用程序意外退出，退出码: {exit_code}")

                    # 读取错误日志
                    try:
                        with open(stderr_log, 'r', encoding='utf-8') as f:
                            recent_errors = f.readlines()[-10:]  # 最后10行
                            if recent_errors:
                                self.logger.error("最近的错误日志:")
                                for line in recent_errors:
                                    self.logger.error(line.strip())
                    except:
                        pass

                    self.restart_count += 1
                    if self.restart_count < self.max_restarts:
                        wait_time = min(30, 5 * self.restart_count)  # 递增等待时间
                        self.logger.info(f"第{self.restart_count}次重启，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        self.logger.error(f"已达到最大重启次数({self.max_restarts})，停止重启")
                        break

            except Exception as e:
                self.logger.error(f"启动应用程序时出错: {e}")
                self.restart_count += 1
                if self.is_alive and self.restart_count < self.max_restarts:
                    wait_time = min(60, 10 * self.restart_count)
                    self.logger.info(f"异常重启，{wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    break

        if self.restart_count >= self.max_restarts:
            self.logger.error("服务重启次数过多，服务将停止")

        self.logger.info("Gemini Balance 服务主循环已结束")
        
    def is_port_available(self, port):
        """检查端口是否可用（未被占用）"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False

    def is_port_listening(self, port):
        """检查端口是否正在监听"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                return result == 0
        except:
            return False


def install_service():
    """安装服务"""
    try:
        # 检查是否已安装
        try:
            win32serviceutil.QueryServiceStatus("GeminiBalance")
            print("服务已存在，正在更新...")
            win32serviceutil.RemoveService("GeminiBalance")
        except:
            pass

        # 使用 HandleCommandLine 方式安装服务
        sys.argv = [sys.argv[0], '--startup', 'auto', 'install']
        win32serviceutil.HandleCommandLine(GeminiBalanceService)

        print("✅ Gemini Balance 服务安装成功！")
        print("服务名称: GeminiBalance")
        print("显示名称: Gemini Balance API Service")

    except Exception as e:
        print(f"❌ 服务安装失败: {e}")


def uninstall_service():
    """卸载服务"""
    try:
        win32serviceutil.RemoveService("GeminiBalance")
        print("✅ Gemini Balance 服务卸载成功！")
    except Exception as e:
        print(f"❌ 服务卸载失败: {e}")


def start_service():
    """启动服务"""
    try:
        win32serviceutil.StartService("GeminiBalance")
        # 等待服务启动
        import time
        time.sleep(2)

        # 验证服务是否真的启动了
        status = win32serviceutil.QueryServiceStatus("GeminiBalance")[1]
        if status == 4:  # SERVICE_RUNNING
            print("✅ Gemini Balance 服务启动成功！")
            print("🌐 服务地址: http://localhost:8001")
        else:
            print(f"❌ 服务启动失败，当前状态: {status}")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")


def stop_service():
    """停止服务"""
    try:
        win32serviceutil.StopService("GeminiBalance")
        print("✅ Gemini Balance 服务停止成功！")
    except Exception as e:
        print(f"❌ 服务停止失败: {e}")


def restart_service():
    """重启服务"""
    try:
        win32serviceutil.RestartService("GeminiBalance")
        print("✅ Gemini Balance 服务重启成功！")
    except Exception as e:
        print(f"❌ 服务重启失败: {e}")


if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 作为服务运行
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(GeminiBalanceService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 命令行操作
        command = sys.argv[1].lower()
        
        if command == 'install':
            install_service()
        elif command == 'uninstall':
            uninstall_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'restart':
            restart_service()
        else:
            print("用法:")
            print("  python gemini_balance_service.py install    - 安装服务")
            print("  python gemini_balance_service.py uninstall  - 卸载服务")
            print("  python gemini_balance_service.py start      - 启动服务")
            print("  python gemini_balance_service.py stop       - 停止服务")
            print("  python gemini_balance_service.py restart    - 重启服务")
