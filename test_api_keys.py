#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini API 密钥测试脚本
测试 API 密钥的可用性并输出可用的密钥到文件
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from pathlib import Path

# 要测试的 API 密钥列表
API_KEYS = [
    "AIzaSyBgOPja66M8YAFFpWg05z-GHjcS7R55kXk",
    "AIzaSyDBc2yio6i1eqPbjeX3i-k69EV1H71ZnR0",
    "AIzaSyDtDsxXpYxiM0qcZKxh7aQ8ghzqTtdjjgk",
    "AIzaSyBRBbqPsMEWtDb3yM_7Wzp2FptpFKwuqH0",
    "AIzaSyDxmrZNLNPLaW9kxUupX35qDmq1OwJaBPE",
    "AIzaSyAeM72ozqBoyCRU53QdGfj_OOxXHArv4Pg",
    "AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0",
    "AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE",
    "AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4",
    "AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30",
    "AIzaSyDFtn4L4QnG6f_T_oy-QU3RCnSjGspeKeE",
    "AIzaSyAT6rPzhAMiwOnjWWLscAfxLvdRaGFJeNg",
    "AIzaSyDqw2P__xctNxWt2JaR3IGCbk_NLU_OJDU",
    "AIzaSyCZch0ezSzEcnUeB60HZWb-uzvUt4SfW0I",
    "AIzaSyDaroOULn3FdeJASLqlosWr0RXDSZt2-x0",
    "AIzaSyCMjX6TlBujpII8WX9NOtnYW9COP9BvUzg",
    "AIzaSyANO9WBN-fT96QoMGxl62W4TkRtjdAfeYc",
    "AIzaSyDSppCltAuTuvKmMF95THHpdfCAKFbCPzI",
    "AIzaSyBC_WJ8xPmIoZRqC5HIeYLq3_-zmTdyKqk",
    "AIzaSyCvFF06evSd7KksPLvFSpJ12vRDf2kru0c",
    "AIzaSyC7OXtnf6NtqNXUWX1KkmGeYf7X-whkw_E",
    "AIzaSyDijyk9v6Jq8evWy9IksF0nwwbZ5dn7KNM",
    "AIzaSyDYIKupXMUQOoopV0ZQrPajs8ovGP4ztzU",
    "AIzaSyBkSCCGQyFQRDtuKlulZ9eOE7sIQb3Asow",
    "AIzaSyCeuIEay318flNMf7-WDzuk1FZkaY8Yblg",
    "AIzaSyDlbsFUDcCVoDstSweBReZLqKmEulWcfh8",
    "AIzaSyB-_fMhnWfGtRhdPAbCilJi6ldV12ehw9I",
    "AIzaSyCJesd2BfnNpjja426h81I4p5Z-b3rM1wc"
]

# Gemini API 配置
GEMINI_API_BASE = "https://generativelanguage.googleapis.com/v1beta"
MODEL_NAME = "gemini-2.5-flash"
TEST_MESSAGE = "Hello, this is a test message. Please respond with 'API key is working'."

class APIKeyTester:
    def __init__(self):
        self.valid_keys = []
        self.invalid_keys = []
        self.error_details = {}
        
    async def test_api_key(self, session, api_key, index):
        """测试单个 API 密钥"""
        try:
            url = f"{GEMINI_API_BASE}/models/{MODEL_NAME}:generateContent"
            
            headers = {
                "Content-Type": "application/json",
                "x-goog-api-key": api_key
            }
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": TEST_MESSAGE
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 50
                }
            }
            
            print(f"[{index+1:2d}/{len(API_KEYS)}] 测试密钥: {api_key[:20]}...")
            
            async with session.post(url, headers=headers, json=payload, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'candidates' in data and len(data['candidates']) > 0:
                        content = data['candidates'][0].get('content', {})
                        if 'parts' in content and len(content['parts']) > 0:
                            response_text = content['parts'][0].get('text', '')
                            print(f"    ✅ 可用 - 响应: {response_text[:30]}...")
                            self.valid_keys.append(api_key)
                            return True
                
                # 如果到这里说明响应格式不正确
                error_text = await response.text()
                print(f"    ❌ 响应格式错误 - 状态码: {response.status}")
                self.invalid_keys.append(api_key)
                self.error_details[api_key] = f"Status: {response.status}, Response: {error_text[:100]}"
                return False
                
        except asyncio.TimeoutError:
            print(f"    ❌ 超时")
            self.invalid_keys.append(api_key)
            self.error_details[api_key] = "Timeout"
            return False
        except Exception as e:
            print(f"    ❌ 错误: {str(e)[:50]}")
            self.invalid_keys.append(api_key)
            self.error_details[api_key] = str(e)[:100]
            return False
    
    async def test_all_keys(self):
        """测试所有 API 密钥"""
        print(f"开始测试 {len(API_KEYS)} 个 API 密钥...")
        print(f"测试模型: {MODEL_NAME}")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 创建 HTTP 会话
        connector = aiohttp.TCPConnector(limit=10)  # 限制并发连接数
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 创建任务列表，但限制并发数
            semaphore = asyncio.Semaphore(5)  # 最多同时测试5个密钥
            
            async def test_with_semaphore(api_key, index):
                async with semaphore:
                    return await self.test_api_key(session, api_key, index)
            
            # 执行所有测试
            tasks = [test_with_semaphore(key, i) for i, key in enumerate(API_KEYS)]
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def save_results(self):
        """保存测试结果到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存可用的 API 密钥
        valid_keys_file = f"valid_api_keys_{timestamp}.txt"
        with open(valid_keys_file, 'w', encoding='utf-8') as f:
            f.write(f"# Gemini API 可用密钥列表\n")
            f.write(f"# 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 测试模型: {MODEL_NAME}\n")
            f.write(f"# 可用密钥数量: {len(self.valid_keys)}/{len(API_KEYS)}\n")
            f.write(f"# 成功率: {len(self.valid_keys)/len(API_KEYS)*100:.1f}%\n")
            f.write("\n")
            
            for key in self.valid_keys:
                f.write(f"{key}\n")
        
        # 保存详细测试报告
        report_file = f"api_test_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Gemini API 密钥测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试模型: {MODEL_NAME}\n")
            f.write(f"总密钥数: {len(API_KEYS)}\n")
            f.write(f"可用密钥: {len(self.valid_keys)}\n")
            f.write(f"无效密钥: {len(self.invalid_keys)}\n")
            f.write(f"成功率: {len(self.valid_keys)/len(API_KEYS)*100:.1f}%\n")
            f.write("\n")
            
            f.write("可用密钥列表:\n")
            f.write("-" * 30 + "\n")
            for i, key in enumerate(self.valid_keys, 1):
                f.write(f"{i:2d}. {key}\n")
            
            f.write("\n无效密钥详情:\n")
            f.write("-" * 30 + "\n")
            for i, key in enumerate(self.invalid_keys, 1):
                error = self.error_details.get(key, "未知错误")
                f.write(f"{i:2d}. {key}\n")
                f.write(f"    错误: {error}\n")
        
        return valid_keys_file, report_file
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print(f"总密钥数: {len(API_KEYS)}")
        print(f"可用密钥: {len(self.valid_keys)}")
        print(f"无效密钥: {len(self.invalid_keys)}")
        print(f"成功率: {len(self.valid_keys)/len(API_KEYS)*100:.1f}%")
        
        if self.valid_keys:
            print(f"\n✅ 可用密钥 ({len(self.valid_keys)}个):")
            for i, key in enumerate(self.valid_keys, 1):
                print(f"  {i:2d}. {key}")
        
        if self.invalid_keys:
            print(f"\n❌ 无效密钥 ({len(self.invalid_keys)}个):")
            for i, key in enumerate(self.invalid_keys[:5], 1):  # 只显示前5个
                print(f"  {i:2d}. {key[:20]}... - {self.error_details.get(key, '未知错误')[:30]}")
            if len(self.invalid_keys) > 5:
                print(f"  ... 还有 {len(self.invalid_keys) - 5} 个无效密钥")

async def main():
    """主函数"""
    tester = APIKeyTester()
    
    try:
        await tester.test_all_keys()
        tester.print_summary()
        
        # 保存结果
        valid_file, report_file = tester.save_results()
        
        print(f"\n📁 结果已保存:")
        print(f"  可用密钥: {valid_file}")
        print(f"  详细报告: {report_file}")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
