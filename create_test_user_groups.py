#!/usr/bin/env python3
"""
创建测试用户组数据
用于测试用户组管理功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.sqlite_operations import create_user_group, list_user_groups
from app.log.logger import get_logger

logger = get_logger(__name__)

async def create_test_groups():
    """创建测试用户组"""
    test_groups = [
        {
            "group_id": "test-group-1",
            "group_name": "测试用户组1",
            "description": "这是第一个测试用户组",
            "priority": 8,
            "auto_fallback": True,
            "context_isolation": True,
            "no_fallback_models": ["gpt-4"]
        },
        {
            "group_id": "test-group-2", 
            "group_name": "测试用户组2",
            "description": "这是第二个测试用户组",
            "priority": 5,
            "auto_fallback": False,
            "context_isolation": True,
            "no_fallback_models": []
        },
        {
            "group_id": "premium-users",
            "group_name": "高级用户组",
            "description": "高级用户专用组，享受优先服务",
            "priority": 9,
            "auto_fallback": True,
            "context_isolation": True,
            "no_fallback_models": []
        },
        {
            "group_id": "basic-users",
            "group_name": "基础用户组", 
            "description": "基础用户组，标准服务",
            "priority": 3,
            "auto_fallback": True,
            "context_isolation": False,
            "no_fallback_models": ["gpt-4", "claude-3"]
        }
    ]
    
    created_count = 0
    for group_data in test_groups:
        try:
            success = await create_user_group(**group_data)
            if success:
                print(f"✅ 创建用户组成功: {group_data['group_id']}")
                created_count += 1
            else:
                print(f"❌ 创建用户组失败: {group_data['group_id']}")
        except Exception as e:
            print(f"❌ 创建用户组异常 {group_data['group_id']}: {str(e)}")
    
    return created_count

async def main():
    """主函数"""
    try:
        print("🚀 开始创建测试用户组...")
        
        # 检查现有用户组
        existing_groups = await list_user_groups()
        print(f"📊 当前用户组数量: {len(existing_groups)}")
        
        # 创建测试用户组
        created_count = await create_test_groups()
        print(f"✅ 成功创建 {created_count} 个测试用户组")
        
        # 验证创建结果
        final_groups = await list_user_groups()
        print(f"📊 最终用户组数量: {len(final_groups)}")
        
        print("🎉 测试用户组创建完成！")
        
    except Exception as e:
        print(f"❌ 创建测试用户组失败: {str(e)}")
        logger.error(f"Create test user groups failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
