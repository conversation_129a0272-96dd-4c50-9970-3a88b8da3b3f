"""
HTTP连接池管理器
优化API请求的连接复用和性能
"""
import asyncio
import httpx
from typing import Dict, Optional
from datetime import datetime, timedelta

from app.config.config import settings
from app.log.logger import get_connection_pool_logger

logger = get_connection_pool_logger()


class ConnectionPoolManager:
    """HTTP连接池管理器"""
    
    def __init__(self):
        self.pools: Dict[str, httpx.AsyncClient] = {}
        self.pool_stats: Dict[str, Dict] = {}
        self._initialized = False
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化连接池"""
        async with self._lock:
            if self._initialized:
                return
            
            # Gemini API连接池
            self.pools['gemini'] = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_keepalive_connections=50,  # 保持活跃连接数
                    max_connections=200,           # 最大连接数
                    keepalive_expiry=60           # 连接保持时间(秒)
                ),
                timeout=httpx.Timeout(
                    connect=10.0,    # 连接超时
                    read=300.0,      # 读取超时
                    write=30.0,      # 写入超时
                    pool=5.0         # 连接池超时
                ),
                http2=True,          # 启用HTTP/2
                follow_redirects=True
            )
            
            # OpenAI兼容API连接池
            self.pools['openai'] = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_keepalive_connections=30,
                    max_connections=100,
                    keepalive_expiry=45
                ),
                timeout=httpx.Timeout(
                    connect=8.0,
                    read=300.0,
                    write=30.0,
                    pool=5.0
                ),
                http2=True,
                follow_redirects=True
            )
            
            # Vertex AI连接池
            self.pools['vertex'] = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_keepalive_connections=40,
                    max_connections=150,
                    keepalive_expiry=60
                ),
                timeout=httpx.Timeout(
                    connect=10.0,
                    read=300.0,
                    write=30.0,
                    pool=5.0
                ),
                http2=True,
                follow_redirects=True
            )
            
            # 文件上传专用连接池
            self.pools['upload'] = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_keepalive_connections=20,
                    max_connections=50,
                    keepalive_expiry=30
                ),
                timeout=httpx.Timeout(
                    connect=15.0,
                    read=600.0,      # 文件上传需要更长超时
                    write=300.0,
                    pool=10.0
                ),
                http2=False,         # 文件上传使用HTTP/1.1更稳定
                follow_redirects=True
            )
            
            # 初始化统计信息
            for pool_name in self.pools.keys():
                self.pool_stats[pool_name] = {
                    'requests_count': 0,
                    'errors_count': 0,
                    'total_time': 0.0,
                    'avg_response_time': 0.0,
                    'last_used': datetime.now()
                }
            
            self._initialized = True
            logger.info("Connection pools initialized successfully")
    
    async def get_client(self, pool_type: str = 'gemini') -> httpx.AsyncClient:
        """获取指定类型的HTTP客户端"""
        if not self._initialized:
            await self.initialize()
        
        client = self.pools.get(pool_type)
        if not client:
            logger.warning(f"Pool type '{pool_type}' not found, using default 'gemini' pool")
            client = self.pools['gemini']
        
        # 更新使用统计
        self.pool_stats[pool_type]['last_used'] = datetime.now()
        
        return client
    
    async def make_request(
        self,
        method: str,
        url: str,
        pool_type: str = 'gemini',
        **kwargs
    ) -> httpx.Response:
        """使用连接池发起请求"""
        client = await self.get_client(pool_type)
        start_time = datetime.now()
        
        try:
            # 添加代理支持
            if settings.PROXIES and pool_type in ['gemini', 'openai', 'vertex']:
                if 'proxy' not in kwargs:
                    proxy = self._select_proxy(kwargs.get('api_key', ''))
                    if proxy:
                        kwargs['proxy'] = proxy
            
            response = await client.request(method, url, **kwargs)
            
            # 更新成功统计
            self._update_stats(pool_type, start_time, success=True)
            
            return response
            
        except Exception as e:
            # 更新错误统计
            self._update_stats(pool_type, start_time, success=False)
            logger.error(f"Request failed for pool '{pool_type}': {str(e)}")
            raise
    
    def _select_proxy(self, api_key: str) -> Optional[str]:
        """选择代理服务器"""
        if not settings.PROXIES:
            return None
        
        if settings.PROXIES_USE_CONSISTENCY_HASH_BY_API_KEY and api_key:
            # 基于API密钥的一致性哈希
            proxy_index = hash(api_key) % len(settings.PROXIES)
            return settings.PROXIES[proxy_index]
        else:
            # 轮询选择
            import random
            return random.choice(settings.PROXIES)
    
    def _update_stats(self, pool_type: str, start_time: datetime, success: bool):
        """更新连接池统计信息"""
        stats = self.pool_stats[pool_type]
        request_time = (datetime.now() - start_time).total_seconds()
        
        stats['requests_count'] += 1
        stats['total_time'] += request_time
        
        if success:
            stats['avg_response_time'] = stats['total_time'] / stats['requests_count']
        else:
            stats['errors_count'] += 1
    
    async def get_pool_stats(self) -> Dict:
        """获取连接池统计信息"""
        if not self._initialized:
            return {"error": "Connection pools not initialized"}
        
        stats = {}
        for pool_name, pool_stats in self.pool_stats.items():
            client = self.pools[pool_name]
            
            stats[pool_name] = {
                **pool_stats,
                'pool_info': {
                    'max_connections': client._limits.max_connections,
                    'max_keepalive_connections': client._limits.max_keepalive_connections,
                    'keepalive_expiry': client._limits.keepalive_expiry,
                },
                'error_rate': (
                    pool_stats['errors_count'] / max(pool_stats['requests_count'], 1) * 100
                ),
                'health_status': self._get_pool_health(pool_name)
            }
        
        return stats
    
    def _get_pool_health(self, pool_name: str) -> str:
        """评估连接池健康状态"""
        stats = self.pool_stats[pool_name]
        
        # 检查错误率
        error_rate = stats['errors_count'] / max(stats['requests_count'], 1)
        if error_rate > 0.1:  # 错误率超过10%
            return "unhealthy"
        elif error_rate > 0.05:  # 错误率超过5%
            return "warning"
        
        # 检查响应时间
        if stats['avg_response_time'] > 10.0:  # 平均响应时间超过10秒
            return "warning"
        
        # 检查最后使用时间
        last_used = stats['last_used']
        if datetime.now() - last_used > timedelta(hours=1):
            return "idle"
        
        return "healthy"
    
    async def close_all(self):
        """关闭所有连接池"""
        for pool_name, client in self.pools.items():
            try:
                await client.aclose()
                logger.info(f"Closed connection pool: {pool_name}")
            except Exception as e:
                logger.error(f"Error closing pool {pool_name}: {str(e)}")
        
        self.pools.clear()
        self.pool_stats.clear()
        self._initialized = False


# 全局连接池管理器实例
connection_pool_manager = ConnectionPoolManager()


async def get_connection_pool_manager() -> ConnectionPoolManager:
    """获取连接池管理器实例"""
    if not connection_pool_manager._initialized:
        await connection_pool_manager.initialize()
    return connection_pool_manager
