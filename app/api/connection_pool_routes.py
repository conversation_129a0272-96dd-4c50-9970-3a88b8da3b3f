"""
连接池监控API路由
提供连接池状态查询和管理功能
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import asyncio

from app.core.connection_pool import get_connection_pool_manager
from app.log.logger import get_connection_pool_logger

logger = get_connection_pool_logger()
router = APIRouter(prefix="/connection-pool", tags=["连接池管理"])


@router.get("/health", summary="连接池健康检查")
async def get_connection_pool_health() -> Dict[str, Any]:
    """
    获取连接池健康状态
    
    Returns:
        Dict[str, Any]: 连接池健康状态信息
    """
    try:
        pool_manager = await get_connection_pool_manager()
        stats = await pool_manager.get_pool_stats()
        
        # 计算整体健康状态
        overall_health = "healthy"
        unhealthy_pools = []
        warning_pools = []
        
        for pool_name, pool_info in stats.items():
            health_status = pool_info.get('health_status', 'unknown')
            if health_status == 'unhealthy':
                overall_health = "unhealthy"
                unhealthy_pools.append(pool_name)
            elif health_status == 'warning':
                if overall_health == "healthy":
                    overall_health = "warning"
                warning_pools.append(pool_name)
        
        return {
            "status": "success",
            "overall_health": overall_health,
            "pools": stats,
            "summary": {
                "total_pools": len(stats),
                "healthy_pools": len([p for p in stats.values() if p.get('health_status') == 'healthy']),
                "warning_pools": len(warning_pools),
                "unhealthy_pools": len(unhealthy_pools),
                "warning_pool_names": warning_pools,
                "unhealthy_pool_names": unhealthy_pools
            },
            "recommendations": _get_health_recommendations(stats)
        }
        
    except Exception as e:
        logger.error(f"Failed to get connection pool health: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get connection pool health: {str(e)}")


@router.get("/stats", summary="连接池统计信息")
async def get_connection_pool_stats() -> Dict[str, Any]:
    """
    获取详细的连接池统计信息
    
    Returns:
        Dict[str, Any]: 连接池统计信息
    """
    try:
        pool_manager = await get_connection_pool_manager()
        stats = await pool_manager.get_pool_stats()
        
        # 计算汇总统计
        total_requests = sum(pool['requests_count'] for pool in stats.values())
        total_errors = sum(pool['errors_count'] for pool in stats.values())
        avg_response_time = sum(pool['avg_response_time'] for pool in stats.values()) / len(stats) if stats else 0
        
        return {
            "status": "success",
            "pools": stats,
            "summary": {
                "total_requests": total_requests,
                "total_errors": total_errors,
                "overall_error_rate": (total_errors / max(total_requests, 1)) * 100,
                "average_response_time": avg_response_time,
                "active_pools": len(stats)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get connection pool stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get connection pool stats: {str(e)}")


@router.post("/test/{pool_type}", summary="测试连接池")
async def test_connection_pool(pool_type: str) -> Dict[str, Any]:
    """
    测试指定连接池的连接性能
    
    Args:
        pool_type: 连接池类型 (gemini, openai, vertex, upload)
        
    Returns:
        Dict[str, Any]: 测试结果
    """
    try:
        pool_manager = await get_connection_pool_manager()
        
        # 验证连接池类型
        if pool_type not in ['gemini', 'openai', 'vertex', 'upload']:
            raise HTTPException(status_code=400, detail=f"Invalid pool type: {pool_type}")
        
        # 执行连接测试
        start_time = asyncio.get_event_loop().time()
        
        try:
            client = await pool_manager.get_client(pool_type)
            # 简单的连接测试 - 尝试获取客户端
            test_success = client is not None
            
            end_time = asyncio.get_event_loop().time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            return {
                "status": "success",
                "pool_type": pool_type,
                "test_result": {
                    "success": test_success,
                    "response_time_ms": response_time,
                    "client_available": test_success
                },
                "message": f"Connection pool '{pool_type}' test completed"
            }
            
        except Exception as test_error:
            end_time = asyncio.get_event_loop().time()
            response_time = (end_time - start_time) * 1000
            
            return {
                "status": "error",
                "pool_type": pool_type,
                "test_result": {
                    "success": False,
                    "response_time_ms": response_time,
                    "error": str(test_error)
                },
                "message": f"Connection pool '{pool_type}' test failed"
            }
        
    except Exception as e:
        logger.error(f"Failed to test connection pool {pool_type}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to test connection pool: {str(e)}")


@router.post("/reinitialize", summary="重新初始化连接池")
async def reinitialize_connection_pools() -> Dict[str, Any]:
    """
    重新初始化所有连接池
    
    Returns:
        Dict[str, Any]: 重新初始化结果
    """
    try:
        pool_manager = await get_connection_pool_manager()
        
        # 关闭现有连接池
        await pool_manager.close_all()
        
        # 重新初始化
        await pool_manager.initialize()
        
        # 获取新的统计信息
        stats = await pool_manager.get_pool_stats()
        
        return {
            "status": "success",
            "message": "Connection pools reinitialized successfully",
            "pools": list(stats.keys()),
            "timestamp": asyncio.get_event_loop().time()
        }
        
    except Exception as e:
        logger.error(f"Failed to reinitialize connection pools: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to reinitialize connection pools: {str(e)}")


def _get_health_recommendations(stats: Dict[str, Any]) -> list:
    """
    基于连接池统计生成健康建议
    
    Args:
        stats: 连接池统计信息
        
    Returns:
        list: 建议列表
    """
    recommendations = []
    
    for pool_name, pool_info in stats.items():
        error_rate = pool_info.get('error_rate', 0)
        avg_response_time = pool_info.get('avg_response_time', 0)
        health_status = pool_info.get('health_status', 'unknown')
        
        if error_rate > 10:
            recommendations.append(f"连接池 '{pool_name}' 错误率过高 ({error_rate:.1f}%)，建议检查网络连接或API配置")
        
        if avg_response_time > 10:
            recommendations.append(f"连接池 '{pool_name}' 响应时间过长 ({avg_response_time:.1f}s)，建议优化网络或增加超时时间")
        
        if health_status == 'idle':
            recommendations.append(f"连接池 '{pool_name}' 长时间未使用，可以考虑调整连接保持时间")
        
        if health_status == 'unhealthy':
            recommendations.append(f"连接池 '{pool_name}' 状态不健康，建议重新初始化或检查配置")
    
    if not recommendations:
        recommendations.append("所有连接池运行正常，无需特殊操作")
    
    return recommendations
