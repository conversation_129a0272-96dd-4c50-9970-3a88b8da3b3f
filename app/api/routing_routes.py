"""
智能路由管理API
提供路由配置、监控和优化功能
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from enum import Enum

from app.service.routing.intelligent_router import get_intelligent_router, LoadBalancingStrategy, EndpointStatus
from app.log.logger import get_logger

logger = get_logger("routing_api")
router = APIRouter(prefix="/routing", tags=["智能路由"])


class StrategyUpdate(BaseModel):
    """策略更新请求模型"""
    strategy: str  # round_robin, weighted_round_robin, least_connections, response_time, adaptive


class EndpointConfig(BaseModel):
    """端点配置模型"""
    url: str
    provider: str
    region: str = "default"
    priority: int = 1
    weight: float = 1.0
    max_concurrent: int = 100
    timeout: float = 30.0


@router.get("/health", summary="路由系统健康检查")
async def get_routing_health() -> Dict[str, Any]:
    """
    获取路由系统健康状态
    
    Returns:
        Dict[str, Any]: 路由系统健康信息
    """
    try:
        router_instance = await get_intelligent_router()
        stats = router_instance.get_routing_stats()
        
        # 计算整体健康状态
        total_endpoints = 0
        healthy_endpoints = 0
        warning_endpoints = 0
        unhealthy_endpoints = 0
        
        for provider, endpoints in stats['endpoints'].items():
            for endpoint in endpoints:
                total_endpoints += 1
                status = endpoint['status']
                if status == 'healthy':
                    healthy_endpoints += 1
                elif status == 'warning':
                    warning_endpoints += 1
                elif status == 'unhealthy':
                    unhealthy_endpoints += 1
        
        overall_health = "healthy"
        if unhealthy_endpoints > 0:
            overall_health = "unhealthy"
        elif warning_endpoints > 0:
            overall_health = "warning"
        
        return {
            "status": "success",
            "overall_health": overall_health,
            "summary": {
                "total_endpoints": total_endpoints,
                "healthy_endpoints": healthy_endpoints,
                "warning_endpoints": warning_endpoints,
                "unhealthy_endpoints": unhealthy_endpoints,
                "success_rate": stats['success_rate'],
                "total_requests": stats['total_requests']
            },
            "routing_stats": stats,
            "timestamp": "2025-01-21T04:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Failed to get routing health: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get routing health: {str(e)}")


@router.get("/stats", summary="获取路由统计信息")
async def get_routing_stats() -> Dict[str, Any]:
    """
    获取详细的路由统计信息
    
    Returns:
        Dict[str, Any]: 路由统计信息
    """
    try:
        router_instance = await get_intelligent_router()
        stats = router_instance.get_routing_stats()
        
        # 计算额外的统计信息
        provider_stats = {}
        for provider, endpoints in stats['endpoints'].items():
            provider_total_load = sum(ep['current_load'] for ep in endpoints)
            provider_avg_response_time = sum(ep['avg_response_time'] for ep in endpoints) / len(endpoints) if endpoints else 0
            provider_success_rate = sum(ep['success_rate'] for ep in endpoints) / len(endpoints) if endpoints else 0
            
            provider_stats[provider] = {
                "endpoint_count": len(endpoints),
                "total_current_load": provider_total_load,
                "avg_response_time": provider_avg_response_time,
                "avg_success_rate": provider_success_rate,
                "healthy_endpoints": len([ep for ep in endpoints if ep['status'] == 'healthy'])
            }
        
        return {
            "status": "success",
            "routing_stats": stats,
            "provider_stats": provider_stats,
            "recommendations": _generate_routing_recommendations(stats),
            "timestamp": "2025-01-21T04:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Failed to get routing stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get routing stats: {str(e)}")


@router.post("/strategy", summary="更新负载均衡策略")
async def update_load_balancing_strategy(strategy_update: StrategyUpdate) -> Dict[str, Any]:
    """
    更新负载均衡策略
    
    Args:
        strategy_update: 策略更新信息
        
    Returns:
        Dict[str, Any]: 更新结果
    """
    try:
        # 验证策略
        valid_strategies = [s.value for s in LoadBalancingStrategy]
        if strategy_update.strategy not in valid_strategies:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid strategy. Valid options: {valid_strategies}"
            )
        
        router_instance = await get_intelligent_router()
        new_strategy = LoadBalancingStrategy(strategy_update.strategy)
        router_instance.set_strategy(new_strategy)
        
        return {
            "status": "success",
            "message": f"Load balancing strategy updated to: {strategy_update.strategy}",
            "previous_strategy": router_instance.strategy.value,
            "new_strategy": strategy_update.strategy,
            "timestamp": "2025-01-21T04:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Failed to update strategy: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update strategy: {str(e)}")


@router.post("/endpoints/{provider}", summary="添加端点")
async def add_endpoint(provider: str, endpoint_config: EndpointConfig) -> Dict[str, Any]:
    """
    为指定提供商添加端点
    
    Args:
        provider: 提供商名称
        endpoint_config: 端点配置
        
    Returns:
        Dict[str, Any]: 添加结果
    """
    try:
        router_instance = await get_intelligent_router()
        
        # 创建端点信息
        from app.service.routing.intelligent_router import EndpointInfo
        endpoint_info = EndpointInfo(
            url=endpoint_config.url,
            provider=endpoint_config.provider,
            region=endpoint_config.region,
            priority=endpoint_config.priority,
            weight=endpoint_config.weight,
            max_concurrent=endpoint_config.max_concurrent,
            timeout=endpoint_config.timeout
        )
        
        router_instance.add_endpoint(provider, endpoint_info)
        
        return {
            "status": "success",
            "message": f"Endpoint added for provider: {provider}",
            "endpoint": {
                "url": endpoint_config.url,
                "provider": endpoint_config.provider,
                "region": endpoint_config.region,
                "priority": endpoint_config.priority,
                "weight": endpoint_config.weight
            },
            "timestamp": "2025-01-21T04:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Failed to add endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add endpoint: {str(e)}")


@router.delete("/endpoints/{provider}", summary="删除端点")
async def remove_endpoint(
    provider: str, 
    url: str = Query(..., description="要删除的端点URL")
) -> Dict[str, Any]:
    """
    删除指定提供商的端点
    
    Args:
        provider: 提供商名称
        url: 端点URL
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        router_instance = await get_intelligent_router()
        success = router_instance.remove_endpoint(provider, url)
        
        if success:
            return {
                "status": "success",
                "message": f"Endpoint removed for provider: {provider}",
                "removed_url": url,
                "timestamp": "2025-01-21T04:00:00Z"
            }
        else:
            raise HTTPException(
                status_code=404, 
                detail=f"Endpoint not found: {url} for provider: {provider}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to remove endpoint: {str(e)}")


@router.get("/endpoints", summary="获取所有端点信息")
async def get_all_endpoints() -> Dict[str, Any]:
    """
    获取所有端点的详细信息
    
    Returns:
        Dict[str, Any]: 所有端点信息
    """
    try:
        router_instance = await get_intelligent_router()
        stats = router_instance.get_routing_stats()
        
        return {
            "status": "success",
            "endpoints": stats['endpoints'],
            "summary": {
                "total_providers": len(stats['endpoints']),
                "total_endpoints": sum(len(endpoints) for endpoints in stats['endpoints'].values()),
                "current_strategy": stats['strategy']
            },
            "timestamp": "2025-01-21T04:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Failed to get endpoints: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get endpoints: {str(e)}")


@router.post("/test/{provider}", summary="测试提供商路由")
async def test_provider_routing(provider: str) -> Dict[str, Any]:
    """
    测试指定提供商的路由选择
    
    Args:
        provider: 提供商名称
        
    Returns:
        Dict[str, Any]: 测试结果
    """
    try:
        router_instance = await get_intelligent_router()
        
        # 模拟请求信息
        request_info = {
            "model": "test-model",
            "estimated_tokens": 1000,
            "priority": "normal"
        }
        
        # 尝试路由请求
        selected_endpoint = await router_instance.route_request(provider, request_info)
        
        if selected_endpoint:
            # 模拟请求完成
            await router_instance.release_endpoint(selected_endpoint, success=True, response_time=0.5)
            
            return {
                "status": "success",
                "provider": provider,
                "selected_endpoint": {
                    "url": selected_endpoint.url,
                    "region": selected_endpoint.region,
                    "priority": selected_endpoint.priority,
                    "current_load": selected_endpoint.current_load,
                    "status": selected_endpoint.status.value
                },
                "test_result": "routing_successful",
                "timestamp": "2025-01-21T04:00:00Z"
            }
        else:
            return {
                "status": "warning",
                "provider": provider,
                "test_result": "no_available_endpoints",
                "message": f"No available endpoints for provider: {provider}",
                "timestamp": "2025-01-21T04:00:00Z"
            }
        
    except Exception as e:
        logger.error(f"Failed to test routing for {provider}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to test routing: {str(e)}")


@router.get("/optimization/suggestions", summary="获取路由优化建议")
async def get_optimization_suggestions() -> Dict[str, Any]:
    """
    获取路由优化建议
    
    Returns:
        Dict[str, Any]: 优化建议
    """
    try:
        router_instance = await get_intelligent_router()
        stats = router_instance.get_routing_stats()
        
        suggestions = _generate_optimization_suggestions(stats)
        
        return {
            "status": "success",
            "optimization_suggestions": suggestions,
            "current_performance": {
                "success_rate": stats['success_rate'],
                "total_requests": stats['total_requests'],
                "strategy": stats['strategy']
            },
            "timestamp": "2025-01-21T04:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Failed to get optimization suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get optimization suggestions: {str(e)}")


def _generate_routing_recommendations(stats: Dict[str, Any]) -> List[str]:
    """生成路由建议"""
    recommendations = []
    
    success_rate = stats.get('success_rate', 0)
    if success_rate < 95:
        recommendations.append(f"成功率较低 ({success_rate:.1f}%)，建议检查端点健康状态")
    
    for provider, endpoints in stats['endpoints'].items():
        unhealthy_count = len([ep for ep in endpoints if ep['status'] == 'unhealthy'])
        if unhealthy_count > 0:
            recommendations.append(f"提供商 {provider} 有 {unhealthy_count} 个不健康端点")
        
        high_load_count = len([ep for ep in endpoints if ep['current_load'] > ep['max_concurrent'] * 0.8])
        if high_load_count > 0:
            recommendations.append(f"提供商 {provider} 有 {high_load_count} 个高负载端点")
    
    if not recommendations:
        recommendations.append("路由系统运行正常，无需特殊操作")
    
    return recommendations


def _generate_optimization_suggestions(stats: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成优化建议"""
    suggestions = []
    
    # 策略优化建议
    current_strategy = stats.get('strategy', '')
    success_rate = stats.get('success_rate', 0)
    
    if success_rate < 90:
        suggestions.append({
            "type": "strategy",
            "priority": "high",
            "title": "考虑切换到自适应策略",
            "description": "当前成功率较低，自适应策略可以更好地处理端点故障",
            "action": "将策略切换为 'adaptive'"
        })
    
    # 端点优化建议
    for provider, endpoints in stats['endpoints'].items():
        avg_response_time = sum(ep['avg_response_time'] for ep in endpoints) / len(endpoints) if endpoints else 0
        
        if avg_response_time > 2.0:
            suggestions.append({
                "type": "performance",
                "priority": "medium",
                "title": f"优化 {provider} 提供商性能",
                "description": f"平均响应时间 {avg_response_time:.2f}s 较高",
                "action": f"考虑添加更多 {provider} 端点或优化网络配置"
            })
        
        healthy_count = len([ep for ep in endpoints if ep['status'] == 'healthy'])
        if healthy_count < 2:
            suggestions.append({
                "type": "reliability",
                "priority": "high",
                "title": f"增加 {provider} 提供商冗余",
                "description": f"只有 {healthy_count} 个健康端点，存在单点故障风险",
                "action": f"为 {provider} 添加更多备用端点"
            })
    
    if not suggestions:
        suggestions.append({
            "type": "status",
            "priority": "low",
            "title": "系统运行良好",
            "description": "路由系统当前运行状态良好，无需特殊优化",
            "action": "继续监控系统性能"
        })
    
    return suggestions
