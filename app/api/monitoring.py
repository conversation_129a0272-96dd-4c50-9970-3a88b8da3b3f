from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from app.service.model.quota_monitor import quota_monitor
from app.service.model.model_switch_service import model_switch_service
from app.service.cache.intelligent_cache import intelligent_cache
from app.log.logger import Logger

logger = Logger.setup_logger(__name__)

router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])


@router.get("/quota/overview")
async def get_quota_overview() -> Dict[str, Any]:
    """获取配额使用概览"""
    try:
        # 获取所有模型的配额使用情况
        overview = {
            "total_models": len(quota_monitor.QUOTA_LIMITS),
            "monitored_combinations": len(quota_monitor.usage_tracker),
            "models": []
        }
        
        for model, limits in quota_monitor.QUOTA_LIMITS.items():
            model_data = {
                "model": model,
                "limits": limits,
                "active_keys": 0,
                "total_usage": {
                    "rpm": 0,
                    "tpm": 0,
                    "rpd": 0
                },
                "peak_usage": {
                    "rpm": 0,
                    "tpm": 0,
                    "rpd": 0
                },
                "status": "healthy"
            }
            
            # 统计该模型的使用情况
            for key, usage in quota_monitor.usage_tracker.items():
                if usage.model == model:
                    model_data["active_keys"] += 1
                    model_data["total_usage"]["rpm"] += usage.current_minute_requests
                    model_data["total_usage"]["tpm"] += usage.current_minute_tokens
                    model_data["total_usage"]["rpd"] += usage.current_day_requests
                    
                    # 计算峰值使用率
                    rpm_rate = (usage.current_minute_requests / limits["rpm"]) * 100
                    tpm_rate = (usage.current_minute_tokens / limits["tpm"]) * 100
                    rpd_rate = (usage.current_day_requests / limits["rpd"]) * 100
                    
                    model_data["peak_usage"]["rpm"] = max(model_data["peak_usage"]["rpm"], rpm_rate)
                    model_data["peak_usage"]["tpm"] = max(model_data["peak_usage"]["tpm"], tpm_rate)
                    model_data["peak_usage"]["rpd"] = max(model_data["peak_usage"]["rpd"], rpd_rate)
            
            # 确定状态
            max_usage = max(
                model_data["peak_usage"]["rpm"],
                model_data["peak_usage"]["tpm"],
                model_data["peak_usage"]["rpd"]
            )
            
            if max_usage > 90:
                model_data["status"] = "critical"
            elif max_usage > 70:
                model_data["status"] = "warning"
            else:
                model_data["status"] = "healthy"
            
            overview["models"].append(model_data)
        
        return overview
        
    except Exception as e:
        logger.error(f"Error getting quota overview: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/quota/details/{model}")
async def get_model_quota_details(model: str, api_key_suffix: Optional[str] = None) -> Dict[str, Any]:
    """获取特定模型的详细配额信息"""
    try:
        if model not in quota_monitor.QUOTA_LIMITS:
            raise HTTPException(status_code=404, detail=f"Model {model} not found")
        
        details = {
            "model": model,
            "limits": quota_monitor.QUOTA_LIMITS[model],
            "api_keys": []
        }
        
        for key, usage in quota_monitor.usage_tracker.items():
            if usage.model == model:
                if api_key_suffix and not usage.api_key.endswith(api_key_suffix):
                    continue
                
                api_key_data = {
                    "api_key_suffix": usage.api_key[-4:],
                    "current_usage": {
                        "rpm": usage.current_minute_requests,
                        "tpm": usage.current_minute_tokens,
                        "rpd": usage.current_day_requests
                    },
                    "usage_percentage": {
                        "rpm": (usage.current_minute_requests / details["limits"]["rpm"]) * 100,
                        "tpm": (usage.current_minute_tokens / details["limits"]["tpm"]) * 100,
                        "rpd": (usage.current_day_requests / details["limits"]["rpd"]) * 100
                    },
                    "last_reset": usage.last_reset_time.isoformat(),
                    "predictions": quota_monitor.predict_quota_exhaustion(model, usage.api_key)
                }
                
                details["api_keys"].append(api_key_data)
        
        return details
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model quota details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cache/stats")
async def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    try:
        stats = intelligent_cache.get_stats()
        
        # 添加额外的分析
        cache_entries = list(intelligent_cache.cache.values())
        
        if cache_entries:
            # 按模型分组统计
            model_stats = {}
            for entry in cache_entries:
                if entry.model not in model_stats:
                    model_stats[entry.model] = {
                        "count": 0,
                        "total_tokens": 0,
                        "avg_access_count": 0,
                        "total_access_count": 0
                    }
                
                model_stats[entry.model]["count"] += 1
                model_stats[entry.model]["total_tokens"] += entry.token_count
                model_stats[entry.model]["total_access_count"] += entry.access_count
            
            # 计算平均值
            for model_data in model_stats.values():
                if model_data["count"] > 0:
                    model_data["avg_access_count"] = model_data["total_access_count"] / model_data["count"]
                    model_data["avg_tokens"] = model_data["total_tokens"] / model_data["count"]
            
            stats["by_model"] = model_stats
            
            # 时间分析
            now = datetime.now()
            recent_entries = [e for e in cache_entries if (now - e.created_at).total_seconds() < 3600]
            stats["recent_hour_entries"] = len(recent_entries)
            
            # 热门缓存
            top_accessed = sorted(cache_entries, key=lambda x: x.access_count, reverse=True)[:5]
            stats["top_accessed"] = [
                {
                    "key": entry.key[-16:],  # 只显示后16位
                    "model": entry.model,
                    "access_count": entry.access_count,
                    "created_at": entry.created_at.isoformat(),
                    "token_count": entry.token_count
                }
                for entry in top_accessed
            ]
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance/models")
async def get_model_performance() -> Dict[str, Any]:
    """获取模型性能统计"""
    try:
        performance_data = {
            "models": [],
            "summary": {
                "total_models": len(model_switch_service.MODEL_PERFORMANCE_LEVEL),
                "fallback_chains": len(model_switch_service.MODEL_FALLBACK_MAP)
            }
        }
        
        for model, level in model_switch_service.MODEL_PERFORMANCE_LEVEL.items():
            model_info = model_switch_service.get_model_info(model)
            
            # 获取配额信息
            quota_info = quota_monitor.QUOTA_LIMITS.get(model, {})
            
            model_data = {
                "model": model,
                "performance_level": level,
                "quota_limits": quota_info,
                "fallback_info": {
                    "has_fallbacks": model_info["has_fallbacks"],
                    "fallback_count": model_info["fallback_count"],
                    "fallback_models": model_info["fallback_models"]
                },
                "efficiency_score": 0,  # 可以基于实际使用数据计算
                "availability_score": 100  # 可以基于配额使用情况计算
            }
            
            # 计算效率评分（基于配额限制）
            if quota_info:
                rpm_score = min(quota_info.get("rpm", 0) / 30 * 100, 100)
                tpm_score = min(quota_info.get("tpm", 0) / 1000000 * 100, 100)
                rpd_score = min(quota_info.get("rpd", 0) / 1000 * 100, 100)
                model_data["efficiency_score"] = (rpm_score + tpm_score + rpd_score) / 3
            
            performance_data["models"].append(model_data)
        
        # 按性能等级排序
        performance_data["models"].sort(key=lambda x: x["performance_level"])
        
        return performance_data
        
    except Exception as e:
        logger.error(f"Error getting model performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts")
async def get_alerts(severity: Optional[str] = Query(None, regex="^(low|medium|high|critical)$")) -> Dict[str, Any]:
    """获取系统警报"""
    try:
        alerts = []
        
        # 检查配额警报
        for key, usage in quota_monitor.usage_tracker.items():
            model = usage.model
            limits = quota_monitor.QUOTA_LIMITS.get(model, {})
            
            if not limits:
                continue
            
            # RPM警报
            rpm_usage = (usage.current_minute_requests / limits["rpm"]) * 100
            if rpm_usage > 90:
                alerts.append({
                    "type": "quota_warning",
                    "severity": "critical" if rpm_usage > 95 else "high",
                    "model": model,
                    "api_key_suffix": usage.api_key[-4:],
                    "metric": "RPM",
                    "usage_percentage": rpm_usage,
                    "message": f"RPM usage at {rpm_usage:.1f}% for {model}",
                    "timestamp": datetime.now().isoformat()
                })
            
            # TPM警报
            tpm_usage = (usage.current_minute_tokens / limits["tpm"]) * 100
            if tpm_usage > 90:
                alerts.append({
                    "type": "quota_warning",
                    "severity": "critical" if tpm_usage > 95 else "high",
                    "model": model,
                    "api_key_suffix": usage.api_key[-4:],
                    "metric": "TPM",
                    "usage_percentage": tpm_usage,
                    "message": f"TPM usage at {tpm_usage:.1f}% for {model}",
                    "timestamp": datetime.now().isoformat()
                })
            
            # RPD警报
            rpd_usage = (usage.current_day_requests / limits["rpd"]) * 100
            if rpd_usage > 80:
                alerts.append({
                    "type": "quota_warning",
                    "severity": "medium" if rpd_usage < 90 else "high",
                    "model": model,
                    "api_key_suffix": usage.api_key[-4:],
                    "metric": "RPD",
                    "usage_percentage": rpd_usage,
                    "message": f"Daily quota at {rpd_usage:.1f}% for {model}",
                    "timestamp": datetime.now().isoformat()
                })
        
        # 检查缓存警报
        cache_stats = intelligent_cache.get_stats()
        cache_usage = (cache_stats["cache_size"] / cache_stats["max_size"]) * 100
        
        if cache_usage > 90:
            alerts.append({
                "type": "cache_warning",
                "severity": "medium",
                "message": f"Cache usage at {cache_usage:.1f}%",
                "cache_size": cache_stats["cache_size"],
                "max_size": cache_stats["max_size"],
                "timestamp": datetime.now().isoformat()
            })
        
        # 按严重程度过滤
        if severity:
            alerts = [alert for alert in alerts if alert["severity"] == severity]
        
        # 按严重程度和时间排序
        severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        alerts.sort(key=lambda x: (severity_order.get(x["severity"], 4), x["timestamp"]), reverse=True)
        
        return {
            "alerts": alerts,
            "total_count": len(alerts),
            "by_severity": {
                "critical": len([a for a in alerts if a["severity"] == "critical"]),
                "high": len([a for a in alerts if a["severity"] == "high"]),
                "medium": len([a for a in alerts if a["severity"] == "medium"]),
                "low": len([a for a in alerts if a["severity"] == "low"])
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cache/clear")
async def clear_cache(expired_only: bool = Query(False)) -> Dict[str, Any]:
    """清理缓存"""
    try:
        if expired_only:
            cleared_count = intelligent_cache.clear_expired()
            return {
                "message": f"Cleared {cleared_count} expired cache entries",
                "cleared_count": cleared_count,
                "remaining_count": len(intelligent_cache.cache)
            }
        else:
            old_size = len(intelligent_cache.cache)
            intelligent_cache.clear_all()
            return {
                "message": f"Cleared all {old_size} cache entries",
                "cleared_count": old_size,
                "remaining_count": 0
            }
            
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))
