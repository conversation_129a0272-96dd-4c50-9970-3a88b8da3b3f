
from functools import wraps
from typing import Callable, TypeVar, Optional, Any
import httpx

from app.config.config import settings
from app.log.logger import get_retry_logger
from app.handler.quota_error_detector import quota_error_detector
from app.service.model.model_switch_service import model_switch_service
from app.service.model.quota_monitor import quota_monitor
from app.service.model.smart_model_selector import smart_model_selector
from app.service.cache.intelligent_cache import intelligent_cache
from app.service.fallback.fallback_manager import fallback_manager
from app.service.fallback.model_selector import intelligent_model_selector, SelectionCriteria, FallbackStrategy

T = TypeVar("T")
logger = get_retry_logger()


class RetryHandler:
    """重试处理装饰器，支持API Key轮换和模型切换"""

    def __init__(self, key_arg: str = "api_key", model_arg: str = "model"):
        self.key_arg = key_arg
        self.model_arg = model_arg

    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            last_exception = None
            model_switch_attempts = 0

            # 尝试从缓存获取结果
            cached_result = self._try_get_from_cache(kwargs)
            if cached_result is not None:
                return cached_result

            for attempt in range(settings.MAX_RETRIES):
                retries = attempt + 1
                try:
                    result = await func(*args, **kwargs)

                    # 成功时保存到缓存
                    self._try_save_to_cache(kwargs, result)

                    return result
                except Exception as e:
                    last_exception = e
                    error_message = str(e)
                    status_code = self._extract_status_code(e)

                    logger.warning(
                        f"API call failed with error: {error_message}. Attempt {retries} of {settings.MAX_RETRIES}"
                    )

                    # 使用智能错误处理
                    if await self._handle_error_intelligently(
                        error_message, status_code, kwargs, model_switch_attempts, retries
                    ):
                        model_switch_attempts += 1
                        continue
                    else:
                        break

            logger.error(
                f"All retry attempts failed, raising final exception: {str(last_exception)}"
            )
            raise last_exception

        return wrapper

    async def _handle_error_intelligently(
        self,
        error_message: str,
        status_code: Optional[int],
        kwargs: dict,
        model_switch_attempts: int,
        retries: int
    ) -> bool:
        """
        智能错误处理

        Returns:
            bool: 是否应该继续重试
        """
        # 获取推荐动作
        recommended_action = quota_error_detector.get_recommended_action(error_message, status_code)

        if recommended_action == 'abort':
            logger.error(f"Fatal error detected, aborting: {error_message}")
            return False

        if recommended_action == 'retry_same':
            logger.info("Temporary error detected, retrying with same configuration")
            return True

        if recommended_action == 'switch_model':
            return await self._handle_model_switch(kwargs, model_switch_attempts, error_message)

        if recommended_action == 'switch_api_key':
            return await self._handle_api_key_switch(kwargs, retries)

        return False

    async def _handle_model_switch(self, kwargs: dict, model_switch_attempts: int, error_message: str = "") -> bool:
        """处理模型切换（增强版）"""
        if not model_switch_service.is_model_switch_enabled():
            return False

        current_model = kwargs.get(self.model_arg)
        if not current_model or model_switch_attempts >= settings.MODEL_SWITCH_MAX_ATTEMPTS:
            return False

        # 记录配额使用情况
        api_key = kwargs.get(self.key_arg)
        if api_key:
            quota_monitor.track_request(current_model, api_key)

        # 尝试使用智能降级管理器
        group_id = kwargs.get("group_id")
        if group_id:
            estimated_tokens = kwargs.get("estimated_tokens", 1000)

            # 使用降级管理器进行智能决策
            fallback_decision = await fallback_manager.should_fallback(
                group_id, current_model, error_message, estimated_tokens
            )

            if fallback_decision.should_fallback and fallback_decision.target_model:
                kwargs[self.model_arg] = fallback_decision.target_model
                logger.info(f"Intelligent fallback: {current_model} -> {fallback_decision.target_model} "
                           f"(strategy: {fallback_decision.strategy_used.value}, confidence: {fallback_decision.confidence:.2f})")
                return True

        # 回退到原有逻辑
        fallback_model = smart_model_selector.select_best_model(
            preferred_models=model_switch_service.MODEL_FALLBACK_MAP.get(current_model, []),
            api_key=api_key,
            prioritize_performance=False  # 优先选择配额充足的模型
        )

        if not fallback_model:
            # 最后回退到基础逻辑
            fallback_model = model_switch_service.get_fallback_model(current_model, model_switch_attempts)

        if fallback_model:
            kwargs[self.model_arg] = fallback_model
            logger.info(f"Switched model due to quota error: {current_model} -> {fallback_model}")
            return True
        else:
            logger.warning(f"No fallback model available for {current_model}")
            return False

    async def _handle_api_key_switch(self, kwargs: dict, retries: int) -> bool:
        """处理API Key切换"""
        key_manager = kwargs.get("key_manager")
        if not key_manager:
            logger.error("KeyManager not available for API key switching")
            return False

        old_key = kwargs.get(self.key_arg)
        new_key = await key_manager.handle_api_failure(old_key, retries)

        if new_key:
            kwargs[self.key_arg] = new_key
            logger.info(f"Switched to new API key: {new_key}")
            return True
        else:
            logger.error(f"No valid API key available after {retries} retries.")
            return False

    def _try_get_from_cache(self, kwargs: dict) -> Optional[Any]:
        """尝试从缓存获取结果"""
        try:
            # 检查是否有缓存相关的参数
            request_data = kwargs.get('payload') or kwargs.get('request_data')
            model = kwargs.get(self.model_arg)
            api_key = kwargs.get(self.key_arg)

            if request_data and model and api_key:
                return intelligent_cache.get(request_data, model, api_key)
        except Exception as e:
            logger.debug(f"Cache get failed: {e}")

        return None

    def _try_save_to_cache(self, kwargs: dict, result: Any) -> None:
        """尝试保存结果到缓存"""
        try:
            request_data = kwargs.get('payload') or kwargs.get('request_data')
            model = kwargs.get(self.model_arg)
            api_key = kwargs.get(self.key_arg)

            if request_data and model and api_key and result:
                # 估算token数量
                token_count = self._estimate_token_count(result)
                intelligent_cache.set(request_data, model, api_key, result, token_count)
        except Exception as e:
            logger.debug(f"Cache save failed: {e}")

    def _estimate_token_count(self, result: Any) -> int:
        """估算响应的token数量"""
        try:
            if isinstance(result, str):
                return len(result) // 4  # 粗略估算
            elif isinstance(result, dict):
                content = str(result.get('content', ''))
                return len(content) // 4
            else:
                return len(str(result)) // 4
        except Exception:
            return 0

    def _extract_status_code(self, exception: Exception) -> Optional[int]:
        """从异常中提取HTTP状态码"""
        if hasattr(exception, 'status_code'):
            return exception.status_code
        elif hasattr(exception, 'response') and hasattr(exception.response, 'status_code'):
            return exception.response.status_code
        elif isinstance(exception, httpx.HTTPStatusError):
            return exception.response.status_code
        return None
