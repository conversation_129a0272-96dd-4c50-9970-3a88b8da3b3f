from contextlib import asynccontextmanager
from fastapi import HTTPException
import logging
import functools
from typing import Callable, Any
import re

@asynccontextmanager
async def handle_route_errors(logger: logging.Logger, operation_name: str, success_message: str = None, failure_message: str = None):
    """
    一个异步上下文管理器，用于统一处理 FastAPI 路由中的常见错误和日志记录。

    Args:
        logger: 用于记录日志的 Logger 实例。
        operation_name: 操作的名称，用于日志记录和错误详情。
        success_message: 操作成功时记录的自定义消息 (可选)。
        failure_message: 操作失败时记录的自定义消息 (可选)。
    """
    default_success_msg = f"{operation_name} request successful"
    default_failure_msg = f"{operation_name} request failed"

    logger.info("-" * 50 + operation_name + "-" * 50)
    try:
        yield
        logger.info(success_message or default_success_msg)
    except HTTPException as http_exc:
        # 如果已经是 HTTPException，直接重新抛出，保留原始状态码和详情
        logger.error(f"{failure_message or default_failure_msg}: {http_exc.detail} (Status: {http_exc.status_code})")
        raise http_exc
    except Exception as e:
        # 终极修复：确保能从各种异常中提取最真实的status_code
        status_code = 500  # 默认值
        error_str = str(e)

        # 优先从结构化属性中获取
        if hasattr(e, 'status_code'):
            status_code = e.status_code
        elif hasattr(e, 'response') and hasattr(e.response, 'status_code'):
            status_code = e.response.status_code
        # 如果没有，则从错误文本中解析
        else:
            match = re.search(r"status code (\d{3})", error_str, re.IGNORECASE)
            if match:
                status_code = int(match.group(1))

        detail_message = f"Internal server error during {operation_name}"
        # 对于客户端错误，传递更具体的原始错误信息
        if 400 <= status_code < 500:
            detail_message = error_str

        logger.error(f"{failure_message or default_failure_msg}: {error_str} (Final Status: {status_code})")
        raise HTTPException(
            status_code=status_code, detail=detail_message
        ) from e


def route_error_handler(operation_name: str = None):
    """
    路由错误处理装饰器

    Args:
        operation_name: 操作名称，用于日志记录
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                result = await func(*args, **kwargs)
                return result
            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as e:
                # 记录错误并转换为HTTP异常
                operation = operation_name or func.__name__
                logging.error(f"{operation} failed: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"{operation} failed: {str(e)}"
                )
        return wrapper

    # 支持无参数调用 @route_error_handler
    if operation_name is None or callable(operation_name):
        func = operation_name
        operation_name = func.__name__ if func else "unknown_operation"
        if func:
            return decorator(func)

    return decorator