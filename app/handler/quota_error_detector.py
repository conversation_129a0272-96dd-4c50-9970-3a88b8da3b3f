import re
from typing import Optional, Dict, Any
from app.log.logger import Logger

logger = Logger.setup_logger(__name__)


class QuotaErrorDetector:
    """增强的配额错误检测器，支持更精确的错误分类和处理建议"""

    # 配额相关的错误模式
    QUOTA_ERROR_PATTERNS = [
        # 429错误相关
        r"429.*quota.*exceeded",
        r"429.*rate.*limit.*exceeded",
        r"429.*too.*many.*requests",
        r"quota.*exceeded",
        r"rate.*limit.*exceeded",
        r"requests.*per.*minute.*exceeded",
        r"requests.*per.*day.*exceeded",
        r"tokens.*per.*minute.*exceeded",

        # Google API特定错误
        r"RESOURCE_EXHAUSTED",
        r"QUOTA_EXCEEDED",
        r"RATE_LIMIT_EXCEEDED",
        r"API_QUOTA_EXCEEDED",

        # 通用配额错误
        r"too.*many.*requests",
        r"request.*limit.*reached",
        r"daily.*limit.*exceeded",
        r"monthly.*limit.*exceeded",
    ]

    # RPM限制错误模式（建议API Key轮换）
    RPM_ERROR_PATTERNS = [
        r"requests.*per.*minute",
        r"rpm.*exceeded",
        r"minute.*limit",
    ]

    # RPD限制错误模式（建议模型切换）
    RPD_ERROR_PATTERNS = [
        r"requests.*per.*day",
        r"daily.*quota",
        r"rpd.*exceeded",
        r"day.*limit",
    ]
    
    # 临时错误模式（可重试）
    TEMPORARY_ERROR_PATTERNS = [
        r"503.*service.*unavailable",
        r"502.*bad.*gateway", 
        r"500.*internal.*server.*error",
        r"timeout",
        r"connection.*error",
        r"network.*error",
    ]
    
    def __init__(self):
        # 编译正则表达式以提高性能
        self.quota_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.QUOTA_ERROR_PATTERNS]
        self.temp_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.TEMPORARY_ERROR_PATTERNS]
        self.rpm_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.RPM_ERROR_PATTERNS]
        self.rpd_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.RPD_ERROR_PATTERNS]
    
    def is_quota_error(self, error_message: str, status_code: Optional[int] = None) -> bool:
        """
        检测是否为配额限制错误
        
        Args:
            error_message: 错误消息
            status_code: HTTP状态码
            
        Returns:
            bool: 是否为配额错误
        """
        if not error_message:
            return False
            
        # 检查状态码
        if status_code == 429:
            logger.debug(f"Detected quota error by status code 429: {error_message}")
            return True
            
        # 检查错误消息模式
        error_text = str(error_message).lower()
        for pattern in self.quota_patterns:
            if pattern.search(error_text):
                logger.debug(f"Detected quota error by pattern: {error_message}")
                return True
                
        return False
    
    def is_temporary_error(self, error_message: str, status_code: Optional[int] = None) -> bool:
        """
        检测是否为临时错误（可重试）
        
        Args:
            error_message: 错误消息
            status_code: HTTP状态码
            
        Returns:
            bool: 是否为临时错误
        """
        if not error_message:
            return False
            
        # 检查状态码
        if status_code in [500, 502, 503, 504]:
            return True
            
        # 检查错误消息模式
        error_text = str(error_message).lower()
        for pattern in self.temp_patterns:
            if pattern.search(error_text):
                return True
                
        return False
    
    def is_rpm_error(self, error_message: str) -> bool:
        """检测是否为RPM（每分钟请求数）限制错误"""
        if not error_message:
            return False

        error_text = str(error_message).lower()
        for pattern in self.rpm_patterns:
            if pattern.search(error_text):
                return True
        return False

    def is_rpd_error(self, error_message: str) -> bool:
        """检测是否为RPD（每日请求数）限制错误"""
        if not error_message:
            return False

        error_text = str(error_message).lower()
        for pattern in self.rpd_patterns:
            if pattern.search(error_text):
                return True
        return False

    def get_recommended_action(self, error_message: str, status_code: Optional[int] = None) -> str:
        """
        获取推荐的处理动作

        Returns:
            str: 'switch_api_key', 'switch_model', 'retry_same', 'abort'
        """
        if not self.is_quota_error(error_message, status_code):
            if self.is_temporary_error(error_message, status_code):
                return 'retry_same'
            else:
                return 'abort'

        # 是配额错误，进一步判断类型
        if self.is_rpm_error(error_message):
            return 'switch_api_key'  # RPM限制建议切换API Key
        elif self.is_rpd_error(error_message):
            return 'switch_model'    # RPD限制建议切换模型
        else:
            return 'switch_api_key'    # 默认切换API Key，因为这通常是更有效的初步解决方案

    def should_switch_model(self, error_message: str, status_code: Optional[int] = None) -> bool:
        """
        判断是否应该切换模型

        Args:
            error_message: 错误消息
            status_code: HTTP状态码

        Returns:
            bool: 是否应该切换模型
        """
        action = self.get_recommended_action(error_message, status_code)
        return action == 'switch_model'

    def should_switch_api_key(self, error_message: str, status_code: Optional[int] = None) -> bool:
        """
        判断是否应该切换API Key

        Args:
            error_message: 错误消息
            status_code: HTTP状态码

        Returns:
            bool: 是否应该切换API Key
        """
        action = self.get_recommended_action(error_message, status_code)
        return action == 'switch_api_key'
    
    def extract_error_details(self, error_message: str, status_code: Optional[int] = None) -> Dict[str, Any]:
        """
        提取错误详细信息
        
        Args:
            error_message: 错误消息
            status_code: HTTP状态码
            
        Returns:
            Dict: 错误详细信息
        """
        return {
            "is_quota_error": self.is_quota_error(error_message, status_code),
            "is_temporary_error": self.is_temporary_error(error_message, status_code),
            "should_switch_model": self.should_switch_model(error_message, status_code),
            "status_code": status_code,
            "error_message": error_message
        }


# 全局实例
quota_error_detector = QuotaErrorDetector()
