"""
SQLite数据库操作实现
实现用户组管理的数据库操作
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from app.database.sqlite_init import get_db_manager
from app.log.logger import get_logger

logger = get_logger(__name__)

async def create_user_group(
    group_id: str,
    group_name: str,
    description: str = "",
    auto_fallback: bool = True,
    context_isolation: bool = True,
    priority: int = 5,
    no_fallback_models: List[str] = None
) -> bool:
    """创建用户组"""
    try:
        db_manager = await get_db_manager()
        
        # 准备数据
        no_fallback_models_json = json.dumps(no_fallback_models or [])
        
        query = """
            INSERT INTO user_groups 
            (group_id, group_name, description, priority, auto_fallback, 
             context_isolation, no_fallback_models, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        now = datetime.now().isoformat()
        params = (
            group_id, group_name, description, priority,
            auto_fallback, context_isolation, no_fallback_models_json,
            now, now
        )
        
        await db_manager.execute_query(query, params)
        logger.info(f"Created user group: {group_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating user group {group_id}: {str(e)}")
        return False

async def get_user_group(group_id: str) -> Optional[Dict[str, Any]]:
    """获取单个用户组"""
    try:
        db_manager = await get_db_manager()
        
        query = "SELECT * FROM user_groups WHERE group_id = ?"
        row = await db_manager.fetch_one(query, (group_id,))
        
        if row:
            # 转换为字典格式
            columns = [
                'id', 'group_id', 'group_name', 'description', 'status',
                'priority', 'auto_fallback', 'context_isolation', 'token_limit',
                'expiry_date', 'token_rates', 'no_fallback_models',
                'created_at', 'updated_at'
            ]
            
            result = dict(zip(columns, row))
            
            # 解析JSON字段
            if result['token_rates']:
                result['token_rates'] = json.loads(result['token_rates'])
            else:
                result['token_rates'] = {}
                
            if result['no_fallback_models']:
                result['no_fallback_models'] = json.loads(result['no_fallback_models'])
            else:
                result['no_fallback_models'] = []
            
            return result
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting user group {group_id}: {str(e)}")
        return None

async def list_user_groups() -> List[Dict[str, Any]]:
    """获取所有用户组"""
    try:
        db_manager = await get_db_manager()
        
        query = "SELECT * FROM user_groups ORDER BY created_at DESC"
        rows = await db_manager.fetch_all(query)
        
        columns = [
            'id', 'group_id', 'group_name', 'description', 'status',
            'priority', 'auto_fallback', 'context_isolation', 'token_limit',
            'expiry_date', 'token_rates', 'no_fallback_models',
            'created_at', 'updated_at'
        ]
        
        results = []
        for row in rows:
            result = dict(zip(columns, row))
            
            # 解析JSON字段
            if result['token_rates']:
                result['token_rates'] = json.loads(result['token_rates'])
            else:
                result['token_rates'] = {}
                
            if result['no_fallback_models']:
                result['no_fallback_models'] = json.loads(result['no_fallback_models'])
            else:
                result['no_fallback_models'] = []
            
            results.append(result)
        
        return results
        
    except Exception as e:
        logger.error(f"Error listing user groups: {str(e)}")
        return []

async def update_user_group(group_id: str, updates: Dict[str, Any]) -> bool:
    """更新用户组"""
    try:
        db_manager = await get_db_manager()
        
        # 构建更新查询
        set_clauses = []
        params = []
        
        for key, value in updates.items():
            if key in ['token_rates', 'no_fallback_models']:
                # JSON字段需要序列化
                value = json.dumps(value)
            set_clauses.append(f"{key} = ?")
            params.append(value)
        
        # 添加更新时间
        set_clauses.append("updated_at = ?")
        params.append(datetime.now().isoformat())
        
        # 添加WHERE条件
        params.append(group_id)
        
        query = f"UPDATE user_groups SET {', '.join(set_clauses)} WHERE group_id = ?"
        
        await db_manager.execute_query(query, tuple(params))
        logger.info(f"Updated user group: {group_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating user group {group_id}: {str(e)}")
        return False

async def delete_user_group(group_id: str) -> bool:
    """删除用户组"""
    try:
        db_manager = await get_db_manager()
        
        # 删除相关数据
        await db_manager.execute_query("DELETE FROM user_group_quotas WHERE group_id = ?", (group_id,))
        await db_manager.execute_query("DELETE FROM user_group_api_keys WHERE group_id = ?", (group_id,))
        await db_manager.execute_query("DELETE FROM cache_statistics WHERE group_id = ?", (group_id,))
        await db_manager.execute_query("DELETE FROM security_events WHERE group_id = ?", (group_id,))
        await db_manager.execute_query("DELETE FROM monitoring_metrics WHERE group_id = ?", (group_id,))
        
        # 删除用户组
        await db_manager.execute_query("DELETE FROM user_groups WHERE group_id = ?", (group_id,))
        
        logger.info(f"Deleted user group: {group_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error deleting user group {group_id}: {str(e)}")
        return False

async def get_user_group_quotas(group_id: str) -> List[Dict[str, Any]]:
    """获取用户组配额"""
    try:
        db_manager = await get_db_manager()
        
        query = "SELECT * FROM user_group_quotas WHERE group_id = ?"
        rows = await db_manager.fetch_all(query, (group_id,))
        
        columns = [
            'id', 'group_id', 'model_name', 'quota_limit', 'current_usage',
            'reset_period', 'last_reset', 'created_at'
        ]
        
        return [dict(zip(columns, row)) for row in rows]
        
    except Exception as e:
        logger.error(f"Error getting quotas for group {group_id}: {str(e)}")
        return []

async def get_user_group_api_keys(group_id: str) -> List[Dict[str, Any]]:
    """获取用户组API密钥"""
    try:
        db_manager = await get_db_manager()
        
        query = "SELECT * FROM user_group_api_keys WHERE group_id = ?"
        rows = await db_manager.fetch_all(query, (group_id,))
        
        columns = [
            'id', 'group_id', 'api_key', 'key_name', 'is_active',
            'usage_count', 'last_used', 'created_at'
        ]
        
        return [dict(zip(columns, row)) for row in rows]
        
    except Exception as e:
        logger.error(f"Error getting API keys for group {group_id}: {str(e)}")
        return []
