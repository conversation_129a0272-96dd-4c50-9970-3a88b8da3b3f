"""
SQLite数据库初始化脚本
为Gemini Balance项目创建必要的数据表
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, Dict, Any
import asyncio
import aiosqlite
from app.log.logger import get_logger

logger = get_logger(__name__)

# 数据库文件路径
DB_PATH = "data/gemini_balance.db"

# 确保数据目录存在
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = DB_PATH):
        self.db_path = db_path
    
    async def init_database(self):
        """初始化数据库表结构"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 创建用户组表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS user_groups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        group_id TEXT UNIQUE NOT NULL,
                        group_name TEXT NOT NULL,
                        description TEXT,
                        status TEXT DEFAULT 'ACTIVE',
                        priority INTEGER DEFAULT 5,
                        auto_fallback BOOLEAN DEFAULT 1,
                        context_isolation BOOLEAN DEFAULT 1,
                        token_limit INTEGER DEFAULT 0,
                        expiry_date TEXT,
                        token_rates TEXT, -- JSON字符串
                        no_fallback_models TEXT, -- JSON字符串
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建用户组配额表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS user_group_quotas (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        group_id TEXT NOT NULL,
                        model_name TEXT NOT NULL,
                        quota_limit INTEGER NOT NULL,
                        current_usage INTEGER DEFAULT 0,
                        reset_period TEXT DEFAULT 'monthly',
                        last_reset TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (group_id) REFERENCES user_groups(group_id)
                    )
                """)
                
                # 创建用户组API密钥表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS t_user_group_api_keys (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        group_id TEXT NOT NULL,
                        api_key TEXT NOT NULL,
                        key_name TEXT,
                        key_type TEXT DEFAULT 'gemini',
                        is_active BOOLEAN DEFAULT 1,
                        failure_count INTEGER DEFAULT 0,
                        max_failures INTEGER DEFAULT 5,
                        total_requests INTEGER DEFAULT 0,
                        successful_requests INTEGER DEFAULT 0,
                        last_used_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (group_id) REFERENCES t_user_groups(group_id)
                    )
                """)
                
                # 创建缓存统计表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS cache_statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        group_id TEXT,
                        cache_hits INTEGER DEFAULT 0,
                        cache_misses INTEGER DEFAULT 0,
                        cache_size INTEGER DEFAULT 0,
                        hit_rate REAL DEFAULT 0.0,
                        avg_response_time REAL DEFAULT 0.0,
                        memory_usage REAL DEFAULT 0.0,
                        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建安全事件表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS security_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        group_id TEXT,
                        event_type TEXT NOT NULL,
                        severity TEXT DEFAULT 'medium',
                        source_ip TEXT,
                        user_agent TEXT,
                        description TEXT,
                        metadata TEXT, -- JSON字符串
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建监控指标表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS monitoring_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        metric_type TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        group_id TEXT,
                        model_name TEXT,
                        tags TEXT, -- JSON字符串
                        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建系统配置表
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS system_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        config_key TEXT UNIQUE NOT NULL,
                        config_value TEXT,
                        config_type TEXT DEFAULT 'string',
                        description TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                await db.execute("CREATE INDEX IF NOT EXISTS idx_user_groups_group_id ON user_groups(group_id)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_user_group_quotas_group_id ON user_group_quotas(group_id)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_user_group_api_keys_group_id ON t_user_group_api_keys(group_id)")
                
                # 为 t_user_group_api_keys 添加新字段（如果不存在）
                try:
                    await db.execute("ALTER TABLE t_user_group_api_keys ADD COLUMN key_name TEXT")
                except sqlite3.OperationalError:
                    # 字段已存在
                    pass
                
                await db.commit()
                logger.info("Database tables created/updated successfully")
                
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    async def get_connection(self):
        """获取数据库连接"""
        return await aiosqlite.connect(self.db_path)
    
    async def execute_query(self, query: str, params: tuple = ()):
        """执行查询"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(query, params)
            await db.commit()
            return cursor
    
    async def fetch_one(self, query: str, params: tuple = ()):
        """获取单条记录"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(query, params)
            return await cursor.fetchone()
    
    async def fetch_all(self, query: str, params: tuple = ()):
        """获取多条记录"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(query, params)
            return await cursor.fetchall()

# 全局数据库管理器实例
db_manager = DatabaseManager()

async def init_database():
    """初始化数据库"""
    await db_manager.init_database()

async def get_db_manager():
    """获取数据库管理器实例"""
    return db_manager

if __name__ == "__main__":
    # 直接运行此脚本来初始化数据库
    asyncio.run(init_database())
    print(f"Database initialized at: {DB_PATH}")
