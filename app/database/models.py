"""
数据库模型模块
"""
import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, BigInteger, Enum, ForeignKey, Float
from sqlalchemy.orm import relationship
import enum

from app.database.connection import Base


class Settings(Base):
    """
    设置表，对应.env中的配置项
    """
    __tablename__ = "t_settings"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(100), nullable=False, unique=True, comment="配置项键名")
    value = Column(Text, nullable=True, comment="配置项值")
    description = Column(String(255), nullable=True, comment="配置项描述")
    created_at = Column(DateTime, default=datetime.datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment="更新时间")
    
    def __repr__(self):
        return f"<Settings(key='{self.key}', value='{self.value}')>"


class ErrorLog(Base):
    """
    错误日志表
    """
    __tablename__ = "t_error_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    gemini_key = Column(String(100), nullable=True, comment="Gemini API密钥")
    model_name = Column(String(100), nullable=True, comment="模型名称")
    error_type = Column(String(50), nullable=True, comment="错误类型")
    error_log = Column(Text, nullable=True, comment="错误日志")
    error_code = Column(Integer, nullable=True, comment="错误代码")
    request_msg = Column(JSON, nullable=True, comment="请求消息")
    request_time = Column(DateTime, default=datetime.datetime.now, comment="请求时间")
    
    def __repr__(self):
        return f"<ErrorLog(id='{self.id}', gemini_key='{self.gemini_key}')>"


class RequestLog(Base):
    """
    API 请求日志表
    """

    __tablename__ = "t_request_log"

    id = Column(Integer, primary_key=True, autoincrement=True)
    request_time = Column(DateTime, default=datetime.datetime.now, comment="请求时间")
    model_name = Column(String(100), nullable=True, comment="模型名称")
    api_key = Column(String(100), nullable=True, comment="使用的API密钥")
    is_success = Column(Boolean, nullable=False, comment="请求是否成功")
    status_code = Column(Integer, nullable=True, comment="API响应状态码")
    latency_ms = Column(Integer, nullable=True, comment="请求耗时(毫秒)")

    def __repr__(self):
        return f"<RequestLog(id='{self.id}', key='{self.api_key[:4]}...', success='{self.is_success}')>"


class FileState(enum.Enum):
    """文件状态枚举"""
    PROCESSING = "PROCESSING"
    ACTIVE = "ACTIVE"
    FAILED = "FAILED"


class FileRecord(Base):
    """
    文件记录表，用于存储上传到 Gemini 的文件信息
    """
    __tablename__ = "t_file_records"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 文件基本信息
    name = Column(String(255), unique=True, nullable=False, comment="文件名称，格式: files/{file_id}")
    display_name = Column(String(255), nullable=True, comment="用户上传时的原始文件名")
    mime_type = Column(String(100), nullable=False, comment="MIME 类型")
    size_bytes = Column(BigInteger, nullable=False, comment="文件大小（字节）")
    sha256_hash = Column(String(255), nullable=True, comment="文件的 SHA256 哈希值")
    
    # 状态信息
    state = Column(Enum(FileState), nullable=False, default=FileState.PROCESSING, comment="文件状态")
    
    # 时间戳
    create_time = Column(DateTime, nullable=False, comment="创建时间")
    update_time = Column(DateTime, nullable=False, comment="更新时间")
    expiration_time = Column(DateTime, nullable=False, comment="过期时间")
    
    # API 相关
    uri = Column(String(500), nullable=False, comment="文件访问 URI")
    api_key = Column(String(100), nullable=False, comment="上传时使用的 API Key")
    upload_url = Column(Text, nullable=True, comment="临时上传 URL（用于分块上传）")
    
    # 额外信息
    user_token = Column(String(100), nullable=True, comment="上传用户的 token")
    upload_completed = Column(DateTime, nullable=True, comment="上传完成时间")
    
    def __repr__(self):
        return f"<FileRecord(name='{self.name}', state='{self.state.value if self.state else 'None'}', api_key='{self.api_key[:8]}...')>"
    
    def to_dict(self):
        """转换为字典格式，用于 API 响应"""
        return {
            "name": self.name,
            "displayName": self.display_name,
            "mimeType": self.mime_type,
            "sizeBytes": str(self.size_bytes),
            "createTime": self.create_time.isoformat() + "Z",
            "updateTime": self.update_time.isoformat() + "Z",
            "expirationTime": self.expiration_time.isoformat() + "Z",
            "sha256Hash": self.sha256_hash,
            "uri": self.uri,
            "state": self.state.value if self.state else "PROCESSING"
        }
    
    def is_expired(self):
        """检查文件是否已过期"""
        # 确保比较时都是 timezone-aware
        expiration_time = self.expiration_time
        if expiration_time.tzinfo is None:
            expiration_time = expiration_time.replace(tzinfo=datetime.timezone.utc)
        return datetime.datetime.now(datetime.timezone.utc) > expiration_time


class UserGroupStatus(enum.Enum):
    """用户组状态枚举"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    SUSPENDED = "SUSPENDED"


class UserGroup(Base):
    """
    用户组表，用于管理不同的用户组
    """
    __tablename__ = "t_user_groups"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(String(50), unique=True, nullable=False, comment="用户组唯一标识")
    group_name = Column(String(100), nullable=False, comment="用户组名称")
    description = Column(Text, nullable=True, comment="用户组描述")
    status = Column(Enum(UserGroupStatus), nullable=False, default=UserGroupStatus.ACTIVE, comment="用户组状态")

    # 配置选项
    auto_fallback = Column(Boolean, nullable=False, default=True, comment="自动降级开关")
    context_isolation = Column(Boolean, nullable=False, default=True, comment="上下文隔离开关")
    priority = Column(Integer, nullable=False, default=5, comment="优先级(1-10)")

    # 不降级模型列表(JSON格式存储)
    no_fallback_models = Column(JSON, nullable=True, comment="不降级模型列表")

    # 时间戳
    created_at = Column(DateTime, default=datetime.datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment="更新时间")

    # 关联关系
    quotas = relationship("UserGroupQuota", back_populates="user_group", cascade="all, delete-orphan")
    api_keys = relationship("UserGroupApiKey", back_populates="user_group", cascade="all, delete-orphan")
    usage_records = relationship("UserGroupUsage", back_populates="user_group", cascade="all, delete-orphan")
    contexts = relationship("UserGroupContext", back_populates="user_group", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<UserGroup(group_id='{self.group_id}', name='{self.group_name}', status='{self.status.value}')>"


class QuotaType(enum.Enum):
    """配额类型枚举"""
    RPM = "RPM"  # 每分钟请求数
    TPM = "TPM"  # 每分钟令牌数
    RPD = "RPD"  # 每日请求数
    TPD = "TPD"  # 每日令牌数


class UserGroupQuota(Base):
    """
    用户组配额表，用于管理用户组的配额限制
    """
    __tablename__ = "t_user_group_quotas"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(String(50), ForeignKey('t_user_groups.group_id'), nullable=False, comment="用户组ID")
    quota_type = Column(Enum(QuotaType), nullable=False, comment="配额类型")
    model_name = Column(String(100), nullable=True, comment="模型名称，为空表示全局配额")
    quota_limit = Column(BigInteger, nullable=False, comment="配额限制")
    current_usage = Column(BigInteger, nullable=False, default=0, comment="当前使用量")

    # 时间窗口
    window_start = Column(DateTime, nullable=False, default=datetime.datetime.now, comment="时间窗口开始时间")
    window_duration_minutes = Column(Integer, nullable=False, default=1, comment="时间窗口持续时间(分钟)")

    # 时间戳
    created_at = Column(DateTime, default=datetime.datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment="更新时间")

    # 关联关系
    user_group = relationship("UserGroup", back_populates="quotas")

    def __repr__(self):
        return f"<UserGroupQuota(group_id='{self.group_id}', type='{self.quota_type.value}', limit={self.quota_limit})>"

    def is_quota_exceeded(self) -> bool:
        """检查配额是否超限"""
        return self.current_usage >= self.quota_limit

    def get_usage_percentage(self) -> float:
        """获取配额使用百分比"""
        if self.quota_limit == 0:
            return 0.0
        return (self.current_usage / self.quota_limit) * 100


class UserGroupApiKey(Base):
    """
    用户组API密钥表，用于管理用户组专属的API密钥
    """
    __tablename__ = "t_user_group_api_keys"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(String(50), ForeignKey('t_user_groups.group_id'), nullable=False, comment="用户组ID")
    api_key = Column(String(200), nullable=False, comment="API密钥")
    key_name = Column(String(100), nullable=True, comment="密钥名称")
    key_type = Column(String(50), nullable=False, default="gemini", comment="密钥类型(gemini/vertex)")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否激活")
    failure_count = Column(Integer, nullable=False, default=0, comment="失败次数")
    max_failures = Column(Integer, nullable=False, default=5, comment="最大失败次数")

    # 统计信息
    total_requests = Column(BigInteger, nullable=False, default=0, comment="总请求数")
    successful_requests = Column(BigInteger, nullable=False, default=0, comment="成功请求数")
    last_used_at = Column(DateTime, nullable=True, comment="最后使用时间")

    # 时间戳
    created_at = Column(DateTime, default=datetime.datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment="更新时间")

    # 关联关系
    user_group = relationship("UserGroup", back_populates="api_keys")

    def __repr__(self):
        return f"<UserGroupApiKey(group_id='{self.group_id}', key='{self.api_key[:8]}...', active={self.is_active})>"

    def is_valid(self) -> bool:
        """检查密钥是否有效"""
        return self.is_active and self.failure_count < self.max_failures

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100


class UserGroupUsage(Base):
    """
    用户组使用统计表，用于记录用户组的详细使用情况
    """
    __tablename__ = "t_user_group_usage"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(String(50), ForeignKey('t_user_groups.group_id'), nullable=False, comment="用户组ID")

    # 请求信息
    model_name = Column(String(100), nullable=False, comment="模型名称")
    api_key_hash = Column(String(64), nullable=False, comment="API密钥哈希")
    request_time = Column(DateTime, default=datetime.datetime.now, comment="请求时间")

    # 使用量统计
    input_tokens = Column(Integer, nullable=False, default=0, comment="输入令牌数")
    output_tokens = Column(Integer, nullable=False, default=0, comment="输出令牌数")
    total_tokens = Column(Integer, nullable=False, default=0, comment="总令牌数")

    # 性能指标
    latency_ms = Column(Integer, nullable=True, comment="响应延迟(毫秒)")
    is_success = Column(Boolean, nullable=False, comment="请求是否成功")
    status_code = Column(Integer, nullable=True, comment="HTTP状态码")
    error_type = Column(String(50), nullable=True, comment="错误类型")

    # 降级信息
    is_fallback = Column(Boolean, nullable=False, default=False, comment="是否为降级请求")
    original_model = Column(String(100), nullable=True, comment="原始请求模型")

    # 关联关系
    user_group = relationship("UserGroup", back_populates="usage_records")

    def __repr__(self):
        return f"<UserGroupUsage(group_id='{self.group_id}', model='{self.model_name}', tokens={self.total_tokens})>"


class UserGroupContext(Base):
    """
    用户组上下文表，用于管理用户组的对话上下文
    """
    __tablename__ = "t_user_group_contexts"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(String(50), ForeignKey('t_user_groups.group_id'), nullable=False, comment="用户组ID")
    conversation_id = Column(String(100), nullable=False, comment="对话ID")

    # 上下文内容
    context_data = Column(JSON, nullable=False, comment="上下文数据")
    message_count = Column(Integer, nullable=False, default=0, comment="消息数量")
    total_tokens = Column(Integer, nullable=False, default=0, comment="总令牌数")

    # 元数据
    model_name = Column(String(100), nullable=False, comment="使用的模型")
    last_model = Column(String(100), nullable=True, comment="最后使用的模型")
    is_shared = Column(Boolean, nullable=False, default=False, comment="是否为共享上下文")

    # 时间戳
    created_at = Column(DateTime, default=datetime.datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment="更新时间")
    last_accessed_at = Column(DateTime, default=datetime.datetime.now, comment="最后访问时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")

    # 关联关系
    user_group = relationship("UserGroup", back_populates="contexts")

    def __repr__(self):
        return f"<UserGroupContext(group_id='{self.group_id}', conversation_id='{self.conversation_id}', messages={self.message_count})>"

    def is_expired(self) -> bool:
        """检查上下文是否过期"""
        if self.expires_at is None:
            return False
        return datetime.datetime.now() > self.expires_at

    def get_context_size_mb(self) -> float:
        """获取上下文大小(MB)"""
        import json
        context_str = json.dumps(self.context_data) if self.context_data else "{}"
        return len(context_str.encode('utf-8')) / (1024 * 1024)
