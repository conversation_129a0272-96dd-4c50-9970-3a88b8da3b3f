"""
用户组功能数据库迁移脚本
创建用户组相关的所有表
"""
import asyncio
from app.database.connection import database, engine
from app.database.models import Base
from app.log.logger import get_database_logger

logger = get_database_logger()


async def create_user_group_tables():
    """创建用户组相关的数据库表"""
    try:
        logger.info("开始创建用户组相关数据库表...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info("用户组相关数据库表创建成功")
        return True
    except Exception as e:
        logger.error(f"创建用户组数据库表失败: {str(e)}")
        raise


async def drop_user_group_tables():
    """删除用户组相关的数据库表"""
    try:
        logger.info("开始删除用户组相关数据库表...")
        
        # 删除表的SQL语句
        drop_statements = [
            "DROP TABLE IF EXISTS t_user_group_contexts;",
            "DROP TABLE IF EXISTS t_user_group_usage;", 
            "DROP TABLE IF EXISTS t_user_group_api_keys;",
            "DROP TABLE IF EXISTS t_user_group_quotas;",
            "DROP TABLE IF EXISTS t_user_groups;"
        ]
        
        for statement in drop_statements:
            try:
                await database.execute(statement)
                logger.info(f"执行SQL: {statement}")
            except Exception as e:
                logger.warning(f"执行SQL失败 {statement}: {str(e)}")
        
        logger.info("用户组相关数据库表删除完成")
        return True
    except Exception as e:
        logger.error(f"删除用户组数据库表失败: {str(e)}")
        raise


async def migrate_up():
    """执行向上迁移"""
    try:
        await database.connect()
        await create_user_group_tables()
        logger.info("用户组数据库迁移完成")
    except Exception as e:
        logger.error(f"用户组数据库迁移失败: {str(e)}")
        raise
    finally:
        await database.disconnect()


async def migrate_down():
    """执行向下迁移"""
    try:
        await database.connect()
        await drop_user_group_tables()
        logger.info("用户组数据库回滚完成")
    except Exception as e:
        logger.error(f"用户组数据库回滚失败: {str(e)}")
        raise
    finally:
        await database.disconnect()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "down":
        asyncio.run(migrate_down())
    else:
        asyncio.run(migrate_up())
