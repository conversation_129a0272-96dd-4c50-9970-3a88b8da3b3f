"""
安全中间件
提供用户组级别的安全检查和保护
"""
import time
import json
from typing import Optional
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON>NResponse

from app.service.security.enhanced_security_service import enhanced_security_service
from app.log.logger import get_security_logger

logger = get_security_logger()


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    安全中间件
    在请求处理前进行安全检查
    """
    
    def __init__(self, app, enable_security_checks: bool = True):
        super().__init__(app)
        self.enable_security_checks = enable_security_checks
        self.security_service = enhanced_security_service
        
        # 需要安全检查的路径
        self.protected_paths = [
            "/v1/groups/",
            "/admin/",
            "/monitoring/",
            "/cache/"
        ]
        
        # 跳过安全检查的路径
        self.skip_paths = [
            "/health",
            "/info",
            "/docs",
            "/openapi.json",
            "/static/",
            "/favicon.ico"
        ]
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        start_time = time.time()
        
        try:
            # 检查是否需要安全检查
            if not self._should_check_security(request):
                response = await call_next(request)
                return response
            
            # 提取请求信息
            source_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            request_path = request.url.path
            
            # 提取用户组ID
            group_id = self._extract_group_id(request)
            if not group_id:
                # 如果无法提取用户组ID，跳过用户组级别的安全检查
                response = await call_next(request)
                return response
            
            # 提取API密钥
            api_key = self._extract_api_key(request)
            if not api_key:
                return JSONResponse(
                    status_code=401,
                    content={"error": "Missing API key"}
                )
            
            # 执行安全检查
            security_result = await self.security_service.verify_group_access(
                group_id, source_ip, user_agent, api_key, request_path
            )
            
            if not security_result.get("allowed", False):
                # 安全检查失败
                logger.warning(
                    f"Security check failed for group {group_id} from {source_ip}: "
                    f"{security_result.get('reason', 'Unknown')}"
                )
                
                return JSONResponse(
                    status_code=403,
                    content={
                        "error": "Access denied",
                        "reason": security_result.get("reason", "Security check failed"),
                        "group_id": group_id
                    }
                )
            
            # 在请求中添加安全信息
            request.state.security_info = {
                "group_id": group_id,
                "source_ip": source_ip,
                "security_level": security_result.get("security_level", "medium"),
                "verified_at": time.time()
            }
            
            # 继续处理请求
            response = await call_next(request)
            
            # 记录请求完成
            processing_time = time.time() - start_time
            logger.debug(
                f"Request completed for group {group_id} from {source_ip} "
                f"in {processing_time:.3f}s"
            )
            
            return response
            
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"Error in security middleware: {str(e)}")
            # 安全检查出错时，记录错误但允许请求继续
            response = await call_next(request)
            return response
    
    def _should_check_security(self, request: Request) -> bool:
        """判断是否需要进行安全检查"""
        if not self.enable_security_checks:
            return False
        
        path = request.url.path
        
        # 跳过特定路径
        for skip_path in self.skip_paths:
            if path.startswith(skip_path):
                return False
        
        # 检查是否为受保护路径
        for protected_path in self.protected_paths:
            if path.startswith(protected_path):
                return True
        
        return False
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # 取第一个IP（原始客户端IP）
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip.strip()
        
        # 回退到直接连接IP
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def _extract_group_id(self, request: Request) -> Optional[str]:
        """从请求中提取用户组ID"""
        path = request.url.path
        
        # 从URL路径中提取
        if "/v1/groups/" in path:
            parts = path.split("/v1/groups/")
            if len(parts) > 1:
                group_part = parts[1].split("/")[0]
                return group_part
        
        # 从查询参数中提取
        group_id = request.query_params.get("group_id")
        if group_id:
            return group_id
        
        # 从头部中提取
        group_id = request.headers.get("x-group-id")
        if group_id:
            return group_id
        
        return None
    
    def _extract_api_key(self, request: Request) -> Optional[str]:
        """从请求中提取API密钥"""
        # 从Authorization头中提取
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header.replace("Bearer ", "")
        
        # 从API Key头中提取
        api_key = request.headers.get("x-api-key")
        if api_key:
            return api_key
        
        # 从查询参数中提取
        api_key = request.query_params.get("api_key")
        if api_key:
            return api_key
        
        return None


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    速率限制中间件
    提供全局和用户组级别的速率限制
    """
    
    def __init__(self, app, global_rate_limit: int = 1000):
        super().__init__(app)
        self.global_rate_limit = global_rate_limit
        self.request_counts = {}
        self.last_reset = time.time()
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        current_time = time.time()
        
        # 每分钟重置计数器
        if current_time - self.last_reset > 60:
            self.request_counts.clear()
            self.last_reset = current_time
        
        # 获取客户端标识
        client_id = self._get_client_identifier(request)
        
        # 检查全局速率限制
        current_count = self.request_counts.get(client_id, 0)
        if current_count >= self.global_rate_limit:
            logger.warning(f"Global rate limit exceeded for {client_id}")
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "limit": self.global_rate_limit,
                    "reset_time": int(self.last_reset + 60)
                }
            )
        
        # 增加计数
        self.request_counts[client_id] = current_count + 1
        
        # 继续处理请求
        response = await call_next(request)
        
        # 添加速率限制头
        response.headers["X-RateLimit-Limit"] = str(self.global_rate_limit)
        response.headers["X-RateLimit-Remaining"] = str(self.global_rate_limit - self.request_counts[client_id])
        response.headers["X-RateLimit-Reset"] = str(int(self.last_reset + 60))
        
        return response
    
    def _get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符"""
        # 优先使用API密钥
        api_key = request.headers.get("authorization", "").replace("Bearer ", "")
        if api_key:
            return f"key:{api_key[:8]}"
        
        # 回退到IP地址
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return f"ip:{forwarded_for.split(',')[0].strip()}"
        
        if hasattr(request.client, "host"):
            return f"ip:{request.client.host}"
        
        return "unknown"


class AuditMiddleware(BaseHTTPMiddleware):
    """
    审计中间件
    记录所有API访问日志
    """
    
    def __init__(self, app, enable_audit: bool = True):
        super().__init__(app)
        self.enable_audit = enable_audit
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        start_time = time.time()
        
        # 记录请求开始
        if self.enable_audit:
            await self._log_request_start(request)
        
        try:
            response = await call_next(request)
            
            # 记录请求完成
            if self.enable_audit:
                processing_time = time.time() - start_time
                await self._log_request_end(request, response, processing_time)
            
            return response
            
        except Exception as e:
            # 记录请求错误
            if self.enable_audit:
                processing_time = time.time() - start_time
                await self._log_request_error(request, e, processing_time)
            raise
    
    async def _log_request_start(self, request: Request):
        """记录请求开始"""
        try:
            source_ip = request.headers.get("x-forwarded-for", "").split(",")[0].strip()
            if not source_ip and hasattr(request.client, "host"):
                source_ip = request.client.host
            
            logger.info(
                f"API Request: {request.method} {request.url.path} "
                f"from {source_ip} "
                f"User-Agent: {request.headers.get('user-agent', 'unknown')}"
            )
        except Exception as e:
            logger.error(f"Error logging request start: {str(e)}")
    
    async def _log_request_end(self, request: Request, response: Response, processing_time: float):
        """记录请求完成"""
        try:
            source_ip = request.headers.get("x-forwarded-for", "").split(",")[0].strip()
            if not source_ip and hasattr(request.client, "host"):
                source_ip = request.client.host
            
            logger.info(
                f"API Response: {request.method} {request.url.path} "
                f"Status: {response.status_code} "
                f"Time: {processing_time:.3f}s "
                f"IP: {source_ip}"
            )
        except Exception as e:
            logger.error(f"Error logging request end: {str(e)}")
    
    async def _log_request_error(self, request: Request, error: Exception, processing_time: float):
        """记录请求错误"""
        try:
            source_ip = request.headers.get("x-forwarded-for", "").split(",")[0].strip()
            if not source_ip and hasattr(request.client, "host"):
                source_ip = request.client.host
            
            logger.error(
                f"API Error: {request.method} {request.url.path} "
                f"Error: {str(error)} "
                f"Time: {processing_time:.3f}s "
                f"IP: {source_ip}"
            )
        except Exception as e:
            logger.error(f"Error logging request error: {str(e)}")


# 中间件工厂函数
def create_security_middleware(enable_security_checks: bool = True):
    """创建安全中间件"""
    def middleware_factory(app):
        return SecurityMiddleware(app, enable_security_checks)
    return middleware_factory


def create_rate_limit_middleware(global_rate_limit: int = 1000):
    """创建速率限制中间件"""
    def middleware_factory(app):
        return RateLimitMiddleware(app, global_rate_limit)
    return middleware_factory


def create_audit_middleware(enable_audit: bool = True):
    """创建审计中间件"""
    def middleware_factory(app):
        return AuditMiddleware(app, enable_audit)
    return middleware_factory
