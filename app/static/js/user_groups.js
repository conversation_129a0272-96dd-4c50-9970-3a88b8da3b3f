/**
 * 用户组管理前端逻辑
 */

class UserGroupManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalItems = 0;
        this.groups = [];
        this.editingGroup = null;

        this.init();
    }

    getAuthToken() {
        // 从cookie或localStorage获取认证token
        return localStorage.getItem('auth_token') || 'sk-123456';
    }
    
    init() {
        this.bindEvents();
        this.loadStatistics();
        this.loadGroups();
    }
    
    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadStatistics();
            this.loadGroups();
        });
        
        // 创建用户组按钮
        document.getElementById('createGroupBtn').addEventListener('click', () => {
            this.showCreateModal();
        });
        
        // 搜索输入
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.debounce(() => this.filterGroups(), 300)();
        });
        
        // 状态过滤
        document.getElementById('statusFilter').addEventListener('change', () => {
            this.filterGroups();
        });
        
        // 优先级过滤
        document.getElementById('priorityFilter').addEventListener('change', () => {
            this.filterGroups();
        });
        
        // 模态框事件
        document.getElementById('closeModal').addEventListener('click', () => {
            this.hideModal();
        });
        
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.hideModal();
        });
        
        document.getElementById('closeDetailModal').addEventListener('click', () => {
            this.hideDetailModal();
        });
        
        // 表单提交
        document.getElementById('groupForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveGroup();
        });
        
        // 点击模态框外部关闭
        document.getElementById('groupModal').addEventListener('click', (e) => {
            if (e.target.id === 'groupModal') {
                this.hideModal();
            }
        });
        
        document.getElementById('detailModal').addEventListener('click', (e) => {
            if (e.target.id === 'detailModal') {
                this.hideDetailModal();
            }
        });
    }
    
    async loadStatistics() {
        try {
            const response = await fetch('/admin/groups/statistics/overview', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const stats = await response.json();
                this.updateStatistics(stats);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    updateStatistics(stats) {
        document.getElementById('totalGroups').textContent = stats.total_groups || 0;
        document.getElementById('activeGroups').textContent = stats.active_groups || 0;
        document.getElementById('alertGroups').textContent = stats.alert_groups || 0;
        document.getElementById('totalRequests').textContent = this.formatNumber(stats.total_requests || 0);
    }
    
    async loadGroups() {
        try {
            const offset = (this.currentPage - 1) * this.pageSize;
            const response = await fetch(`/admin/groups?limit=${this.pageSize}&offset=${offset}`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.groups = data.user_groups || [];
                this.totalItems = data.total || 0;
                this.renderGroups();
                this.renderPagination();
            }
        } catch (error) {
            console.error('加载用户组失败:', error);
            this.showError('加载用户组失败');
        }
    }
    
    renderGroups() {
        const container = document.getElementById('groupsList');
        
        if (this.groups.length === 0) {
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="text-center">
                        <i class="fas fa-users text-gray-400 text-3xl mb-4"></i>
                        <p class="text-gray-500">暂无用户组</p>
                    </div>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.groups.map(group => this.renderGroupItem(group)).join('');
    }
    
    renderGroupItem(group) {
        const statusClass = this.getStatusClass(group.status);
        const priorityClass = this.getPriorityClass(group.priority);
        
        return `
            <div class="px-6 py-4 hover:bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <i class="fas fa-users text-primary-600"></i>
                            </div>
                        </div>
                        <div class="min-w-0 flex-1">
                            <div class="flex items-center space-x-2">
                                <p class="text-sm font-medium text-gray-900 truncate">${group.group_name}</p>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                                    ${this.getStatusText(group.status)}
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityClass}">
                                    ${this.getPriorityText(group.priority)}
                                </span>
                            </div>
                            <p class="text-sm text-gray-500">${group.group_id}</p>
                            <p class="text-xs text-gray-400 mt-1">${group.description || '无描述'}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right text-sm">
                            <p class="text-gray-900">今日请求: ${this.formatNumber(group.today_requests || 0)}</p>
                            <p class="text-gray-500">配额使用: ${group.quota_usage || 0}%</p>
                            <p class="text-blue-600 text-xs">
                                <i class="fas fa-key mr-1"></i>
                                API密钥: ${group.api_keys_count || 0} 个
                            </p>
                        </div>
                        <div class="flex space-x-1">
                            <button onclick="userGroupManager.viewGroup('${group.group_id}')"
                                    class="text-blue-600 hover:text-blue-900 p-2" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="userGroupManager.editGroup('${group.group_id}')"
                                    class="text-indigo-600 hover:text-indigo-900 p-2" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="userGroupManager.deleteGroup('${group.group_id}')"
                                    class="text-red-600 hover:text-red-900 p-2" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    getStatusClass(status) {
        const classes = {
            'active': 'bg-green-100 text-green-800',
            'inactive': 'bg-gray-100 text-gray-800',
            'suspended': 'bg-red-100 text-red-800'
        };
        return classes[status] || 'bg-gray-100 text-gray-800';
    }
    
    getStatusText(status) {
        const texts = {
            'active': '活跃',
            'inactive': '非活跃',
            'suspended': '暂停'
        };
        return texts[status] || status;
    }
    
    getPriorityClass(priority) {
        const classes = {
            'high': 'bg-red-100 text-red-800',
            'medium': 'bg-yellow-100 text-yellow-800',
            'low': 'bg-blue-100 text-blue-800'
        };
        return classes[priority] || 'bg-gray-100 text-gray-800';
    }
    
    getPriorityText(priority) {
        const texts = {
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return texts[priority] || priority;
    }
    
    renderPagination() {
        const totalPages = Math.ceil(this.totalItems / this.pageSize);
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalItems);
        
        document.getElementById('startItem').textContent = startItem;
        document.getElementById('endItem').textContent = endItem;
        document.getElementById('totalItems').textContent = this.totalItems;
        
        const pagination = document.getElementById('pagination');
        let paginationHTML = '';
        
        // 上一页
        if (this.currentPage > 1) {
            paginationHTML += `
                <button onclick="userGroupManager.goToPage(${this.currentPage - 1})" 
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }
        
        // 页码
        for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
            const isActive = i === this.currentPage;
            paginationHTML += `
                <button onclick="userGroupManager.goToPage(${i})" 
                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            isActive 
                                ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' 
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }">
                    ${i}
                </button>
            `;
        }
        
        // 下一页
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button onclick="userGroupManager.goToPage(${this.currentPage + 1})" 
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }
        
        pagination.innerHTML = paginationHTML;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadGroups();
    }
    
    filterGroups() {
        // 实现搜索和过滤逻辑
        this.currentPage = 1;
        this.loadGroups();
    }
    
    showCreateModal() {
        this.editingGroup = null;
        document.getElementById('modalTitle').textContent = '创建用户组';
        document.getElementById('submitText').textContent = '创建用户组';
        document.getElementById('groupForm').reset();
        document.getElementById('groupModal').classList.remove('hidden');
    }
    
    async editGroup(groupId) {
        try {
            const response = await fetch(`/admin/groups/${groupId}`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const group = await response.json();
                this.editingGroup = group;
                this.populateForm(group);
                document.getElementById('modalTitle').textContent = '编辑用户组';
                document.getElementById('submitText').textContent = '更新用户组';
                document.getElementById('groupModal').classList.remove('hidden');
            }
        } catch (error) {
            console.error('加载用户组详情失败:', error);
            this.showError('加载用户组详情失败');
        }
    }
    
    populateForm(group) {
        document.getElementById('groupId').value = group.group_id;
        document.getElementById('groupName').value = group.group_name;
        document.getElementById('priority').value = group.priority;
        document.getElementById('status').value = group.status;
        document.getElementById('description').value = group.description || '';
        
        // 配额设置
        if (group.quota_config) {
            document.getElementById('dailyQuota').value = group.quota_config.daily_quota || '';
            document.getElementById('monthlyQuota').value = group.quota_config.monthly_quota || '';
            document.getElementById('requestsPerMinute').value = group.quota_config.requests_per_minute || '';
        }
        
        // 模型配置
        if (group.model_config) {
            document.getElementById('enableGemini').checked = group.model_config.enable_gemini !== false;
            document.getElementById('enableFallback').checked = group.model_config.enable_fallback !== false;
            document.getElementById('enableCache').checked = group.model_config.enable_cache !== false;
        }
    }
    
    async saveGroup() {
        const formData = new FormData(document.getElementById('groupForm'));

        // 收集模型Token消耗倍率
        const tokenRates = {};
        const rateInputs = document.querySelectorAll('[id^="tokenRate_"]');
        rateInputs.forEach(input => {
            const modelName = input.id.replace('tokenRate_', '');
            const rate = parseFloat(input.value) || 1.0;
            tokenRates[modelName] = rate;
        });

        const groupData = {
            group_id: formData.get('groupId'),
            group_name: formData.get('groupName'),
            priority: parseInt(formData.get('priority')) || 5,
            status: formData.get('status') || 'ACTIVE',
            description: formData.get('description') || '',
            token_limit: parseInt(formData.get('tokenLimit')) || 0,
            expiry_date: formData.get('expiryDate') || null,
            token_rates: tokenRates,
            auto_fallback: formData.get('autoFallback') === 'on',
            context_isolation: formData.get('contextIsolation') === 'on',
            no_fallback_models: []
        };
        
        try {
            this.setLoading(true);
            
            const url = this.editingGroup ? `/admin/groups/${this.editingGroup.group_id}` : '/admin/groups';
            const method = this.editingGroup ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(groupData)
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.error === 'GROUP_EXISTS') {
                    this.showError(result.message || '用户组已存在');
                } else {
                    this.hideModal();
                    this.loadGroups();
                    this.loadStatistics();

                    // 如果是创建用户组且返回了API密钥，显示API密钥
                    if (!this.editingGroup && result.api_key) {
                        this.showApiKeyModal(result.api_key, result.group_id);
                    } else {
                        this.showSuccess(this.editingGroup ? '用户组更新成功' : '用户组创建成功');
                    }
                }
            } else {
                const error = await response.json();
                this.showError(error.detail || '操作失败');
            }
        } catch (error) {
            console.error('保存用户组失败:', error);
            this.showError('保存用户组失败');
        } finally {
            this.setLoading(false);
        }
    }
    
    async deleteGroup(groupId) {
        if (!confirm('确定要删除这个用户组吗？此操作不可恢复。')) {
            return;
        }
        
        try {
            const response = await fetch(`/admin/groups/${groupId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            if (response.ok) {
                this.loadGroups();
                this.loadStatistics();
                this.showSuccess('用户组删除成功');
            } else {
                const error = await response.json();
                this.showError(error.detail || '删除失败');
            }
        } catch (error) {
            console.error('删除用户组失败:', error);
            this.showError('删除用户组失败');
        }
    }
    
    async viewGroup(groupId) {
        try {
            const response = await fetch(`/admin/groups/${groupId}`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const details = await response.json();
                this.showGroupDetails(details);
            }
        } catch (error) {
            console.error('加载用户组详情失败:', error);
            this.showError('加载用户组详情失败');
        }
    }
    
    showGroupDetails(details) {
        const content = document.getElementById('detailContent');

        // 生成API密钥列表HTML
        const apiKeysHtml = details.api_keys && details.api_keys.length > 0
            ? details.api_keys.map(key => `
                <div class="flex items-center justify-between py-2 px-3 bg-white rounded border">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium text-gray-900">${key.key_name || 'Unnamed Key'}</span>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${key.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${key.is_active ? '活跃' : '非活跃'}
                            </span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">
                            <code class="bg-gray-100 px-2 py-1 rounded">${key.api_key}</code>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="navigator.clipboard.writeText('${key.api_key.replace('...', '')}'); this.innerHTML='<i class=\\'fas fa-check\\'></i>'; setTimeout(() => this.innerHTML='<i class=\\'fas fa-copy\\'></i>', 2000)"
                                class="text-blue-600 hover:text-blue-900 p-1" title="复制API密钥">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            `).join('')
            : '<p class="text-sm text-gray-500 text-center py-4">暂无API密钥</p>';

        content.innerHTML = `
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 基本信息 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-900 mb-3">基本信息</h4>
                    <dl class="space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">用户组ID:</dt>
                            <dd class="text-sm text-gray-900">${details.group_id}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">名称:</dt>
                            <dd class="text-sm text-gray-900">${details.group_name}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">状态:</dt>
                            <dd class="text-sm text-gray-900">${this.getStatusText(details.status)}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">优先级:</dt>
                            <dd class="text-sm text-gray-900">${this.getPriorityText(details.priority)}</dd>
                        </div>
                    </dl>
                </div>

                <!-- 使用统计 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-900 mb-3">使用统计</h4>
                    <dl class="space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">今日请求:</dt>
                            <dd class="text-sm text-gray-900">${this.formatNumber(details.today_requests || 0)}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">本月请求:</dt>
                            <dd class="text-sm text-gray-900">${this.formatNumber(details.month_requests || 0)}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">成功率:</dt>
                            <dd class="text-sm text-gray-900">${details.success_rate || 0}%</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">配额使用:</dt>
                            <dd class="text-sm text-gray-900">${details.quota_usage || 0}%</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- API密钥信息 -->
            <div class="mt-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900">API密钥</h4>
                        <span class="text-sm text-gray-500">${details.api_keys ? details.api_keys.length : 0} 个密钥</span>
                    </div>
                    <div class="space-y-2">
                        ${apiKeysHtml}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('detailModal').classList.remove('hidden');
    }
    
    hideModal() {
        document.getElementById('groupModal').classList.add('hidden');
    }

    showApiKeyModal(apiKey, groupId) {
        // 创建API密钥显示模态框
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <i class="fas fa-key text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2 text-center">用户组创建成功</h3>
                    <div class="mt-4 px-7 py-3">
                        <p class="text-sm text-gray-500 text-center mb-4">
                            用户组 <strong>${groupId}</strong> 已创建成功，并自动生成了API密钥：
                        </p>
                        <div class="bg-gray-100 p-3 rounded-md">
                            <div class="flex items-center justify-between">
                                <code class="text-sm font-mono text-gray-800 break-all">${apiKey}</code>
                                <button onclick="navigator.clipboard.writeText('${apiKey}'); this.innerHTML='<i class=\\"fas fa-check\\"></i>'"
                                        class="ml-2 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-red-500 mt-2 text-center">
                            请妥善保存此API密钥，关闭后将无法再次查看完整密钥。
                        </p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button onclick="this.closest('.fixed').remove()"
                                class="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-300">
                            我已保存，关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 3秒后自动聚焦到关闭按钮
        setTimeout(() => {
            const closeBtn = modal.querySelector('button');
            if (closeBtn) closeBtn.focus();
        }, 100);
    }
    
    hideDetailModal() {
        document.getElementById('detailModal').classList.add('hidden');
    }
    
    setLoading(loading) {
        const submitBtn = document.getElementById('submitText');
        const spinner = document.getElementById('submitSpinner');
        
        if (loading) {
            submitBtn.textContent = '保存中...';
            spinner.classList.remove('hidden');
        } else {
            submitBtn.textContent = this.editingGroup ? '更新用户组' : '创建用户组';
            spinner.classList.add('hidden');
        }
    }
    
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type) {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 等待DOM加载完成后初始化用户组管理器
document.addEventListener('DOMContentLoaded', function() {
    window.userGroupManager = new UserGroupManager();
    console.log('UserGroupManager initialized on DOMContentLoaded');
});
