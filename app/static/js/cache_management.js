/**
 * 缓存管理中心前端逻辑
 */

class CacheManager {
    constructor() {
        this.currentGroupId = '';
        this.warmupInterval = null;
        this.refreshInterval = null;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadUserGroups();
        this.loadGlobalData(); // 默认加载全局数据
        this.startAutoRefresh();
    }
    
    bindEvents() {
        try {
            // 刷新按钮
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    this.refreshAll();
                });
            }

            // 移除了导航栏的用户组选择器，统一使用用户组配置面板中的选择器
        
        // 缓存配置表单
        document.getElementById('cacheConfigForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCacheConfig();
        });
        
        // 清空缓存按钮
        document.getElementById('clearCacheBtn').addEventListener('click', () => {
            this.clearCache();
        });
        
        // 预热按钮
        document.getElementById('startWarmupBtn').addEventListener('click', () => {
            this.startWarmup();
        });
        
        document.getElementById('stopWarmupBtn').addEventListener('click', () => {
            this.stopWarmup();
        });

        // 配置选项卡切换
        const globalConfigTab = document.getElementById('globalConfigTab');
        const groupConfigTab = document.getElementById('groupConfigTab');

        if (globalConfigTab) {
            globalConfigTab.addEventListener('click', () => {
                this.switchConfigTab('global');
            });
        }

        if (groupConfigTab) {
            groupConfigTab.addEventListener('click', () => {
                this.switchConfigTab('group');
            });
        }

        // 保存全局配置按钮
        const saveGlobalConfigBtn = document.getElementById('saveGlobalConfigBtn');
        if (saveGlobalConfigBtn) {
            saveGlobalConfigBtn.addEventListener('click', () => {
                this.saveGlobalCacheConfig();
            });
        }

        // 用户组配置选择器 (统一处理统计查看和配置功能)
        const groupConfigSelector = document.getElementById('groupConfigSelector');
        if (groupConfigSelector) {
            groupConfigSelector.addEventListener('change', (e) => {
                this.currentGroupId = e.target.value;
                if (this.currentGroupId) {
                    // 同时加载用户组缓存统计和配置
                    this.loadGroupData();
                    this.loadGroupCacheConfig();
                    this.showGroupCacheStats();
                } else {
                    this.clearGroupConfigForm();
                    this.hideGroupCacheStats();
                    this.loadGlobalData();
                }
            });
        }
    } catch (error) {
        console.error("Error binding events:", error);
    }
    }

    switchConfigTab(tab) {
        const globalTab = document.getElementById('globalConfigTab');
        const groupTab = document.getElementById('groupConfigTab');
        const globalPanel = document.getElementById('globalConfigPanel');
        const groupPanel = document.getElementById('groupConfigPanel');

        if (tab === 'global') {
            globalTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            globalTab.classList.remove('border-transparent', 'text-gray-500');
            groupTab.classList.remove('active', 'border-blue-500', 'text-blue-600');
            groupTab.classList.add('border-transparent', 'text-gray-500');

            globalPanel.classList.remove('hidden');
            groupPanel.classList.add('hidden');
        } else {
            groupTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            groupTab.classList.remove('border-transparent', 'text-gray-500');
            globalTab.classList.remove('active', 'border-blue-500', 'text-blue-600');
            globalTab.classList.add('border-transparent', 'text-gray-500');

            groupPanel.classList.remove('hidden');
            globalPanel.classList.add('hidden');
        }
    }
    
    async loadUserGroups() {
        try {
            const response = await fetch('/admin/groups?limit=100&offset=0', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                const groups = data.user_groups || [];
                this.populateGroupSelector(groups);
            } else if (response.status === 401) {
                this.showError('认证失败，请重新登录');
                window.location.href = '/';
            } else {
                this.showError('加载用户组失败');
            }
        } catch (error) {
            console.error('加载用户组失败:', error);
            this.showError('加载用户组失败');
        }
    }

    getAuthToken() {
        // 从cookie或localStorage获取认证token
        return localStorage.getItem('auth_token') || 'sk-123456';
    }
    
    populateGroupSelector(groups) {
        const configSelector = document.getElementById('groupConfigSelector');

        // 更新用户组配置选择器
        if (configSelector) {
            configSelector.innerHTML = '<option value="">请选择用户组</option>';

            groups.forEach(group => {
                const configOption = document.createElement('option');
                configOption.value = group.group_id || group.id;
                configOption.textContent = group.group_name || group.name;
                configSelector.appendChild(configOption);
            });
        }
    }
    
    async loadGroupData() {
        if (!this.currentGroupId) return;

        this.showLoading();
        try {
            await Promise.all([
                this.loadCacheStatistics(),
                this.loadCacheConfig()
            ]);
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('加载数据失败');
        } finally {
            this.hideLoading();
        }
    }

    async loadGlobalData() {
        this.showLoading();
        try {
            // 加载全局缓存统计和配置
            await Promise.all([
                this.loadGlobalCacheStatistics(),
                this.loadGlobalCacheConfig()
            ]);
        } catch (error) {
            console.error('加载全局数据失败:', error);
            this.showError('加载全局数据失败');
        } finally {
            this.hideLoading();
        }
    }

    async loadGlobalCacheConfig() {
        try {
            const response = await fetch('/cache/config/global', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const config = await response.json();
                this.populateGlobalConfigForm(config);
            }
        } catch (error) {
            console.error('加载全局缓存配置失败:', error);
        }
    }

    populateGlobalConfigForm(config) {
        // 填充全局配置表单
        if (document.getElementById('globalCacheEnabled')) {
            document.getElementById('globalCacheEnabled').checked = config.cache_enabled || false;
        }
        if (document.getElementById('globalCacheTtl')) {
            document.getElementById('globalCacheTtl').value = config.default_ttl || 3600;
        }
        if (document.getElementById('globalMaxCacheSize')) {
            document.getElementById('globalMaxCacheSize').value = config.max_cache_size || 1000;
        }
        if (document.getElementById('globalEnableSemanticCache')) {
            document.getElementById('globalEnableSemanticCache').checked = config.enable_semantic_cache || false;
        }
    }

    async saveGlobalCacheConfig() {
        const config = {
            cache_enabled: document.getElementById('globalCacheEnabled')?.checked || false,
            default_ttl: parseInt(document.getElementById('globalCacheTtl')?.value) || 3600,
            max_cache_size: parseInt(document.getElementById('globalMaxCacheSize')?.value) || 1000,
            enable_semantic_cache: document.getElementById('globalEnableSemanticCache')?.checked || false
        };

        try {
            const response = await fetch('/cache/config/global', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(config)
            });

            if (response.ok) {
                this.showSuccess('全局缓存配置已保存');
                await this.loadGlobalData(); // 重新加载数据
            } else {
                this.showError('保存全局缓存配置失败');
            }
        } catch (error) {
            console.error('保存全局缓存配置失败:', error);
            this.showError('保存全局缓存配置失败');
        }
    }
    
    async loadCacheStatistics() {
        try {
            const endpoint = this.currentGroupId
                ? `/cache/groups/${this.currentGroupId}/statistics`
                : '/cache/statistics/global';

            const response = await fetch(endpoint, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const stats = await response.json();
                this.updateStatisticsDisplay(stats);
            }
        } catch (error) {
            console.error('加载缓存统计失败:', error);
        }
    }
    
    async loadGlobalCacheStatistics() {
        try {
            const response = await fetch('/cache/statistics/global', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const stats = await response.json();
                this.updateStatisticsDisplay(stats);
            }
        } catch (error) {
            console.error('加载全局缓存统计失败:', error);
        }
    }
    
    async loadCacheConfig() {
        if (!this.currentGroupId) return;

        try {
            const response = await fetch(`/cache/groups/${this.currentGroupId}/config`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const config = await response.json();
                this.populateConfigForm(config);
            }
        } catch (error) {
            console.error('加载缓存配置失败:', error);
        }
    }
    
    // 缓存分析数据已整合到监控大屏和分析报告中心
    // 此处不再重复实现图表功能
    
    updateStatisticsDisplay(stats) {
        // 更新全局统计显示
        document.getElementById('hitRate').textContent = `${(stats.hit_rate || 0).toFixed(1)}%`;
        document.getElementById('cacheSize').textContent = this.formatBytes(stats.cache_size || 0);
        document.getElementById('avgResponseTime').textContent = `${(stats.avg_response_time || 0).toFixed(0)}ms`;
        document.getElementById('utilization').textContent = `${(stats.utilization || 0).toFixed(1)}%`;

        // 如果当前选择了用户组，也更新用户组统计显示
        if (this.currentGroupId) {
            this.updateGroupCacheStats(stats);
        }
    }
    
    populateConfigForm(config) {
        document.getElementById('cacheMode').value = config.cache_mode || 'isolated';
        document.getElementById('cacheStrategy').value = config.strategy || 'lru';
        document.getElementById('maxSize').value = config.max_size || 1000;
        document.getElementById('defaultTtl').value = config.default_ttl || 3600;
        document.getElementById('enableSemanticCache').checked = config.enable_semantic_cache || false;
        document.getElementById('enableCrossModelCache').checked = config.enable_cross_model_cache || false;
    }
    
    async saveCacheConfig() {
        if (!this.currentGroupId) {
            this.showError('请先选择用户组');
            return;
        }
        
        const config = {
            cache_mode: document.getElementById('cacheMode').value,
            strategy: document.getElementById('cacheStrategy').value,
            max_size: parseInt(document.getElementById('maxSize').value),
            default_ttl: parseInt(document.getElementById('defaultTtl').value),
            enable_semantic_cache: document.getElementById('enableSemanticCache').checked,
            enable_cross_model_cache: document.getElementById('enableCrossModelCache').checked
        };
        
        try {
            const response = await fetch(`/cache/groups/${this.currentGroupId}/config`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(config)
            });
            
            if (response.ok) {
                this.showSuccess('缓存配置已保存');
                this.loadCacheStatistics();
            } else {
                this.showError('保存配置失败');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showError('保存配置失败');
        }
    }
    
    async clearCache() {
        if (!this.currentGroupId) {
            this.showError('请先选择用户组');
            return;
        }
        
        if (!confirm('确定要清空缓存吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            const response = await fetch(`/cache/groups/${this.currentGroupId}/clear`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            
            if (response.ok) {
                this.showSuccess('缓存已清空');
                this.loadCacheStatistics();
            } else {
                this.showError('清空缓存失败');
            }
        } catch (error) {
            console.error('清空缓存失败:', error);
            this.showError('清空缓存失败');
        }
    }
    
    async startWarmup() {
        if (!this.currentGroupId) {
            this.showError('请先选择用户组');
            return;
        }
        
        const requestsText = document.getElementById('warmupRequests').value.trim();
        if (!requestsText) {
            this.showError('请输入预热请求');
            return;
        }
        
        let requests;
        try {
            requests = JSON.parse(requestsText);
        } catch (error) {
            this.showError('请求格式错误，请输入有效的JSON');
            return;
        }
        
        try {
            const response = await fetch(`/cache/groups/${this.currentGroupId}/warmup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({ common_requests: requests })
            });
            
            if (response.ok) {
                this.showSuccess('缓存预热已开始');
                this.startWarmupProgress();
            } else {
                this.showError('启动预热失败');
            }
        } catch (error) {
            console.error('启动预热失败:', error);
            this.showError('启动预热失败');
        }
    }
    
    startWarmupProgress() {
        document.getElementById('warmupProgress').classList.remove('hidden');
        document.getElementById('startWarmupBtn').disabled = true;
        document.getElementById('stopWarmupBtn').disabled = false;
        
        let progress = 0;
        this.warmupInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress >= 100) {
                progress = 100;
                this.stopWarmup();
            }
            
            document.getElementById('warmupProgressBar').style.width = `${progress}%`;
            document.getElementById('warmupStatus').textContent = 
                progress >= 100 ? '预热完成' : `预热进行中... ${progress.toFixed(0)}%`;
        }, 500);
    }
    
    stopWarmup() {
        if (this.warmupInterval) {
            clearInterval(this.warmupInterval);
            this.warmupInterval = null;
        }
        
        document.getElementById('warmupProgress').classList.add('hidden');
        document.getElementById('startWarmupBtn').disabled = false;
        document.getElementById('stopWarmupBtn').disabled = true;
        document.getElementById('warmupProgressBar').style.width = '0%';
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    refreshAll() {
        if (this.currentGroupId) {
            this.loadGroupData();
        } else {
            this.loadGlobalData();
        }
    }

    showGroupCacheStats() {
        const statsElement = document.getElementById('groupCacheStats');
        if (statsElement) {
            statsElement.classList.remove('hidden');
        }
    }

    hideGroupCacheStats() {
        const statsElement = document.getElementById('groupCacheStats');
        if (statsElement) {
            statsElement.classList.add('hidden');
        }
    }

    updateGroupCacheStats(stats) {
        // 更新用户组缓存统计显示
        const hitRate = stats.hit_rate || 0;
        const cacheSize = this.formatBytes(stats.cache_size || 0);
        const totalRequests = stats.total_requests || 0;
        const avgResponseTime = stats.avg_response_time || 0;

        document.getElementById('groupHitRate').textContent = `${hitRate.toFixed(1)}%`;
        document.getElementById('groupCacheSize').textContent = cacheSize;
        document.getElementById('groupTotalRequests').textContent = this.formatNumber(totalRequests);
        document.getElementById('groupAvgResponseTime').textContent = `${avgResponseTime}ms`;
    }

    async loadGroupCacheConfig() {
        if (!this.currentGroupId) return;

        try {
            const response = await fetch(`/cache/groups/${this.currentGroupId}/config`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            if (response.ok) {
                const config = await response.json();
                this.populateGroupConfigForm(config);
            } else {
                this.showError('加载用户组缓存配置失败');
            }
        } catch (error) {
            console.error('加载用户组缓存配置失败:', error);
            this.showError('加载用户组缓存配置失败');
        }
    }

    populateGroupConfigForm(config) {
        document.getElementById('cacheMode').value = config.cache_mode || 'isolated';
        document.getElementById('cacheStrategy').value = config.strategy || 'lru';
        document.getElementById('maxSize').value = config.max_size || 1000;
        document.getElementById('defaultTtl').value = config.default_ttl || 3600;
        document.getElementById('enableSemanticCache').checked = config.enable_semantic_cache || false;
        document.getElementById('enableCrossModelCache').checked = config.enable_cross_model_cache || false;
    }

    clearGroupConfigForm() {
        document.getElementById('cacheMode').value = 'isolated';
        document.getElementById('cacheStrategy').value = 'lru';
        document.getElementById('maxSize').value = 1000;
        document.getElementById('defaultTtl').value = 3600;
        document.getElementById('enableSemanticCache').checked = false;
        document.getElementById('enableCrossModelCache').checked = false;
    }
    
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadCacheStatistics();
        }, 30000); // 每30秒刷新一次统计数据
    }
    
    showLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
    }
    
    hideLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // 图表功能已移除，避免与监控大屏重复
    // 缓存管理专注于配置和操作功能
}

// 等待DOM加载完成后初始化缓存管理器
document.addEventListener('DOMContentLoaded', function() {
    window.cacheManager = new CacheManager();
    console.log('CacheManager initialized on DOMContentLoaded');
});