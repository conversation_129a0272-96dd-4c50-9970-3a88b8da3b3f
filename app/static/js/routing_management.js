/**
 * 路由管理中心 JavaScript
 * 提供路由策略配置、端点管理、健康监控等功能
 */

class RoutingManager {
    constructor() {
        this.endpoints = [];
        this.currentStrategy = 'round_robin';
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialData();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        try {
            // 刷新按钮
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    this.refreshAll();
                });
            }

            // 策略更新
            const updateStrategyBtn = document.getElementById('updateStrategyBtn');
            if (updateStrategyBtn) {
                updateStrategyBtn.addEventListener('click', () => {
                    this.updateStrategy();
                });
            }

            // 策略重置
            const resetStrategyBtn = document.getElementById('resetStrategyBtn');
            if (resetStrategyBtn) {
                resetStrategyBtn.addEventListener('click', () => {
                    this.resetStrategy();
                });
            }

            // 添加端点
            const addEndpointBtn = document.getElementById('addEndpointBtn');
            if (addEndpointBtn) {
                addEndpointBtn.addEventListener('click', () => {
                    this.showAddEndpointModal();
                });
            }

            // 测试路由
            const testRoutingBtn = document.getElementById('testRoutingBtn');
            if (testRoutingBtn) {
                testRoutingBtn.addEventListener('click', () => {
                    this.testRouting();
                });
            }

            // 模态框事件
            const cancelAddEndpoint = document.getElementById('cancelAddEndpoint');
            if (cancelAddEndpoint) {
                cancelAddEndpoint.addEventListener('click', () => {
                    this.hideAddEndpointModal();
                });
            }

            const confirmAddEndpoint = document.getElementById('confirmAddEndpoint');
            if (confirmAddEndpoint) {
                confirmAddEndpoint.addEventListener('click', () => {
                    this.addEndpoint();
                });
            }

            // 点击模态框外部关闭
            const addEndpointModal = document.getElementById('addEndpointModal');
            if (addEndpointModal) {
                addEndpointModal.addEventListener('click', (e) => {
                    if (e.target.id === 'addEndpointModal') {
                        this.hideAddEndpointModal();
                    }
                });
            }

            console.log('路由管理事件绑定完成');
        } catch (error) {
            console.error('绑定事件失败:', error);
        }
    }
    
    async loadInitialData() {
        this.showLoading();
        try {
            await Promise.all([
                this.loadRoutingHealth(),
                this.loadEndpoints(),
                this.loadOptimizationSuggestions()
            ]);
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showError('加载初始数据失败');
        } finally {
            this.hideLoading();
        }
    }
    
    getAuthToken() {
        return localStorage.getItem('auth_token') || 'sk-123456';
    }

    async loadRoutingHealth() {
        try {
            const response = await fetch('/routing/health', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateHealthDisplay(data);
            } else {
                // 显示默认数据
                this.updateHealthDisplay({
                    overall_health: 'unknown',
                    total_endpoints: 0,
                    healthy_endpoints: 0,
                    avg_response_time: 0,
                    success_rate: 0
                });
            }
        } catch (error) {
            console.error('加载路由健康状态失败:', error);
            this.updateHealthDisplay({
                overall_health: 'unknown',
                total_endpoints: 0,
                healthy_endpoints: 0,
                avg_response_time: 0,
                success_rate: 0
            });
        }
    }
    
    async loadEndpoints() {
        try {
            const response = await fetch('/routing/endpoints', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.endpoints = data.endpoints || {};
                this.currentStrategy = data.summary?.current_strategy || 'round_robin';
                this.updateEndpointsDisplay();
                this.updateStrategySelector();
            } else {
                this.endpoints = {};
                this.updateEndpointsDisplay();
            }
        } catch (error) {
            console.error('加载端点信息失败:', error);
            this.endpoints = {};
            this.updateEndpointsDisplay();
        }
    }
    
    async loadOptimizationSuggestions() {
        try {
            const response = await fetch('/routing/optimization/suggestions');
            if (response.ok) {
                const data = await response.json();
                this.updateOptimizationSuggestions(data.optimization_suggestions || []);
            } else {
                this.updateOptimizationSuggestions([]);
            }
        } catch (error) {
            console.error('加载优化建议失败:', error);
            this.updateOptimizationSuggestions([]);
        }
    }
    
    updateHealthDisplay(health) {
        // 更新健康状态卡片
        const systemHealth = document.getElementById('systemHealth');
        const activeEndpoints = document.getElementById('activeEndpoints');
        const avgResponseTime = document.getElementById('avgResponseTime');
        const successRate = document.getElementById('successRate');
        
        if (systemHealth) {
            const healthText = health.overall_health === 'healthy' ? '健康' : 
                             health.overall_health === 'warning' ? '警告' : 
                             health.overall_health === 'critical' ? '严重' : '未知';
            systemHealth.textContent = healthText;
            systemHealth.className = `text-lg font-medium ${
                health.overall_health === 'healthy' ? 'text-green-600' :
                health.overall_health === 'warning' ? 'text-yellow-600' :
                health.overall_health === 'critical' ? 'text-red-600' : 'text-gray-600'
            }`;
        }
        
        if (activeEndpoints) {
            activeEndpoints.textContent = `${health.healthy_endpoints || 0}/${health.total_endpoints || 0}`;
        }
        
        if (avgResponseTime) {
            avgResponseTime.textContent = `${(health.avg_response_time || 0).toFixed(2)}ms`;
        }
        
        if (successRate) {
            successRate.textContent = `${((health.success_rate || 0) * 100).toFixed(1)}%`;
        }
    }
    
    updateEndpointsDisplay() {
        const tbody = document.getElementById('endpointsTableBody');
        const totalEndpoints = document.getElementById('totalEndpoints');
        const healthyEndpoints = document.getElementById('healthyEndpoints');
        const unhealthyEndpoints = document.getElementById('unhealthyEndpoints');
        
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        let total = 0;
        let healthy = 0;
        let unhealthy = 0;
        
        Object.entries(this.endpoints).forEach(([provider, endpoints]) => {
            endpoints.forEach(endpoint => {
                total++;
                if (endpoint.status === 'healthy') healthy++;
                else unhealthy++;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${provider}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${endpoint.url}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            endpoint.status === 'healthy' ? 'bg-green-100 text-green-800' :
                            endpoint.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                        }">
                            ${endpoint.status === 'healthy' ? '健康' :
                              endpoint.status === 'warning' ? '警告' : '故障'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${endpoint.current_load || 0}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${(endpoint.avg_response_time || 0).toFixed(2)}ms</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${((endpoint.success_rate || 0) * 100).toFixed(1)}%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="routingManager.removeEndpoint('${provider}', '${endpoint.url}')" 
                                class="text-red-600 hover:text-red-900">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button onclick="routingManager.testEndpoint('${provider}')" 
                                class="ml-3 text-blue-600 hover:text-blue-900">
                            <i class="fas fa-vial"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        });
        
        // 更新统计信息
        if (totalEndpoints) totalEndpoints.textContent = total;
        if (healthyEndpoints) healthyEndpoints.textContent = healthy;
        if (unhealthyEndpoints) unhealthyEndpoints.textContent = unhealthy;
    }
    
    updateStrategySelector() {
        const selector = document.getElementById('strategySelector');
        if (selector && this.currentStrategy) {
            selector.value = this.currentStrategy;
        }
    }
    
    updateOptimizationSuggestions(suggestions) {
        const container = document.getElementById('optimizationSuggestions');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (!suggestions || suggestions.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-gray-500">
                    <i class="fas fa-check-circle text-2xl mb-2"></i>
                    <p>当前路由配置良好，暂无优化建议</p>
                </div>
            `;
            return;
        }
        
        suggestions.forEach(suggestion => {
            const div = document.createElement('div');
            div.className = `p-4 rounded-lg border-l-4 ${
                suggestion.priority === 'high' ? 'border-red-400 bg-red-50' :
                suggestion.priority === 'medium' ? 'border-yellow-400 bg-yellow-50' :
                'border-blue-400 bg-blue-50'
            }`;
            
            div.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas ${
                            suggestion.priority === 'high' ? 'fa-exclamation-triangle text-red-400' :
                            suggestion.priority === 'medium' ? 'fa-exclamation-circle text-yellow-400' :
                            'fa-info-circle text-blue-400'
                        }"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-gray-900">${suggestion.title}</h4>
                        <p class="text-sm text-gray-700 mt-1">${suggestion.description}</p>
                        ${suggestion.action ? `
                            <button class="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium">
                                ${suggestion.action}
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
            
            container.appendChild(div);
        });
    }

    async updateStrategy() {
        const selector = document.getElementById('strategySelector');
        const newStrategy = selector.value;

        if (newStrategy === this.currentStrategy) {
            this.showNotification('策略未发生变化', 'info');
            return;
        }

        try {
            const response = await fetch('/routing/strategy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    strategy: newStrategy
                })
            });

            if (response.ok) {
                this.currentStrategy = newStrategy;
                this.showNotification('负载均衡策略更新成功', 'success');
                await this.loadEndpoints(); // 重新加载以获取最新状态
            } else {
                const error = await response.json();
                this.showError(`更新策略失败: ${error.detail || '未知错误'}`);
            }
        } catch (error) {
            console.error('更新策略失败:', error);
            this.showError('更新策略失败');
        }
    }

    resetStrategy() {
        const selector = document.getElementById('strategySelector');
        selector.value = this.currentStrategy;
        this.showNotification('策略已重置', 'info');
    }

    showAddEndpointModal() {
        const modal = document.getElementById('addEndpointModal');
        modal.classList.add('show');
        modal.style.display = 'flex';

        // 清空表单
        document.getElementById('addEndpointForm').reset();
    }

    hideAddEndpointModal() {
        const modal = document.getElementById('addEndpointModal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    async addEndpoint() {
        const provider = document.getElementById('endpointProvider').value.trim();
        const url = document.getElementById('endpointUrl').value.trim();
        const region = document.getElementById('endpointRegion').value.trim() || 'default';
        const priority = parseInt(document.getElementById('endpointPriority').value) || 1;
        const weight = parseFloat(document.getElementById('endpointWeight').value) || 1.0;
        const maxConcurrent = parseInt(document.getElementById('endpointMaxConcurrent').value) || 100;
        const timeout = parseFloat(document.getElementById('endpointTimeout').value) || 30.0;

        if (!provider || !url) {
            this.showError('请填写提供商和端点URL');
            return;
        }

        try {
            const response = await fetch(`/routing/endpoints/${provider}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    url: url,
                    provider: provider,
                    region: region,
                    priority: priority,
                    weight: weight,
                    max_concurrent: maxConcurrent,
                    timeout: timeout
                })
            });

            if (response.ok) {
                this.hideAddEndpointModal();
                this.showNotification('端点添加成功', 'success');
                await this.loadEndpoints();
                await this.loadRoutingHealth();
            } else {
                const error = await response.json();
                this.showError(`添加端点失败: ${error.detail || '未知错误'}`);
            }
        } catch (error) {
            console.error('添加端点失败:', error);
            this.showError('添加端点失败');
        }
    }

    async removeEndpoint(provider, url) {
        if (!confirm(`确定要删除端点 ${url} 吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/routing/endpoints/${provider}?url=${encodeURIComponent(url)}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            if (response.ok) {
                this.showNotification('端点删除成功', 'success');
                await this.loadEndpoints();
                await this.loadRoutingHealth();
            } else {
                const error = await response.json();
                this.showError(`删除端点失败: ${error.detail || '未知错误'}`);
            }
        } catch (error) {
            console.error('删除端点失败:', error);
            this.showError('删除端点失败');
        }
    }

    async testEndpoint(provider) {
        try {
            const response = await fetch(`/routing/test/${provider}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.status === 'success') {
                    this.showNotification(`路由测试成功: ${result.selected_endpoint.url}`, 'success');
                } else {
                    this.showNotification(`路由测试警告: ${result.message}`, 'warning');
                }
            } else {
                const error = await response.json();
                this.showError(`路由测试失败: ${error.detail || '未知错误'}`);
            }
        } catch (error) {
            console.error('路由测试失败:', error);
            this.showError('路由测试失败');
        }
    }

    async testRouting() {
        // 测试所有提供商的路由
        const providers = Object.keys(this.endpoints);
        if (providers.length === 0) {
            this.showNotification('没有可测试的端点', 'warning');
            return;
        }

        this.showNotification('开始路由测试...', 'info');

        for (const provider of providers) {
            await this.testEndpoint(provider);
            // 添加小延迟避免过快的请求
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    async refreshAll() {
        this.showNotification('正在刷新数据...', 'info');
        await this.loadInitialData();
        this.showNotification('数据刷新完成', 'success');
    }

    startAutoRefresh() {
        // 每30秒自动刷新一次健康状态
        this.refreshInterval = setInterval(() => {
            this.loadRoutingHealth();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    showLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showError(message) {
        this.showNotification(message, 'error');
    }
}

// 等待DOM加载完成后初始化路由管理器
document.addEventListener('DOMContentLoaded', function() {
    window.routingManager = new RoutingManager();
});
