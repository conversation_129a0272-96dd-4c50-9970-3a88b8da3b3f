/**
 * System Settings Center JavaScript
 * Provides context isolation config, system parameters, integration config
 */

class SettingsManager {
    constructor() {
        this.configSections = {
            context: 'context',
            system: 'system', 
            integration: 'integration',
            advanced: 'advanced'
        };
        this.currentSection = 'context';
        this.currentGroupId = null;
        this.originalConfigs = {};
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUserGroups();
        this.loadContextIsolationConfig();
        this.initializeConfigSearch();
    }
    
    bindEvents() {
        // 配置分类标签切换
        document.querySelectorAll('.config-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const section = tab.dataset.section;
                this.switchSection(section);
            });
        });
        
        // 配置范围切换
        document.querySelectorAll('input[name="configScope"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleScopeChange(e.target.value);
            });
        });
        
        // 用户组选择
        document.getElementById('groupSelector').addEventListener('change', (e) => {
            this.currentGroupId = e.target.value;
            this.loadContextIsolationConfig();
        });
        
        // 内存阈值滑块
        document.getElementById('memoryThreshold').addEventListener('input', (e) => {
            document.getElementById('memoryThresholdValue').textContent = 
                Math.round(e.target.value * 100) + '%';
        });
        
        // 保存按钮
        document.getElementById('saveContextBtn').addEventListener('click', () => {
            this.saveContextIsolationConfig();
        });
        
        document.getElementById('saveSystemBtn').addEventListener('click', () => {
            this.saveSystemConfig();
        });
        
        document.getElementById('saveIntegrationBtn').addEventListener('click', () => {
            this.saveIntegrationConfig();
        });
        
        document.getElementById('saveAdvancedBtn').addEventListener('click', () => {
            this.saveAdvancedConfig();
        });
        
        // 重置按钮
        document.getElementById('resetContextBtn').addEventListener('click', () => {
            this.resetContextConfig();
        });
        
        document.getElementById('resetSystemBtn').addEventListener('click', () => {
            this.resetSystemConfig();
        });
        
        document.getElementById('resetIntegrationBtn').addEventListener('click', () => {
            this.resetIntegrationConfig();
        });
        
        document.getElementById('resetAdvancedBtn').addEventListener('click', () => {
            this.resetAdvancedConfig();
        });
        
        // 其他按钮
        document.getElementById('exportConfigBtn').addEventListener('click', () => {
            this.exportConfig();
        });
        
        document.getElementById('resetConfigBtn').addEventListener('click', () => {
            this.showConfirmDialog('重置所有配置', '这将重置所有配置到默认值，此操作不可撤销。', () => {
                this.resetAllConfig();
            });
        });
        
        document.getElementById('testIntegrationBtn').addEventListener('click', () => {
            this.testIntegrationConnections();
        });
        
        // 确认对话框
        document.getElementById('cancelConfirm').addEventListener('click', () => {
            this.hideConfirmDialog();
        });
        
        document.getElementById('confirmAction').addEventListener('click', () => {
            if (this.pendingAction) {
                this.pendingAction();
                this.hideConfirmDialog();
            }
        });
        
        // 点击模态框外部关闭
        document.getElementById('confirmModal').addEventListener('click', (e) => {
            if (e.target.id === 'confirmModal') {
                this.hideConfirmDialog();
            }
        });
    }
    
    switchSection(section) {
        console.log('Switching to section:', section);

        // 更新标签状态
        document.querySelectorAll('.config-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        const activeTab = document.getElementById(`tab-${section}`);
        if (activeTab) {
            activeTab.classList.add('active');
            console.log('Activated tab:', `tab-${section}`);
        }

        // 显示对应配置区域
        document.querySelectorAll('.config-section').forEach(sec => {
            sec.classList.add('hidden');
        });
        const targetSection = document.getElementById(`section-${section}`);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            console.log('Showing section:', `section-${section}`);
        } else {
            console.error('Section not found:', `section-${section}`);
        }

        this.currentSection = section;

        // 加载对应配置
        switch (section) {
            case 'context':
                this.loadContextIsolationConfig();
                break;
            case 'system':
                this.loadSystemConfig();
                break;
            case 'integration':
                this.loadIntegrationConfig();
                break;
            case 'advanced':
                this.loadAdvancedConfig();
                break;
        }
    }
    
    handleScopeChange(scope) {
        const groupSelector = document.getElementById('groupSelector');
        if (scope === 'group') {
            groupSelector.classList.remove('hidden');
        } else {
            groupSelector.classList.add('hidden');
            this.currentGroupId = null;
        }
        this.loadContextIsolationConfig();
    }
    
    getAuthToken() {
        return localStorage.getItem('auth_token') || 'sk-123456';
    }

    async loadUserGroups() {
        try {
            const response = await fetch('/admin/groups', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateGroupSelector(data.user_groups || []);
            }
        } catch (error) {
            console.error('加载用户组失败:', error);
        }
    }
    
    updateGroupSelector(groups) {
        const selector = document.getElementById('groupSelector');
        
        // 清除现有选项（保留默认选项）
        while (selector.children.length > 1) {
            selector.removeChild(selector.lastChild);
        }
        
        // 添加用户组选项
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = group.name;
            selector.appendChild(option);
        });
    }
    
    async loadContextIsolationConfig() {
        this.showLoading();
        try {
            const endpoint = this.currentGroupId 
                ? `/api/config/context-isolation/${this.currentGroupId}`
                : '/api/config/context-isolation';
                
            const response = await fetch(endpoint, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const config = await response.json();
                this.updateContextIsolationForm(config);
                this.originalConfigs.context = { ...config };
            } else {
                this.showError('加载上下文隔离配置失败');
            }
        } catch (error) {
            console.error('加载上下文隔离配置失败:', error);
            this.showError('加载上下文隔离配置失败');
        } finally {
            this.hideLoading();
        }
    }
    
    updateContextIsolationForm(config) {
        document.getElementById('enableIsolation').checked = config.enable_isolation || true;
        document.getElementById('maxContextLength').value = config.max_context_length || 4096;
        document.getElementById('contextOverlap').value = config.context_overlap || 200;
        document.getElementById('isolationStrategy').value = config.isolation_strategy || 'sliding_window';
        document.getElementById('memoryThreshold').value = config.memory_threshold || 0.8;
        document.getElementById('memoryThresholdValue').textContent = 
            Math.round((config.memory_threshold || 0.8) * 100) + '%';
    }
    
    async saveContextIsolationConfig() {
        try {
            const config = {
                enable_isolation: document.getElementById('enableIsolation').checked,
                max_context_length: parseInt(document.getElementById('maxContextLength').value),
                context_overlap: parseInt(document.getElementById('contextOverlap').value),
                isolation_strategy: document.getElementById('isolationStrategy').value,
                memory_threshold: parseFloat(document.getElementById('memoryThreshold').value)
            };
            
            const endpoint = this.currentGroupId 
                ? `/api/config/context-isolation/${this.currentGroupId}`
                : '/api/config/context-isolation';
                
            const response = await fetch(endpoint, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(config)
            });
            
            if (response.ok) {
                this.showNotification('上下文隔离配置保存成功', 'success');
                this.originalConfigs.context = { ...config };
            } else {
                this.showError('保存上下文隔离配置失败');
            }
        } catch (error) {
            console.error('保存上下文隔离配置失败:', error);
            this.showError('保存上下文隔离配置失败');
        }
    }
    
    async loadSystemConfig() {
        this.showLoading();
        try {
            const response = await fetch('/api/config');
            if (response.ok) {
                const config = await response.json();
                this.updateSystemConfigForm(config);
                this.originalConfigs.system = { ...config };
            } else {
                this.showError('加载系统配置失败');
            }
        } catch (error) {
            console.error('加载系统配置失败:', error);
            this.showError('加载系统配置失败');
        } finally {
            this.hideLoading();
        }
    }
    
    updateSystemConfigForm(config) {
        const container = document.getElementById('systemConfigContainer');
        container.innerHTML = '';
        
        // 显示高级系统参数
        const advancedParams = [
            'LOG_LEVEL', 'MAX_WORKERS', 'REQUEST_TIMEOUT', 'RATE_LIMIT_ENABLED',
            'CACHE_TTL', 'BACKUP_ENABLED', 'MONITORING_ENABLED'
        ];
        
        advancedParams.forEach(param => {
            if (config.hasOwnProperty(param)) {
                const div = document.createElement('div');
                div.className = 'mb-4';
                
                const value = config[param];
                const isBoolean = typeof value === 'boolean';
                
                div.innerHTML = `
                    <label class="block text-sm font-medium text-gray-700 mb-2">${param}</label>
                    ${isBoolean ? 
                        `<input type="checkbox" id="system_${param}" class="form-checkbox" ${value ? 'checked' : ''}>` :
                        `<input type="text" id="system_${param}" class="form-input" value="${value || ''}">`
                    }
                    <p class="text-xs text-gray-500 mt-1">系统参数: ${param}</p>
                `;
                
                container.appendChild(div);
            }
        });
    }

    async saveSystemConfig() {
        try {
            const config = { ...this.originalConfigs.system };

            // 收集系统配置表单数据
            const container = document.getElementById('systemConfigContainer');
            const inputs = container.querySelectorAll('input');

            inputs.forEach(input => {
                const param = input.id.replace('system_', '');
                if (input.type === 'checkbox') {
                    config[param] = input.checked;
                } else {
                    config[param] = input.value;
                }
            });

            const response = await fetch('/api/config', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            if (response.ok) {
                this.showNotification('系统配置保存成功', 'success');
                this.originalConfigs.system = { ...config };
            } else {
                this.showError('保存系统配置失败');
            }
        } catch (error) {
            console.error('保存系统配置失败:', error);
            this.showError('保存系统配置失败');
        }
    }

    async loadIntegrationConfig() {
        // 模拟集成配置数据
        const config = {
            redis_url: 'redis://localhost:6379',
            database_url: 'postgresql://user:pass@localhost/db',
            monitoring_url: 'http://prometheus:9090',
            api_timeout: 30,
            retry_count: 3,
            concurrency_limit: 100
        };

        this.updateIntegrationForm(config);
        this.originalConfigs.integration = { ...config };
    }

    updateIntegrationForm(config) {
        document.getElementById('redisUrl').value = config.redis_url || '';
        document.getElementById('databaseUrl').value = config.database_url || '';
        document.getElementById('monitoringUrl').value = config.monitoring_url || '';
        document.getElementById('apiTimeout').value = config.api_timeout || 30;
        document.getElementById('retryCount').value = config.retry_count || 3;
        document.getElementById('concurrencyLimit').value = config.concurrency_limit || 100;
    }

    async saveIntegrationConfig() {
        try {
            const config = {
                redis_url: document.getElementById('redisUrl').value,
                database_url: document.getElementById('databaseUrl').value,
                monitoring_url: document.getElementById('monitoringUrl').value,
                api_timeout: parseInt(document.getElementById('apiTimeout').value),
                retry_count: parseInt(document.getElementById('retryCount').value),
                concurrency_limit: parseInt(document.getElementById('concurrencyLimit').value)
            };

            // 这里应该调用实际的集成配置API
            // const response = await fetch('/api/config/integration', { ... });

            this.showNotification('集成配置保存成功', 'success');
            this.originalConfigs.integration = { ...config };
        } catch (error) {
            console.error('保存集成配置失败:', error);
            this.showError('保存集成配置失败');
        }
    }

    async loadAdvancedConfig() {
        // 模拟高级配置数据
        const config = {
            enable_debug_mode: false,
            enable_profiling: false,
            worker_processes: 4,
            enable_strict_mode: true,
            enable_audit_log: true,
            session_timeout: 3600
        };

        this.updateAdvancedForm(config);
        this.originalConfigs.advanced = { ...config };
    }

    updateAdvancedForm(config) {
        document.getElementById('enableDebugMode').checked = config.enable_debug_mode || false;
        document.getElementById('enableProfiling').checked = config.enable_profiling || false;
        document.getElementById('workerProcesses').value = config.worker_processes || 4;
        document.getElementById('enableStrictMode').checked = config.enable_strict_mode || true;
        document.getElementById('enableAuditLog').checked = config.enable_audit_log || true;
        document.getElementById('sessionTimeout').value = config.session_timeout || 3600;
    }

    async saveAdvancedConfig() {
        try {
            const config = {
                enable_debug_mode: document.getElementById('enableDebugMode').checked,
                enable_profiling: document.getElementById('enableProfiling').checked,
                worker_processes: parseInt(document.getElementById('workerProcesses').value),
                enable_strict_mode: document.getElementById('enableStrictMode').checked,
                enable_audit_log: document.getElementById('enableAuditLog').checked,
                session_timeout: parseInt(document.getElementById('sessionTimeout').value)
            };

            // 这里应该调用实际的高级配置API
            // const response = await fetch('/api/config/advanced', { ... });

            this.showNotification('高级配置保存成功', 'success');
            this.originalConfigs.advanced = { ...config };
        } catch (error) {
            console.error('保存高级配置失败:', error);
            this.showError('保存高级配置失败');
        }
    }

    resetContextConfig() {
        if (this.originalConfigs.context) {
            this.updateContextIsolationForm(this.originalConfigs.context);
            this.showNotification('上下文配置已重置', 'info');
        }
    }

    resetSystemConfig() {
        if (this.originalConfigs.system) {
            this.updateSystemConfigForm(this.originalConfigs.system);
            this.showNotification('系统配置已重置', 'info');
        }
    }

    resetIntegrationConfig() {
        if (this.originalConfigs.integration) {
            this.updateIntegrationForm(this.originalConfigs.integration);
            this.showNotification('集成配置已重置', 'info');
        }
    }

    resetAdvancedConfig() {
        if (this.originalConfigs.advanced) {
            this.updateAdvancedForm(this.originalConfigs.advanced);
            this.showNotification('高级配置已重置', 'info');
        }
    }

    async testIntegrationConnections() {
        this.showLoading();
        try {
            // 模拟连接测试
            await new Promise(resolve => setTimeout(resolve, 2000));

            const results = [
                { service: 'Redis', status: 'success', message: '连接成功' },
                { service: 'Database', status: 'success', message: '连接成功' },
                { service: 'Monitoring', status: 'warning', message: '连接超时，但可用' }
            ];

            let message = '连接测试结果:\n';
            results.forEach(result => {
                const icon = result.status === 'success' ? '✅' :
                           result.status === 'warning' ? '⚠️' : '❌';
                message += `${icon} ${result.service}: ${result.message}\n`;
            });

            this.showNotification(message, 'info');
        } catch (error) {
            this.showError('连接测试失败');
        } finally {
            this.hideLoading();
        }
    }

    async exportConfig() {
        try {
            const allConfigs = {
                context: this.originalConfigs.context,
                system: this.originalConfigs.system,
                integration: this.originalConfigs.integration,
                advanced: this.originalConfigs.advanced,
                export_time: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(allConfigs, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system_settings_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showNotification('配置导出成功', 'success');
        } catch (error) {
            console.error('导出配置失败:', error);
            this.showError('导出配置失败');
        }
    }

    async resetAllConfig() {
        try {
            const response = await fetch('/api/config/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                this.showNotification('所有配置已重置为默认值', 'success');
                // 重新加载当前配置
                this.switchSection(this.currentSection);
            } else {
                this.showError('重置配置失败');
            }
        } catch (error) {
            console.error('重置配置失败:', error);
            this.showError('重置配置失败');
        }
    }

    initializeConfigSearch() {
        const searchInput = document.getElementById('searchConfig');
        searchInput.addEventListener('input', (e) => {
            this.filterConfigItems(e.target.value);
        });
    }

    filterConfigItems(searchTerm) {
        const sections = document.querySelectorAll('.config-section');
        sections.forEach(section => {
            const items = section.querySelectorAll('label, h4');
            let hasVisibleItems = false;

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                const matches = text.includes(searchTerm.toLowerCase());
                const parent = item.closest('div');

                if (parent) {
                    parent.style.display = matches ? 'block' : 'none';
                    if (matches) hasVisibleItems = true;
                }
            });
        });
    }

    showConfirmDialog(title, message, action) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;
        this.pendingAction = action;
        document.getElementById('confirmModal').style.display = 'flex';
    }

    hideConfirmDialog() {
        document.getElementById('confirmModal').style.display = 'none';
        this.pendingAction = null;
    }

    showLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span style="white-space: pre-line;">${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showError(message) {
        this.showNotification(message, 'error');
    }
}

// 等待DOM加载完成后初始化设置管理器
document.addEventListener('DOMContentLoaded', function() {
    window.settingsManager = new SettingsManager();
    window.systemSettings = window.settingsManager; // 兼容性别名
});
