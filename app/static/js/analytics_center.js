/**
 * 分析报告中心 JavaScript
 * 提供综合数据分析、可视化图表、智能建议等功能
 */

class AnalyticsCenter {
    constructor() {
        this.charts = {};
        this.currentData = null;
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUserGroups();
        this.loadAnalyticsData();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        // 时间周期选择
        document.getElementById('periodSelector').addEventListener('change', () => {
            this.loadAnalyticsData();
        });
        
        // 用户组选择
        document.getElementById('groupSelector').addEventListener('change', () => {
            this.loadAnalyticsData();
        });
        
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshData();
        });
        
        // 导出按钮
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.showExportModal();
        });
        
        // 导出模态框事件
        document.getElementById('cancelExport').addEventListener('click', () => {
            this.hideExportModal();
        });
        
        document.getElementById('confirmExport').addEventListener('click', () => {
            this.exportReport();
        });
        
        // 点击模态框外部关闭
        document.getElementById('exportModal').addEventListener('click', (e) => {
            if (e.target.id === 'exportModal') {
                this.hideExportModal();
            }
        });
    }
    
    async loadUserGroups() {
        try {
            const response = await fetch('/web/api/groups');
            if (response.ok) {
                const data = await response.json();
                this.updateGroupSelector(data.user_groups || []);
            }
        } catch (error) {
            console.error('加载用户组失败:', error);
        }
    }
    
    updateGroupSelector(groups) {
        const selector = document.getElementById('groupSelector');
        
        // 清除现有选项（保留"全局分析"）
        while (selector.children.length > 1) {
            selector.removeChild(selector.lastChild);
        }
        
        // 添加用户组选项
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = group.name;
            selector.appendChild(option);
        });
    }
    
    async loadAnalyticsData() {
        this.showLoading();
        try {
            const period = document.getElementById('periodSelector').value;
            const groupId = document.getElementById('groupSelector').value;
            
            const params = new URLSearchParams({ period });
            if (groupId) {
                params.append('group_id', groupId);
            }
            
            const response = await fetch(`/web/api/analytics/comprehensive?${params}`);
            if (response.ok) {
                this.currentData = await response.json();
                this.updateDisplay();
            } else {
                this.showError('加载分析数据失败');
            }
        } catch (error) {
            console.error('加载分析数据失败:', error);
            this.showError('加载分析数据失败');
        } finally {
            this.hideLoading();
        }
    }
    
    updateDisplay() {
        if (!this.currentData) return;
        
        this.updateOverviewCards();
        this.updateCharts();
        this.updatePerformanceMetrics();
        this.updateTopModels();
        this.updateUserGroupAnalysis();
        this.updateRecommendations();
    }
    
    updateOverviewCards() {
        const overview = this.currentData.overview || {};
        
        document.getElementById('totalRequests').textContent = 
            (overview.total_requests || 0).toLocaleString();
        document.getElementById('successRate').textContent = 
            `${overview.success_rate || 0}%`;
        document.getElementById('totalCost').textContent = 
            `$${(this.currentData.cost_analysis?.total_cost || 0).toFixed(4)}`;
        document.getElementById('activeUsers').textContent = 
            overview.active_users || 0;
    }
    
    updateCharts() {
        this.updateUsageTrendChart();
        this.updateCostAnalysisChart();
    }
    
    updateUsageTrendChart() {
        const ctx = document.getElementById('usageTrendChart').getContext('2d');
        const trends = this.currentData.usage_trends || {};
        
        if (this.charts.usageTrend) {
            this.charts.usageTrend.destroy();
        }
        
        this.charts.usageTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: trends.timeline || [],
                datasets: [{
                    label: '请求数量',
                    data: trends.requests || [],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '请求数量'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    updateCostAnalysisChart() {
        const ctx = document.getElementById('costAnalysisChart').getContext('2d');
        const costData = this.currentData.cost_analysis || {};
        const modelBreakdown = costData.model_breakdown || {};
        
        if (this.charts.costAnalysis) {
            this.charts.costAnalysis.destroy();
        }
        
        const labels = Object.keys(modelBreakdown);
        const costs = labels.map(model => modelBreakdown[model].cost || 0);
        
        this.charts.costAnalysis = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: costs,
                    backgroundColor: [
                        '#EF4444',
                        '#F59E0B',
                        '#10B981',
                        '#3B82F6',
                        '#8B5CF6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    updatePerformanceMetrics() {
        const metrics = this.currentData.performance_metrics || {};
        const container = document.getElementById('performanceMetrics');
        
        container.innerHTML = `
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-2xl font-bold text-blue-600">${(metrics.avg_response_time || 0).toFixed(2)}s</div>
                    <div class="text-sm text-gray-600">平均响应时间</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-2xl font-bold text-green-600">${(metrics.cache_hit_rate || 0).toFixed(1)}%</div>
                    <div class="text-sm text-gray-600">缓存命中率</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-2xl font-bold text-yellow-600">${(metrics.error_rate || 0).toFixed(1)}%</div>
                    <div class="text-sm text-gray-600">错误率</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-2xl font-bold text-purple-600">${metrics.throughput || 0}</div>
                    <div class="text-sm text-gray-600">吞吐量/分钟</div>
                </div>
            </div>
        `;
    }
    
    updateTopModels() {
        const overview = this.currentData.overview || {};
        const topModels = overview.top_models || [];
        const container = document.getElementById('topModels');
        
        container.innerHTML = '';
        
        topModels.forEach((model, index) => {
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between p-3 bg-gray-50 rounded';
            div.innerHTML = `
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-orange-600 font-bold">${index + 1}</span>
                    </div>
                    <span class="font-medium">${model.name}</span>
                </div>
                <span class="text-gray-600">${model.usage} 次调用</span>
            `;
            container.appendChild(div);
        });
    }
    
    updateUserGroupAnalysis() {
        const analysis = this.currentData.user_group_analysis || {};
        const container = document.getElementById('userGroupAnalysis');
        
        if (analysis.group_id) {
            // 单个用户组分析
            container.innerHTML = `
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${analysis.usage_rank || '-'}</div>
                        <div class="text-sm text-gray-600">使用排名</div>
                    </div>
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${(analysis.quota_utilization || 0).toFixed(1)}%</div>
                        <div class="text-sm text-gray-600">配额利用率</div>
                    </div>
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${analysis.active_members || 0}</div>
                        <div class="text-sm text-gray-600">活跃成员</div>
                    </div>
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${analysis.period || '-'}</div>
                        <div class="text-sm text-gray-600">分析周期</div>
                    </div>
                </div>
            `;
        } else {
            // 全局用户组分析
            const topGroups = analysis.top_groups || [];
            let groupsHtml = '';
            
            topGroups.forEach((group, index) => {
                groupsHtml += `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                                <span class="text-indigo-600 font-bold">${index + 1}</span>
                            </div>
                            <span class="font-medium">${group.name}</span>
                        </div>
                        <span class="text-gray-600">${group.usage} 次调用</span>
                    </div>
                `;
            });
            
            container.innerHTML = `
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${analysis.total_groups || 0}</div>
                        <div class="text-sm text-gray-600">总用户组</div>
                    </div>
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${analysis.active_groups || 0}</div>
                        <div class="text-sm text-gray-600">活跃用户组</div>
                    </div>
                    <div class="text-center p-4 bg-indigo-50 rounded">
                        <div class="text-xl font-bold text-indigo-600">${analysis.period || '-'}</div>
                        <div class="text-sm text-gray-600">分析周期</div>
                    </div>
                </div>
                <div class="space-y-3">
                    <h4 class="font-medium text-gray-900">热门用户组</h4>
                    ${groupsHtml}
                </div>
            `;
        }
    }

    updateRecommendations() {
        const recommendations = this.currentData.recommendations || [];
        const container = document.getElementById('recommendations');

        container.innerHTML = '';

        if (recommendations.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-check-circle text-4xl mb-4 text-green-500"></i>
                    <p class="text-lg">系统运行良好，暂无优化建议</p>
                </div>
            `;
            return;
        }

        recommendations.forEach(rec => {
            const div = document.createElement('div');
            div.className = `p-4 rounded-lg border-l-4 ${
                rec.priority === 'high' ? 'border-red-400 bg-red-50' :
                rec.priority === 'medium' ? 'border-yellow-400 bg-yellow-50' :
                'border-blue-400 bg-blue-50'
            }`;

            div.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas ${
                            rec.priority === 'high' ? 'fa-exclamation-triangle text-red-400' :
                            rec.priority === 'medium' ? 'fa-exclamation-circle text-yellow-400' :
                            'fa-info-circle text-blue-400'
                        }"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <h4 class="text-sm font-medium text-gray-900">${rec.title}</h4>
                        <p class="text-sm text-gray-700 mt-1">${rec.description}</p>
                        ${rec.action ? `
                            <button class="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium">
                                ${rec.action}
                            </button>
                        ` : ''}
                    </div>
                    <div class="flex-shrink-0">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                            rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                        }">
                            ${rec.priority === 'high' ? '高优先级' :
                              rec.priority === 'medium' ? '中优先级' : '低优先级'}
                        </span>
                    </div>
                </div>
            `;

            container.appendChild(div);
        });
    }

    async refreshData() {
        this.showNotification('正在刷新数据...', 'info');
        await this.loadAnalyticsData();
        this.showNotification('数据刷新完成', 'success');
    }

    showExportModal() {
        const modal = document.getElementById('exportModal');
        modal.style.display = 'flex';
    }

    hideExportModal() {
        const modal = document.getElementById('exportModal');
        modal.style.display = 'none';
    }

    async exportReport() {
        try {
            const format = document.getElementById('exportFormat').value;
            const period = document.getElementById('periodSelector').value;
            const groupId = document.getElementById('groupSelector').value;

            const params = new URLSearchParams({ format, period });
            if (groupId) {
                params.append('group_id', groupId);
            }

            const response = await fetch(`/web/api/analytics/export?${params}`);

            if (response.ok) {
                if (format === 'csv') {
                    // 下载CSV文件
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `analytics_report_${period}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    // 下载JSON文件
                    const data = await response.json();
                    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `analytics_report_${period}.json`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                }

                this.hideExportModal();
                this.showNotification('报告导出成功', 'success');
            } else {
                this.showError('导出失败');
            }
        } catch (error) {
            console.error('导出报告失败:', error);
            this.showError('导出报告失败');
        }
    }

    startAutoRefresh() {
        // 每5分钟自动刷新一次数据
        this.refreshInterval = setInterval(() => {
            this.loadAnalyticsData();
        }, 5 * 60 * 1000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    showLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showError(message) {
        this.showNotification(message, 'error');
    }
}

// 等待DOM加载完成后初始化分析中心
document.addEventListener('DOMContentLoaded', function() {
    window.analyticsCenter = new AnalyticsCenter();
});
