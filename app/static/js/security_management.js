/**
 * 安全管理中心前端逻辑
 */

class SecurityManager {
    constructor() {
        this.currentGroupId = '';
        this.currentTab = 'whitelist'; // whitelist 或 blacklist
        this.ipLists = {
            whitelist: [],
            blacklist: []
        };
        this.securityConfig = {};
        this.events = [];
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUserGroups();
        this.loadInitialData();
    }

    async loadInitialData() {
        this.showLoading();
        try {
            // 加载默认的安全统计数据
            this.updateStatistics({
                security_level: 'low',
                recent_events_24h: 0,
                ip_whitelist_size: 0,
                ip_blacklist_size: 0
            });

            // 加载默认的IP管理数据
            this.ipManagement = {
                whitelist: [],
                blacklist: []
            };

            // 加载默认的安全配置
            this.securityConfig = {
                enable_rate_limiting: true,
                max_requests_per_minute: 60,
                enable_ip_filtering: false,
                enable_geo_blocking: false,
                blocked_countries: []
            };

            this.updateConfigForm();
            this.renderIPList();
            this.events = [];
            this.renderEvents();
            this.renderKeyRotationPlan({ keys: [] });

        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showError('加载初始数据失败');
        } finally {
            this.hideLoading();
        }
    }

    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshAll();
        });
        
        // 用户组选择器
        document.getElementById('groupSelector').addEventListener('change', (e) => {
            this.currentGroupId = e.target.value;
            if (this.currentGroupId) {
                this.loadGroupData();
            }
        });
        
        // IP管理标签页
        document.getElementById('whitelistTab').addEventListener('click', () => {
            this.switchTab('whitelist');
        });
        
        document.getElementById('blacklistTab').addEventListener('click', () => {
            this.switchTab('blacklist');
        });
        
        // 添加IP按钮
        document.getElementById('addIpBtn').addEventListener('click', () => {
            this.addIP();
        });
        
        // IP输入框回车事件
        document.getElementById('ipInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addIP();
            }
        });
        
        // 安全配置表单
        document.getElementById('securityConfigForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSecurityConfig();
        });
        
        // 严重级别过滤
        document.getElementById('severityFilter').addEventListener('change', () => {
            this.loadSecurityEvents();
        });
        
        // 刷新事件按钮
        document.getElementById('refreshEventsBtn').addEventListener('click', () => {
            this.loadSecurityEvents();
        });
    }
    
    async loadUserGroups() {
        try {
            const response = await fetch('/admin/groups?limit=100&offset=0', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                const groups = data.user_groups || [];
                this.populateGroupSelector(groups);
            } else if (response.status === 401) {
                this.showError('认证失败，请重新登录');
                window.location.href = '/';
            } else {
                this.showError('加载用户组失败');
            }
        } catch (error) {
            console.error('加载用户组失败:', error);
            this.showError('加载用户组失败');
        }
    }

    getAuthToken() {
        // 从cookie或localStorage获取认证token
        return localStorage.getItem('auth_token') || 'sk-123456';
    }
    
    populateGroupSelector(groups) {
        const selector = document.getElementById('groupSelector');
        selector.innerHTML = '<option value="">选择用户组</option>';

        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.group_id || group.id;
            option.textContent = group.group_name || group.name || group.group_id || group.id;
            selector.appendChild(option);
        });
    }
    
    async loadGroupData() {
        if (!this.currentGroupId) return;
        
        this.showLoading();
        try {
            await Promise.all([
                this.loadSecurityStatistics(),
                this.loadSecurityConfig(),
                this.loadIPLists(),
                this.loadSecurityEvents(),
                this.loadKeyRotationPlan()
            ]);
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('加载数据失败');
        } finally {
            this.hideLoading();
        }
    }
    
    async loadSecurityStatistics() {
        try {
            const response = await fetch(`/security/groups/${this.currentGroupId}/statistics`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const stats = await response.json();
                this.updateStatistics(stats);
            }
        } catch (error) {
            console.error('加载安全统计失败:', error);
        }
    }
    
    updateStatistics(stats) {
        document.getElementById('securityLevel').textContent = stats.security_level || '-';
        document.getElementById('recentEvents').textContent = stats.recent_events_24h || 0;
        document.getElementById('whitelistSize').textContent = stats.ip_whitelist_size || 0;
        document.getElementById('blacklistSize').textContent = stats.ip_blacklist_size || 0;
    }
    
    async loadSecurityConfig() {
        try {
            const response = await fetch(`/security/groups/${this.currentGroupId}/config`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.securityConfig = data.config;
                this.updateConfigForm();
            }
        } catch (error) {
            console.error('加载安全配置失败:', error);
            // 使用默认配置
            this.securityConfig = {
                enable_rate_limiting: true,
                max_requests_per_minute: 60,
                enable_ip_whitelist: false,
                enable_anomaly_detection: true
            };
            this.updateConfigForm();
        }
    }
    
    updateConfigForm() {
        const config = this.securityConfig;
        
        document.getElementById('enableIpWhitelist').checked = config.enable_ip_whitelist || false;
        document.getElementById('enableRateLimiting').checked = config.enable_rate_limiting || false;
        document.getElementById('enableAnomalyDetection').checked = config.enable_anomaly_detection || false;
        document.getElementById('enableAuditLogging').checked = config.enable_audit_logging || false;
        document.getElementById('autoBlockSuspiciousIps').checked = config.auto_block_suspicious_ips || false;
        document.getElementById('securityLevelSelect').value = config.security_level || 'medium';
        document.getElementById('keyRotationInterval').value = config.key_rotation_interval_days || 30;
    }
    
    async saveSecurityConfig() {
        if (!this.currentGroupId) {
            this.showError('请先选择用户组');
            return;
        }
        
        const config = {
            enable_ip_whitelist: document.getElementById('enableIpWhitelist').checked,
            enable_rate_limiting: document.getElementById('enableRateLimiting').checked,
            enable_anomaly_detection: document.getElementById('enableAnomalyDetection').checked,
            enable_audit_logging: document.getElementById('enableAuditLogging').checked,
            auto_block_suspicious_ips: document.getElementById('autoBlockSuspiciousIps').checked,
            security_level: document.getElementById('securityLevelSelect').value,
            key_rotation_interval_days: parseInt(document.getElementById('keyRotationInterval').value)
        };
        
        try {
            const response = await fetch(`/security/groups/${this.currentGroupId}/config`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(config)
            });
            
            if (response.ok) {
                this.showSuccess('安全配置已保存');
                this.loadSecurityStatistics(); // 刷新统计数据
            } else {
                this.showError('保存配置失败');
            }
        } catch (error) {
            console.error('保存安全配置失败:', error);
            this.showError('保存配置失败');
        }
    }
    
    switchTab(tab) {
        this.currentTab = tab;
        
        // 更新标签页样式
        document.getElementById('whitelistTab').classList.toggle('active', tab === 'whitelist');
        document.getElementById('blacklistTab').classList.toggle('active', tab === 'blacklist');
        
        // 更新IP列表显示
        this.renderIPList();
    }
    
    async loadIPLists() {
        // 注意：实际API可能需要分别获取白名单和黑名单
        // 这里假设从统计数据中获取，实际实现时可能需要调整
        try {
            const response = await fetch(`/security/groups/${this.currentGroupId}/statistics`, {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const stats = await response.json();
                // 这里需要根据实际API调整数据获取方式
                this.ipLists.whitelist = stats.whitelist_ips || [];
                this.ipLists.blacklist = stats.blacklist_ips || [];
                this.renderIPList();
            }
        } catch (error) {
            console.error('加载IP列表失败:', error);
            // 使用模拟数据
            this.ipLists = {
                whitelist: ['*************', '*********'],
                blacklist: ['*************']
            };
            this.renderIPList();
        }
    }
    
    renderIPList() {
        const container = document.getElementById('ipListContainer');
        const currentList = this.ipLists[this.currentTab];
        
        if (!currentList || currentList.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-inbox text-4xl mb-4"></i>
                    <p>暂无${this.currentTab === 'whitelist' ? '白名单' : '黑名单'}IP</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = currentList.map(ip => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex-1">
                    <span class="font-mono text-sm">${ip.address || ip}</span>
                    ${ip.reason ? `<span class="text-xs text-gray-500 ml-2">${ip.reason}</span>` : ''}
                </div>
                <button onclick="securityManager.removeIP('${ip.address || ip}')" 
                        class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }
    
    async addIP() {
        const ipInput = document.getElementById('ipInput');
        const reasonInput = document.getElementById('reasonInput');
        const ip = ipInput.value.trim();
        const reason = reasonInput.value.trim();
        
        if (!ip) {
            this.showError('请输入IP地址');
            return;
        }
        
        if (!this.currentGroupId) {
            this.showError('请先选择用户组');
            return;
        }
        
        try {
            const endpoint = this.currentTab === 'whitelist' ? 'whitelist' : 'blacklist';
            const response = await fetch(`/security/groups/${this.currentGroupId}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ip_address: ip,
                    reason: reason
                })
            });
            
            if (response.ok) {
                this.showSuccess(`IP已添加到${this.currentTab === 'whitelist' ? '白名单' : '黑名单'}`);
                ipInput.value = '';
                reasonInput.value = '';
                this.loadIPLists();
                this.loadSecurityStatistics();
            } else {
                this.showError('添加IP失败');
            }
        } catch (error) {
            console.error('添加IP失败:', error);
            this.showError('添加IP失败');
        }
    }
    
    async removeIP(ip) {
        if (!confirm(`确定要从${this.currentTab === 'whitelist' ? '白名单' : '黑名单'}中移除 ${ip} 吗？`)) {
            return;
        }
        
        try {
            const endpoint = this.currentTab === 'whitelist' ? 'whitelist' : 'blacklist';
            const response = await fetch(`/security/groups/${this.currentGroupId}/${endpoint}/${encodeURIComponent(ip)}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                this.showSuccess('IP已移除');
                this.loadIPLists();
                this.loadSecurityStatistics();
            } else {
                this.showError('移除IP失败');
            }
        } catch (error) {
            console.error('移除IP失败:', error);
            this.showError('移除IP失败');
        }
    }
    
    async loadSecurityEvents() {
        if (!this.currentGroupId) return;
        
        try {
            const severity = document.getElementById('severityFilter').value;
            let url = `/security/groups/${this.currentGroupId}/events?limit=50`;
            if (severity) {
                url += `&severity=${severity}`;
            }
            
            const response = await fetch(url);
            if (response.ok) {
                const data = await response.json();
                this.events = data.events || [];
                this.renderEvents();
            }
        } catch (error) {
            console.error('加载安全事件失败:', error);
        }
    }
    
    renderEvents() {
        const container = document.getElementById('eventsContainer');
        
        if (!this.events || this.events.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-shield-alt text-4xl mb-4"></i>
                    <p>暂无安全事件</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = `
            <div class="space-y-4">
                ${this.events.map(event => `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    ${this.getSeverityClass(event.severity)}">
                                    ${event.severity}
                                </span>
                                <span class="text-sm font-medium text-gray-900">${event.event_type}</span>
                            </div>
                            <span class="text-sm text-gray-500">${this.formatDate(event.timestamp)}</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p><strong>来源IP:</strong> ${event.source_ip || '-'}</p>
                            <p><strong>详情:</strong> ${event.details || '-'}</p>
                            ${event.user_agent ? `<p><strong>User Agent:</strong> ${event.user_agent}</p>` : ''}
                        </div>
                        ${event.resolved ? 
                            '<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-800 mt-2">已解决</span>' : 
                            '<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800 mt-2">待处理</span>'
                        }
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    getSeverityClass(severity) {
        const classes = {
            'low': 'bg-green-100 text-green-800',
            'medium': 'bg-yellow-100 text-yellow-800',
            'high': 'bg-orange-100 text-orange-800',
            'critical': 'bg-red-100 text-red-800'
        };
        return classes[severity] || 'bg-gray-100 text-gray-800';
    }
    
    async loadKeyRotationPlan() {
        if (!this.currentGroupId) return;
        
        try {
            const response = await fetch(`/security/groups/${this.currentGroupId}/key-rotation-plan`);
            if (response.ok) {
                const plan = await response.json();
                this.renderKeyRotationPlan(plan);
            }
        } catch (error) {
            console.error('加载密钥轮换计划失败:', error);
        }
    }
    
    renderKeyRotationPlan(plan) {
        const container = document.getElementById('keyRotationPlan');
        
        if (!plan || !plan.keys || plan.keys.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-key text-4xl mb-4"></i>
                    <p>暂无密钥轮换计划</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = `
            <div class="space-y-4">
                ${plan.keys.map(key => `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium text-gray-900">密钥: ***${key.key_suffix}</p>
                                <p class="text-sm text-gray-600">下次轮换: ${this.formatDate(key.next_rotation)}</p>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                ${key.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                ${key.status}
                            </span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    refreshAll() {
        if (this.currentGroupId) {
            this.loadGroupData();
        }
    }
    
    formatDate(dateString) {
        return new Date(dateString).toLocaleString('zh-CN');
    }
    
    showLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// 等待DOM加载完成后初始化安全管理器
document.addEventListener('DOMContentLoaded', function() {
    window.securityManager = new SecurityManager();
    console.log('SecurityManager initialized on DOMContentLoaded');
});
