/**
 * 监控大屏前端逻辑
 */

class MonitoringDashboard {
    constructor() {
        this.charts = {};
        this.refreshInterval = null;
        this.currentPeriod = '1h';
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initCharts();
        this.startAutoRefresh();
        this.updateTime();
        this.loadDashboardData();
    }
    
    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadDashboardData();
        });
        
        // 全屏按钮
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });
        
        // 时间过滤按钮
        document.querySelectorAll('.time-filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.time-filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentPeriod = e.target.dataset.period;
                this.loadChartData();
            });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            } else if (e.key === 'F5') {
                e.preventDefault();
                this.loadDashboardData();
            }
        });
    }
    
    initCharts() {
        // 请求量趋势图
        const requestCtx = document.getElementById('requestChart').getContext('2d');
        this.charts.request = new Chart(requestCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '请求数',
                    data: [],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#E5E7EB' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#9CA3AF' },
                        grid: { color: '#374151' }
                    },
                    y: {
                        ticks: { color: '#9CA3AF' },
                        grid: { color: '#374151' }
                    }
                }
            }
        });
        
        // 成功率图表
        const successCtx = document.getElementById('successChart').getContext('2d');
        this.charts.success = new Chart(successCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '成功率',
                    data: [],
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#E5E7EB' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#9CA3AF' },
                        grid: { color: '#374151' }
                    },
                    y: {
                        min: 0,
                        max: 100,
                        ticks: { 
                            color: '#9CA3AF',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: { color: '#374151' }
                    }
                }
            }
        });
        
        // 模型使用分布图
        const modelCtx = document.getElementById('modelChart').getContext('2d');
        this.charts.model = new Chart(modelCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#3B82F6',
                        '#10B981',
                        '#F59E0B',
                        '#EF4444',
                        '#8B5CF6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { 
                            color: '#E5E7EB',
                            padding: 20
                        }
                    }
                }
            }
        });
        
        // 响应时间图表
        const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
        this.charts.responseTime = new Chart(responseCtx, {
            type: 'bar',
            data: {
                labels: ['平均', 'P95', 'P99'],
                datasets: [{
                    data: [],
                    backgroundColor: ['#3B82F6', '#F59E0B', '#EF4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    x: {
                        ticks: { color: '#9CA3AF' },
                        grid: { display: false }
                    },
                    y: {
                        ticks: { 
                            color: '#9CA3AF',
                            callback: function(value) {
                                return value + 'ms';
                            }
                        },
                        grid: { color: '#374151' }
                    }
                }
            }
        });
    }
    
    async loadDashboardData() {
        try {
            await Promise.all([
                this.loadSystemStatus(),
                this.loadChartData(),
                this.loadGroupStatus(),
                this.loadAlerts(),
                this.loadSystemResources()
            ]);
        } catch (error) {
            console.error('加载监控数据失败:', error);
        }
    }
    
    async loadSystemStatus() {
        try {
            const response = await fetch('/monitoring/system/overview', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateSystemStatus(data);
            }
        } catch (error) {
            console.error('加载系统状态失败:', error);
        }
    }

    getAuthToken() {
        // 从cookie或localStorage获取认证token
        return localStorage.getItem('auth_token') || 'sk-123456';
    }
    
    updateSystemStatus(data) {
        document.getElementById('systemStatus').textContent = data.system_status === 'healthy' ? '正常' : '异常';
        document.getElementById('activeGroups').textContent = data.active_groups || 0;
        document.getElementById('currentQPS').textContent = data.current_qps || 0;
        document.getElementById('activeAlerts').textContent = data.total_active_alerts || 0;
    }
    
    async loadChartData() {
        try {
            const headers = {
                'Authorization': `Bearer ${this.getAuthToken()}`
            };

            const [requestData, successData, modelData, responseData] = await Promise.all([
                fetch(`/monitoring/metrics/requests?period=${this.currentPeriod}`, { headers }).then(r => r.ok ? r.json() : null),
                fetch(`/monitoring/metrics/success-rate?period=${this.currentPeriod}`, { headers }).then(r => r.ok ? r.json() : null),
                fetch(`/monitoring/metrics/models?period=${this.currentPeriod}`, { headers }).then(r => r.ok ? r.json() : null),
                fetch(`/monitoring/metrics/response-time?period=${this.currentPeriod}`, { headers }).then(r => r.ok ? r.json() : null)
            ]);

            if (requestData) this.updateRequestChart(requestData);
            if (successData) this.updateSuccessChart(successData);
            if (modelData) this.updateModelChart(modelData);
            if (responseData) this.updateResponseTimeChart(responseData);
        } catch (error) {
            console.error('加载图表数据失败:', error);
        }
    }
    
    updateRequestChart(data) {
        if (data && data.data_points) {
            const labels = data.data_points.map(point => 
                new Date(point.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            );
            const values = data.data_points.map(point => point.requests);
            
            this.charts.request.data.labels = labels;
            this.charts.request.data.datasets[0].data = values;
            this.charts.request.update('none');
        }
    }
    
    updateSuccessChart(data) {
        if (data && data.data_points) {
            const labels = data.data_points.map(point => 
                new Date(point.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            );
            const values = data.data_points.map(point => point.success_rate);
            
            this.charts.success.data.labels = labels;
            this.charts.success.data.datasets[0].data = values;
            this.charts.success.update('none');
            
            // 更新整体成功率
            const avgSuccessRate = values.length > 0 ? 
                (values.reduce((a, b) => a + b, 0) / values.length).toFixed(1) : 0;
            document.getElementById('overallSuccessRate').textContent = avgSuccessRate + '%';
        }
    }
    
    updateModelChart(data) {
        if (data && data.model_usage) {
            const labels = Object.keys(data.model_usage);
            const values = Object.values(data.model_usage);
            
            this.charts.model.data.labels = labels;
            this.charts.model.data.datasets[0].data = values;
            this.charts.model.update('none');
        }
    }
    
    updateResponseTimeChart(data) {
        if (data) {
            const values = [
                data.avg_response_time || 0,
                data.p95_response_time || 0,
                data.p99_response_time || 0
            ];
            
            this.charts.responseTime.data.datasets[0].data = values;
            this.charts.responseTime.update('none');
            
            // 更新响应时间显示
            document.getElementById('avgResponseTime').textContent = (data.avg_response_time || 0) + 'ms';
            document.getElementById('p95ResponseTime').textContent = (data.p95_response_time || 0) + 'ms';
            document.getElementById('p99ResponseTime').textContent = (data.p99_response_time || 0) + 'ms';
        }
    }
    
    async loadGroupStatus() {
        try {
            const response = await fetch('/admin/groups/statistics/overview', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateGroupStatus(data);
            }
        } catch (error) {
            console.error('加载用户组状态失败:', error);
        }
    }
    
    updateGroupStatus(data) {
        const container = document.getElementById('groupStatusList');
        
        if (!data.groups || data.groups.length === 0) {
            container.innerHTML = '<p class="text-gray-400 text-sm">暂无用户组数据</p>';
            return;
        }
        
        container.innerHTML = data.groups.map(group => `
            <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full ${this.getStatusColor(group.status)}"></div>
                    <span class="text-white text-sm font-medium">${group.group_name}</span>
                </div>
                <div class="text-right">
                    <div class="text-white text-sm">${group.current_qps || 0} QPS</div>
                    <div class="text-gray-400 text-xs">${group.quota_usage || 0}% 配额</div>
                </div>
            </div>
        `).join('');
    }
    
    getStatusColor(status) {
        const colors = {
            'active': 'bg-green-500',
            'warning': 'bg-yellow-500',
            'error': 'bg-red-500',
            'inactive': 'bg-gray-500'
        };
        return colors[status] || 'bg-gray-500';
    }
    
    async loadAlerts() {
        try {
            const response = await fetch('/monitoring/alerts/active', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateAlerts(data);
            }
        } catch (error) {
            console.error('加载告警数据失败:', error);
            // 使用模拟数据
            this.updateAlerts({
                alerts: [
                    {
                        id: 1,
                        title: '系统运行正常',
                        message: '所有服务运行正常',
                        severity: 'info',
                        timestamp: new Date().toISOString()
                    }
                ]
            });
        }
    }
    
    updateAlerts(data) {
        const container = document.getElementById('alertsList');
        const countElement = document.getElementById('alertCount');
        
        const alerts = data.alerts || [];
        countElement.textContent = alerts.length;
        
        if (alerts.length === 0) {
            container.innerHTML = '<p class="text-gray-400 text-sm">暂无活跃告警</p>';
            return;
        }
        
        container.innerHTML = alerts.slice(0, 10).map(alert => `
            <div class="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-${this.getSeverityColor(alert.severity)}-500"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-white text-sm font-medium">${alert.message}</p>
                    <p class="text-gray-400 text-xs">${alert.group_id} • ${this.formatTime(alert.triggered_at)}</p>
                </div>
            </div>
        `).join('');
    }
    
    getSeverityColor(severity) {
        const colors = {
            'low': 'blue',
            'medium': 'yellow',
            'high': 'orange',
            'critical': 'red'
        };
        return colors[severity] || 'gray';
    }
    
    async loadSystemResources() {
        try {
            const response = await fetch('/monitoring/system/resources', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateSystemResources(data);
            }
        } catch (error) {
            console.error('加载系统资源失败:', error);
            // 使用模拟数据
            this.updateSystemResources({
                cpu_usage: Math.random() * 100,
                memory_usage: Math.random() * 100,
                cache_hit_rate: 85 + Math.random() * 10,
                db_connections: Math.random() * 100
            });
        }
    }
    
    updateSystemResources(data) {
        // CPU使用率
        const cpuUsage = Math.round(data.cpu_usage || 0);
        document.getElementById('cpuUsage').textContent = cpuUsage + '%';
        document.getElementById('cpuBar').style.width = cpuUsage + '%';
        
        // 内存使用率
        const memoryUsage = Math.round(data.memory_usage || 0);
        document.getElementById('memoryUsage').textContent = memoryUsage + '%';
        document.getElementById('memoryBar').style.width = memoryUsage + '%';
        
        // 缓存命中率
        const cacheHitRate = Math.round(data.cache_hit_rate || 0);
        document.getElementById('cacheHitRate').textContent = cacheHitRate + '%';
        document.getElementById('cacheBar').style.width = cacheHitRate + '%';
        
        // 数据库连接
        const dbConnections = Math.round(data.db_connections || 0);
        document.getElementById('dbConnections').textContent = dbConnections + '%';
        document.getElementById('dbBar').style.width = dbConnections + '%';
    }
    
    startAutoRefresh() {
        // 每30秒自动刷新
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 30000);
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('currentTime').textContent = timeString;
        
        // 每秒更新时间
        setTimeout(() => this.updateTime(), 1000);
    }
    
    toggleFullscreen() {
        const dashboard = document.body;
        
        if (!document.fullscreenElement) {
            dashboard.requestFullscreen().then(() => {
                dashboard.classList.add('fullscreen');
                document.getElementById('fullscreenBtn').innerHTML = '<i class="fas fa-compress mr-2"></i>退出全屏';
            });
        } else {
            document.exitFullscreen().then(() => {
                dashboard.classList.remove('fullscreen');
                document.getElementById('fullscreenBtn').innerHTML = '<i class="fas fa-expand mr-2"></i>全屏';
            });
        }
    }
    
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 24小时内
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }
    


    destroy() {
        this.stopAutoRefresh();
        Object.values(this.charts).forEach(chart => chart.destroy());
    }
}

// 初始化监控大屏
const monitoringDashboard = new MonitoringDashboard();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    monitoringDashboard.destroy();
});
