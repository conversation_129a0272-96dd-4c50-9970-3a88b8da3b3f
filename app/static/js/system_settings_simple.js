/**
 * System Settings Center JavaScript - Simple Version
 */

class SettingsManager {
    constructor() {
        this.currentSection = 'context';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadContextIsolationConfig();
    }
    
    bindEvents() {
        // Tab switching
        document.querySelectorAll('.config-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const section = tab.dataset.section;
                console.log('Tab clicked:', section);
                this.switchSection(section);
            });
        });
        
        // Save buttons
        const saveContextBtn = document.getElementById('saveContextBtn');
        if (saveContextBtn) {
            saveContextBtn.addEventListener('click', () => {
                this.saveContextIsolationConfig();
            });
        }
        
        const saveSystemBtn = document.getElementById('saveSystemBtn');
        if (saveSystemBtn) {
            saveSystemBtn.addEventListener('click', () => {
                this.saveSystemConfig();
            });
        }
    }
    
    switchSection(section) {
        console.log('Switching to section:', section);
        
        // Update tab states
        document.querySelectorAll('.config-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const activeTab = document.getElementById(`tab-${section}`);
        if (activeTab) {
            activeTab.classList.add('active');
            console.log('Activated tab:', `tab-${section}`);
        }
        
        // Show corresponding config area
        document.querySelectorAll('.config-section').forEach(sec => {
            sec.classList.add('hidden');
        });
        
        const targetSection = document.getElementById(`section-${section}`);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            console.log('Showing section:', `section-${section}`);
        } else {
            console.error('Section not found:', `section-${section}`);
        }
        
        this.currentSection = section;
        
        // Load corresponding config
        switch (section) {
            case 'context':
                this.loadContextIsolationConfig();
                break;
            case 'system':
                this.loadSystemConfig();
                break;
            case 'integration':
                this.loadIntegrationConfig();
                break;
            case 'advanced':
                this.loadAdvancedConfig();
                break;
        }
    }
    
    async loadContextIsolationConfig() {
        console.log('Loading context isolation config');
        try {
            const response = await fetch('/api/config/context-isolation');
            if (response.ok) {
                const config = await response.json();
                this.updateContextIsolationForm(config);
            } else {
                console.error('Failed to load context config');
            }
        } catch (error) {
            console.error('Error loading context config:', error);
        }
    }
    
    updateContextIsolationForm(config) {
        const enableIsolation = document.getElementById('enableIsolation');
        if (enableIsolation) {
            enableIsolation.checked = config.enable_isolation || true;
        }
        
        const maxContextLength = document.getElementById('maxContextLength');
        if (maxContextLength) {
            maxContextLength.value = config.max_context_length || 4096;
        }
        
        const contextOverlap = document.getElementById('contextOverlap');
        if (contextOverlap) {
            contextOverlap.value = config.context_overlap || 200;
        }
    }
    
    async saveContextIsolationConfig() {
        console.log('Saving context isolation config');
        try {
            const config = {
                enable_isolation: document.getElementById('enableIsolation')?.checked || true,
                max_context_length: parseInt(document.getElementById('maxContextLength')?.value || 4096),
                context_overlap: parseInt(document.getElementById('contextOverlap')?.value || 200)
            };
            
            const response = await fetch('/api/config/context-isolation', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
            
            if (response.ok) {
                this.showNotification('Context config saved successfully', 'success');
            } else {
                this.showNotification('Failed to save context config', 'error');
            }
        } catch (error) {
            console.error('Error saving context config:', error);
            this.showNotification('Error saving context config', 'error');
        }
    }
    
    async loadSystemConfig() {
        console.log('Loading system config');
        try {
            const response = await fetch('/api/config');
            if (response.ok) {
                const config = await response.json();
                this.updateSystemConfigForm(config);
            } else {
                console.error('Failed to load system config');
            }
        } catch (error) {
            console.error('Error loading system config:', error);
        }
    }
    
    updateSystemConfigForm(config) {
        const container = document.getElementById('systemConfigContainer');
        if (!container) return;
        
        container.innerHTML = '<p class="text-gray-600">System configuration loaded successfully.</p>';
    }
    
    async saveSystemConfig() {
        console.log('Saving system config');
        this.showNotification('System config saved successfully', 'success');
    }
    
    loadIntegrationConfig() {
        console.log('Loading integration config');
    }
    
    loadAdvancedConfig() {
        console.log('Loading advanced config');
    }
    
    showNotification(message, type = 'info') {
        console.log(`Notification [${type}]:`, message);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing SettingsManager');
    window.settingsManager = new SettingsManager();
    window.systemSettings = window.settingsManager; // Compatibility alias
    console.log('SettingsManager initialized');
});
