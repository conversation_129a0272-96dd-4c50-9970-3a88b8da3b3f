{% extends "base.html" %}

{% block title %}路由管理中心 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-route text-blue-600 mr-2"></i>
            路由管理中心
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <button id="refreshBtn" class="btn-secondary">
            <i class="fas fa-sync-alt mr-2"></i>刷新
          </button>
          <button id="testRoutingBtn" class="btn-primary">
            <i class="fas fa-vial mr-2"></i>测试路由
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 加载指示器 -->
  <div id="loadingIndicator" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
          <i class="fas fa-spinner fa-spin text-blue-600 text-xl"></i>
        </div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">加载中...</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">正在加载路由管理数据，请稍候...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 路由健康状态卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-heartbeat text-green-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">系统健康度</dt>
                <dd class="text-lg font-medium text-gray-900" id="systemHealth">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-server text-blue-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">活跃端点</dt>
                <dd class="text-lg font-medium text-gray-900" id="activeEndpoints">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-tachometer-alt text-yellow-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                <dd class="text-lg font-medium text-gray-900" id="avgResponseTime">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-percentage text-purple-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">成功率</dt>
                <dd class="text-lg font-medium text-gray-900" id="successRate">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 负载均衡策略 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-balance-scale text-blue-600 mr-2"></i>
            负载均衡策略
          </h3>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">当前策略</label>
              <select id="strategySelector" class="form-select">
                <option value="round_robin">轮询 (Round Robin)</option>
                <option value="weighted_round_robin">加权轮询 (Weighted Round Robin)</option>
                <option value="least_connections">最少连接 (Least Connections)</option>
                <option value="response_time">响应时间 (Response Time)</option>
                <option value="adaptive">自适应 (Adaptive)</option>
              </select>
            </div>
            
            <div class="flex space-x-3">
              <button id="updateStrategyBtn" class="btn-primary">
                <i class="fas fa-save mr-2"></i>更新策略
              </button>
              <button id="resetStrategyBtn" class="btn-secondary">
                <i class="fas fa-undo mr-2"></i>重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 端点管理 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-network-wired text-green-600 mr-2"></i>
            端点管理
          </h3>
          
          <div class="space-y-4">
            <button id="addEndpointBtn" class="btn-primary w-full">
              <i class="fas fa-plus mr-2"></i>添加端点
            </button>
            
            <div class="text-sm text-gray-600">
              <p>总端点数: <span id="totalEndpoints" class="font-medium">-</span></p>
              <p>健康端点: <span id="healthyEndpoints" class="font-medium text-green-600">-</span></p>
              <p>故障端点: <span id="unhealthyEndpoints" class="font-medium text-red-600">-</span></p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 端点状态表格 -->
    <div class="mt-8 bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          <i class="fas fa-list text-gray-600 mr-2"></i>
          端点状态监控
        </h3>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提供商</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">端点URL</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负载</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">响应时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成功率</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody id="endpointsTableBody" class="bg-white divide-y divide-gray-200">
              <!-- 动态内容 -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 优化建议 -->
    <div class="mt-8 bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>
          优化建议
        </h3>
        
        <div id="optimizationSuggestions" class="space-y-3">
          <!-- 动态内容 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 通知容器 -->
  <div id="notificationContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
</div>

<!-- 添加端点模态框 -->
<div id="addEndpointModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">添加新端点</h3>
    </div>
    
    <form id="addEndpointForm" class="px-6 py-4 space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">提供商</label>
        <input type="text" id="endpointProvider" class="form-input" placeholder="例如: google" required>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">端点URL</label>
        <input type="url" id="endpointUrl" class="form-input" placeholder="https://api.example.com" required>
      </div>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">区域</label>
          <input type="text" id="endpointRegion" class="form-input" placeholder="us-east-1" value="default">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
          <input type="number" id="endpointPriority" class="form-input" min="1" max="10" value="1">
        </div>
      </div>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">权重</label>
          <input type="number" id="endpointWeight" class="form-input" min="0.1" max="10" step="0.1" value="1.0">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">最大并发</label>
          <input type="number" id="endpointMaxConcurrent" class="form-input" min="1" max="1000" value="100">
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">超时时间 (秒)</label>
        <input type="number" id="endpointTimeout" class="form-input" min="1" max="300" value="30">
      </div>
    </form>
    
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button id="cancelAddEndpoint" class="btn-secondary">取消</button>
      <button id="confirmAddEndpoint" class="btn-primary">添加</button>
    </div>
  </div>
</div>

{% endblock %}

{% block body_scripts %}
<script src="/static/js/routing_management.js"></script>
{% endblock %}
