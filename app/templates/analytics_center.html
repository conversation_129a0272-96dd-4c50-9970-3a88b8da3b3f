{% extends "base.html" %}

{% block title %}分析报告中心 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-chart-bar text-purple-600 mr-2"></i>
            分析报告中心
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <select id="periodSelector" class="form-select text-sm">
            <option value="1h">最近1小时</option>
            <option value="24h" selected>最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
          <select id="groupSelector" class="form-select text-sm">
            <option value="">全局分析</option>
            <!-- 动态加载用户组 -->
          </select>
          <button id="refreshBtn" class="btn-secondary text-sm">
            <i class="fas fa-sync-alt mr-1"></i>刷新
          </button>
          <button id="exportBtn" class="btn-primary text-sm">
            <i class="fas fa-download mr-1"></i>导出报告
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 加载指示器 -->
  <div id="loadingIndicator" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100">
          <i class="fas fa-spinner fa-spin text-purple-600 text-xl"></i>
        </div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">分析中...</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">正在生成综合分析报告，请稍候...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-chart-line text-blue-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">总请求数</dt>
                <dd class="text-lg font-medium text-gray-900" id="totalRequests">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-check-circle text-green-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">成功率</dt>
                <dd class="text-lg font-medium text-gray-900" id="successRate">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-dollar-sign text-yellow-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">总成本</dt>
                <dd class="text-lg font-medium text-gray-900" id="totalCost">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-users text-purple-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                <dd class="text-lg font-medium text-gray-900" id="activeUsers">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- 使用趋势图 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-chart-area text-blue-600 mr-2"></i>
            使用趋势
          </h3>
          <div class="h-64">
            <canvas id="usageTrendChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 成本分析图 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-pie-chart text-yellow-600 mr-2"></i>
            成本分析
          </h3>
          <div class="h-64">
            <canvas id="costAnalysisChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- 性能指标 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-tachometer-alt text-green-600 mr-2"></i>
            性能指标
          </h3>
          <div id="performanceMetrics" class="space-y-4">
            <!-- 动态内容 -->
          </div>
        </div>
      </div>

      <!-- 热门模型 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-star text-orange-600 mr-2"></i>
            热门模型
          </h3>
          <div id="topModels" class="space-y-3">
            <!-- 动态内容 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 用户组分析 -->
    <div class="bg-white shadow rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          <i class="fas fa-users-cog text-indigo-600 mr-2"></i>
          用户组分析
        </h3>
        <div id="userGroupAnalysis">
          <!-- 动态内容 -->
        </div>
      </div>
    </div>

    <!-- 优化建议 -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>
          智能建议
        </h3>
        <div id="recommendations" class="space-y-3">
          <!-- 动态内容 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 通知容器 -->
  <div id="notificationContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
</div>

<!-- 导出模态框 -->
<div id="exportModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">导出分析报告</h3>
    </div>
    
    <div class="px-6 py-4 space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">导出格式</label>
        <select id="exportFormat" class="form-select">
          <option value="json">JSON格式</option>
          <option value="csv">CSV格式</option>
        </select>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">包含数据</label>
        <div class="space-y-2">
          <label class="flex items-center">
            <input type="checkbox" id="includeOverview" class="form-checkbox" checked>
            <span class="ml-2 text-sm text-gray-700">概览数据</span>
          </label>
          <label class="flex items-center">
            <input type="checkbox" id="includeTrends" class="form-checkbox" checked>
            <span class="ml-2 text-sm text-gray-700">使用趋势</span>
          </label>
          <label class="flex items-center">
            <input type="checkbox" id="includeCosts" class="form-checkbox" checked>
            <span class="ml-2 text-sm text-gray-700">成本分析</span>
          </label>
          <label class="flex items-center">
            <input type="checkbox" id="includePerformance" class="form-checkbox" checked>
            <span class="ml-2 text-sm text-gray-700">性能指标</span>
          </label>
        </div>
      </div>
    </div>
    
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button id="cancelExport" class="btn-secondary">取消</button>
      <button id="confirmExport" class="btn-primary">导出</button>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/static/js/analytics_center.js"></script>
{% endblock %}
