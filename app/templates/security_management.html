{% extends "base.html" %}

{% block title %}安全管理中心 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-shield-alt text-red-600 mr-2"></i>
            安全管理中心
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <button id="refreshBtn" class="btn-secondary">
            <i class="fas fa-sync-alt mr-2"></i>刷新
          </button>
          <select id="groupSelector" class="form-select">
            <option value="">选择用户组</option>
          </select>
        </div>
      </div>
    </div>
  </nav>

  <!-- 加载指示器 -->
  <div id="loadingIndicator" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
          <i class="fas fa-spinner fa-spin text-blue-600 text-xl"></i>
        </div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">加载中...</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">正在加载安全管理数据，请稍候...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 安全统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-shield-alt text-green-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">安全等级</dt>
                <dd class="text-lg font-medium text-gray-900" id="securityLevel">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">24小时事件</dt>
                <dd class="text-lg font-medium text-gray-900" id="recentEvents">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-list text-blue-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">白名单IP</dt>
                <dd class="text-lg font-medium text-gray-900" id="whitelistSize">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-ban text-red-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">黑名单IP</dt>
                <dd class="text-lg font-medium text-gray-900" id="blacklistSize">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- IP管理区域 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            <i class="fas fa-network-wired text-blue-600 mr-2"></i>
            IP访问控制
          </h3>
        </div>
        <div class="p-6">
          <!-- IP管理标签页 -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
              <button id="whitelistTab" class="tab-button active">
                <i class="fas fa-check-circle mr-2"></i>白名单
              </button>
              <button id="blacklistTab" class="tab-button">
                <i class="fas fa-times-circle mr-2"></i>黑名单
              </button>
            </nav>
          </div>

          <!-- 添加IP表单 -->
          <div class="mt-4">
            <div class="flex space-x-4">
              <input type="text" id="ipInput" placeholder="输入IP地址" class="form-input flex-1">
              <input type="text" id="reasonInput" placeholder="添加原因" class="form-input flex-1">
              <button id="addIpBtn" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>添加
              </button>
            </div>
          </div>

          <!-- IP列表 -->
          <div class="mt-6">
            <div id="ipListContainer" class="space-y-2">
              <!-- IP列表将在这里动态加载 -->
            </div>
          </div>
        </div>
      </div>

      <!-- 安全配置区域 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            <i class="fas fa-cog text-gray-600 mr-2"></i>
            安全配置
          </h3>
        </div>
        <div class="p-6">
          <form id="securityConfigForm" class="space-y-4">
            <div>
              <label class="flex items-center">
                <input type="checkbox" id="enableIpWhitelist" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用IP白名单</span>
              </label>
            </div>
            <div>
              <label class="flex items-center">
                <input type="checkbox" id="enableRateLimiting" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用速率限制</span>
              </label>
            </div>
            <div>
              <label class="flex items-center">
                <input type="checkbox" id="enableAnomalyDetection" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用异常检测</span>
              </label>
            </div>
            <div>
              <label class="flex items-center">
                <input type="checkbox" id="enableAuditLogging" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用审计日志</span>
              </label>
            </div>
            <div>
              <label class="flex items-center">
                <input type="checkbox" id="autoBlockSuspiciousIps" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">自动阻止可疑IP</span>
              </label>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">安全等级</label>
              <select id="securityLevelSelect" class="form-select mt-1">
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="critical">严重</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">密钥轮换间隔（天）</label>
              <input type="number" id="keyRotationInterval" class="form-input mt-1" min="1" max="365">
            </div>
            <button type="submit" class="btn-primary w-full">
              <i class="fas fa-save mr-2"></i>保存配置
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- 安全事件日志 -->
    <div class="mt-6 bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">
            <i class="fas fa-history text-purple-600 mr-2"></i>
            安全事件日志
          </h3>
          <div class="flex space-x-4">
            <select id="severityFilter" class="form-select">
              <option value="">所有严重级别</option>
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="critical">严重</option>
            </select>
            <button id="refreshEventsBtn" class="btn-secondary">
              <i class="fas fa-sync-alt mr-2"></i>刷新
            </button>
          </div>
        </div>
      </div>
      <div class="p-6">
        <div id="eventsContainer">
          <!-- 事件列表将在这里动态加载 -->
        </div>
      </div>
    </div>

    <!-- API密钥轮换计划 -->
    <div class="mt-6 bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          <i class="fas fa-key text-yellow-600 mr-2"></i>
          API密钥轮换计划
        </h3>
      </div>
      <div class="p-6">
        <div id="keyRotationPlan">
          <!-- 密钥轮换计划将在这里动态加载 -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 通知容器 -->
<div id="notificationContainer" class="fixed top-4 right-4 z-50 space-y-2">
  <!-- 通知将在这里动态添加 -->
</div>

<script src="/static/js/security_management.js"></script>
{% endblock %}
