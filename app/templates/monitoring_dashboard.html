{% extends "base.html" %}

{% block title %}监控大屏 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-900 text-white">
  <!-- 顶部导航 -->
  <nav class="bg-gray-800 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-bold text-white">
            <i class="fas fa-chart-line text-blue-400 mr-2"></i>
            Gemini Balance 监控大屏
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-300">
            <span id="currentTime"></span>
          </div>
          <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium">
            <i class="fas fa-sync-alt mr-2"></i>刷新
          </button>
          <button id="fullscreenBtn" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-md text-sm font-medium">
            <i class="fas fa-expand mr-2"></i>全屏
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 系统状态概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 shadow-lg">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-server text-white text-3xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-blue-100 text-sm">系统状态</p>
            <p class="text-white text-2xl font-bold" id="systemStatus">正常</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-6 shadow-lg">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-users text-white text-3xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-green-100 text-sm">活跃用户组</p>
            <p class="text-white text-2xl font-bold" id="activeGroups">-</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-6 shadow-lg">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-chart-bar text-white text-3xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-purple-100 text-sm">实时QPS</p>
            <p class="text-white text-2xl font-bold" id="currentQPS">-</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 rounded-lg p-6 shadow-lg">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-white text-3xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-yellow-100 text-sm">活跃告警</p>
            <p class="text-white text-2xl font-bold" id="activeAlerts">-</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 请求量趋势图 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-white">请求量趋势</h3>
          <div class="flex space-x-2">
            <button class="time-filter-btn active" data-period="1h">1小时</button>
            <button class="time-filter-btn" data-period="6h">6小时</button>
            <button class="time-filter-btn" data-period="24h">24小时</button>
          </div>
        </div>
        <div class="h-64">
          <canvas id="requestChart"></canvas>
        </div>
      </div>

      <!-- 成功率图表 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-white">成功率统计</h3>
          <span class="text-green-400 text-sm" id="overallSuccessRate">-</span>
        </div>
        <div class="h-64">
          <canvas id="successChart"></canvas>
        </div>
      </div>
    </div>

    <!-- 用户组状态和模型使用 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- 用户组状态 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-white mb-4">用户组状态</h3>
        <div class="space-y-3" id="groupStatusList">
          <!-- 用户组状态将通过JavaScript动态加载 -->
        </div>
      </div>

      <!-- 模型使用分布 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-white mb-4">模型使用分布</h3>
        <div class="h-48">
          <canvas id="modelChart"></canvas>
        </div>
      </div>

      <!-- 响应时间统计 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-white mb-4">响应时间</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-300">平均响应时间</span>
            <span class="text-white font-semibold" id="avgResponseTime">-</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-300">P95响应时间</span>
            <span class="text-white font-semibold" id="p95ResponseTime">-</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-300">P99响应时间</span>
            <span class="text-white font-semibold" id="p99ResponseTime">-</span>
          </div>
          <div class="h-32 mt-4">
            <canvas id="responseTimeChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志和告警 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 实时告警 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-white">实时告警</h3>
          <span class="bg-red-600 text-white px-2 py-1 rounded-full text-xs" id="alertCount">0</span>
        </div>
        <div class="space-y-3 max-h-64 overflow-y-auto" id="alertsList">
          <!-- 告警列表将通过JavaScript动态加载 -->
        </div>
      </div>

      <!-- 系统资源使用 -->
      <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-white mb-4">系统资源</h3>
        <div class="space-y-4">
          <div>
            <div class="flex justify-between text-sm mb-1">
              <span class="text-gray-300">CPU使用率</span>
              <span class="text-white" id="cpuUsage">-</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" id="cpuBar" style="width: 0%"></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between text-sm mb-1">
              <span class="text-gray-300">内存使用率</span>
              <span class="text-white" id="memoryUsage">-</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full transition-all duration-300" id="memoryBar" style="width: 0%"></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between text-sm mb-1">
              <span class="text-gray-300">缓存命中率</span>
              <span class="text-white" id="cacheHitRate">-</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" id="cacheBar" style="width: 0%"></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between text-sm mb-1">
              <span class="text-gray-300">数据库连接</span>
              <span class="text-white" id="dbConnections">-</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div class="bg-yellow-600 h-2 rounded-full transition-all duration-300" id="dbBar" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/static/js/monitoring_dashboard.js"></script>

<style>
.time-filter-btn {
  @apply px-3 py-1 text-xs rounded-md bg-gray-700 text-gray-300 hover:bg-gray-600 transition-colors;
}

.time-filter-btn.active {
  @apply bg-blue-600 text-white;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}

/* 全屏样式 */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #111827;
}
</style>
{% endblock %}
