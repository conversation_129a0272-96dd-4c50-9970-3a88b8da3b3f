{% extends "base.html" %}

{% block title %}管理控制台 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-tachometer-alt text-primary-600 mr-2"></i>
            Gemini Balance 管理控制台
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-500" id="lastUpdate">最后更新: -</span>
          <button id="refreshBtn" class="btn-secondary">
            <i class="fas fa-sync-alt mr-2"></i>刷新
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 系统核心 -->
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        <i class="fas fa-server text-blue-600 mr-2"></i>
        系统核心
        <span class="text-sm text-gray-500 font-normal ml-2">原生功能</span>
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="/web/monitoring" class="group bg-white p-6 rounded-lg shadow hover:shadow-lg transition-all border-l-4 border-green-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-chart-line text-green-600 text-3xl"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900 group-hover:text-green-700">统一监控中心</h3>
              <p class="text-sm text-gray-500">系统、API、性能全方位监控</p>
              <div class="mt-2 flex items-center text-xs text-green-600">
                <i class="fas fa-circle mr-1"></i>
                <span>包含所有监控功能</span>
              </div>
            </div>
          </div>
        </a>

        <a href="/config" class="group bg-white p-6 rounded-lg shadow hover:shadow-lg transition-all border-l-4 border-purple-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-cog text-purple-600 text-3xl"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900 group-hover:text-purple-700">基础配置中心</h3>
              <p class="text-sm text-gray-500">API密钥、模型、基础参数设置</p>
              <div class="mt-2 flex items-center text-xs text-purple-600">
                <i class="fas fa-circle mr-1"></i>
                <span>核心系统配置</span>
              </div>
            </div>
          </div>
        </a>

        <a href="/web/user-groups" class="group bg-white p-6 rounded-lg shadow hover:shadow-lg transition-all border-l-4 border-indigo-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-users text-indigo-600 text-3xl"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900 group-hover:text-indigo-700">用户组管理</h3>
              <p class="text-sm text-gray-500">创建和管理用户组权限</p>
              <div class="mt-2 flex items-center text-xs text-indigo-600">
                <i class="fas fa-circle mr-1"></i>
                <span>权限管理核心</span>
              </div>
            </div>
          </div>
        </a>
      </div>
    </div>

    <!-- 专业化管理工具（擴展） -->
    {% if extension_nav_items %}
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        <i class="fas fa-puzzle-piece text-orange-600 mr-2"></i>
        专业化工具
        <span class="text-sm text-gray-500 font-normal ml-2">扩展功能 - 可独立升级</span>
      </h2>
      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
        <div class="flex items-center">
          <i class="fas fa-info-circle text-orange-600 mr-2"></i>
          <span class="text-sm text-orange-800">
            这些工具专注于特定领域的深度管理，不与核心功能重复，可独立更新维护
          </span>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {% for item in extension_nav_items %}
          <a href="{{ item.url }}" class="group bg-white p-6 rounded-lg shadow hover:shadow-lg transition-all border-l-4 border-{{ item.color }}-500">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <i class="{{ item.icon }} text-{{ item.color }}-600 text-3xl"></i>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900 group-hover:text-{{ item.color }}-700">{{ item.name }}</h3>
                <p class="text-sm text-gray-500">{{ item.description }}</p>
                <div class="mt-2 flex items-center text-xs text-{{ item.color }}-600">
                  <i class="fas fa-puzzle-piece mr-1"></i>
                  <span>扩展工具</span>
                </div>
              </div>
            </div>
          </a>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- 快速工具 -->
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">
        <i class="fas fa-tools text-gray-600 mr-2"></i>
        快速工具
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a href="/error_logs" class="group bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border-l-4 border-red-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-red-600 text-xl group-hover:text-red-700"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-gray-900 group-hover:text-red-700">错误日志</h3>
              <p class="text-xs text-gray-500">系统错误查看</p>
            </div>
          </div>
        </a>

        <a href="/api/docs" class="group bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border-l-4 border-blue-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-book text-blue-600 text-xl group-hover:text-blue-700"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-700">API文档</h3>
              <p class="text-xs text-gray-500">接口文档查看</p>
            </div>
          </div>
        </a>

        <a href="/docs" class="group bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border-l-4 border-green-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-question-circle text-green-600 text-xl group-hover:text-green-700"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-gray-900 group-hover:text-green-700">帮助文档</h3>
              <p class="text-xs text-gray-500">使用说明查看</p>
            </div>
          </div>
        </a>

        <a href="/api/health" class="group bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border-l-4 border-purple-500">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-heartbeat text-purple-600 text-xl group-hover:text-purple-700"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-700">健康检查</h3>
              <p class="text-xs text-gray-500">系统状态检查</p>
            </div>
          </div>
        </a>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- 系统状态 -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-server text-primary-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">系统状态</dt>
                <dd class="flex items-center">
                  <div class="text-lg font-medium text-gray-900" id="systemStatus">正常运行</div>
                  <div class="ml-2 w-2 h-2 bg-green-400 rounded-full" id="statusIndicator"></div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <span class="text-gray-500">运行时间: </span>
            <span class="font-medium text-gray-900" id="uptime">-</span>
          </div>
        </div>
      </div>

      <!-- 用户组统计 -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-users text-green-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">用户组</dt>
                <dd class="text-lg font-medium text-gray-900">
                  <span id="totalGroups">-</span> / <span id="activeGroups">-</span>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <span class="text-gray-500">总数 / 活跃</span>
          </div>
        </div>
      </div>

      <!-- 请求统计 -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-chart-bar text-blue-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">今日请求</dt>
                <dd class="text-lg font-medium text-gray-900" id="todayRequests">-</dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <span class="text-gray-500">成功率: </span>
            <span class="font-medium text-gray-900" id="successRate">-</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动和告警 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最近活动 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg leading-6 font-medium text-gray-900">最近活动</h3>
        </div>
        <div class="p-6">
          <div class="flow-root">
            <ul class="-mb-8" id="recentActivities">
              <!-- 活动列表将通过JavaScript动态加载 -->
              <li class="flex items-center justify-center py-8">
                <div class="text-center">
                  <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
                  <p class="text-gray-500">加载中...</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 系统告警 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg leading-6 font-medium text-gray-900">系统告警</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800" id="alertCount">
              0 个活跃告警
            </span>
          </div>
        </div>
        <div class="p-6">
          <div class="space-y-4" id="systemAlerts">
            <!-- 告警列表将通过JavaScript动态加载 -->
            <div class="flex items-center justify-center py-8">
              <div class="text-center">
                <i class="fas fa-shield-alt text-green-400 text-2xl mb-2"></i>
                <p class="text-gray-500">系统运行正常</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-8 bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">快速操作</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button onclick="createUserGroup()" class="btn-primary w-full">
            <i class="fas fa-plus mr-2"></i>创建用户组
          </button>
          <button onclick="viewMonitoring()" class="btn-secondary w-full">
            <i class="fas fa-chart-line mr-2"></i>查看监控
          </button>
          <button onclick="exportData()" class="btn-secondary w-full">
            <i class="fas fa-download mr-2"></i>导出数据
          </button>
          <button onclick="systemSettings()" class="btn-secondary w-full">
            <i class="fas fa-cog mr-2"></i>系统设置
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
class DashboardHome {
    constructor() {
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadDashboardData();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadDashboardData();
        });
    }
    
    async loadDashboardData() {
        try {
            await Promise.all([
                this.loadSystemStatus(),
                this.loadUserGroupStats(),
                this.loadRequestStats(),
                this.loadRecentActivities(),
                this.loadSystemAlerts()
            ]);
            
            document.getElementById('lastUpdate').textContent = 
                '最后更新: ' + new Date().toLocaleTimeString('zh-CN');
        } catch (error) {
            console.error('加载仪表板数据失败:', error);
        }
    }
    
    async loadSystemStatus() {
        try {
            const response = await fetch('/monitoring/system/overview', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                document.getElementById('systemStatus').textContent = 
                    data.system_status === 'healthy' ? '正常运行' : '异常';
                document.getElementById('statusIndicator').className = 
                    `ml-2 w-2 h-2 rounded-full ${data.system_status === 'healthy' ? 'bg-green-400' : 'bg-red-400'}`;
                document.getElementById('uptime').textContent = data.uptime || '-';
            }
        } catch (error) {
            console.error('加载系统状态失败:', error);
        }
    }
    
    async loadUserGroupStats() {
        try {
            const response = await fetch('/admin/groups/statistics/overview', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                document.getElementById('totalGroups').textContent = data.total_groups || 0;
                document.getElementById('activeGroups').textContent = data.active_groups || 0;
            }
        } catch (error) {
            console.error('加载用户组统计失败:', error);
        }
    }
    
    async loadRequestStats() {
        try {
            const response = await fetch('/monitoring/metrics/requests?period=24h', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                document.getElementById('todayRequests').textContent = 
                    this.formatNumber(data.total_requests || 0);
                document.getElementById('successRate').textContent = 
                    (data.success_rate || 0).toFixed(1) + '%';
            }
        } catch (error) {
            console.error('加载请求统计失败:', error);
        }
    }
    
    async loadRecentActivities() {
        try {
            // 模拟最近活动数据
            const activities = [
                { type: 'user_group_created', message: '创建了用户组 "test-group"', time: new Date(Date.now() - 300000) },
                { type: 'config_updated', message: '更新了系统配置', time: new Date(Date.now() - 600000) },
                { type: 'alert_resolved', message: '解决了高错误率告警', time: new Date(Date.now() - 900000) },
                { type: 'quota_updated', message: '更新了配额设置', time: new Date(Date.now() - 1200000) }
            ];
            
            this.updateRecentActivities(activities);
        } catch (error) {
            console.error('加载最近活动失败:', error);
        }
    }
    
    updateRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        
        if (activities.length === 0) {
            container.innerHTML = '<li class="text-center py-4 text-gray-500">暂无最近活动</li>';
            return;
        }
        
        container.innerHTML = activities.map((activity, index) => `
            <li class="${index < activities.length - 1 ? 'pb-4' : ''}">
              <div class="relative ${index < activities.length - 1 ? 'pb-4' : ''}">
                ${index < activities.length - 1 ? '<span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>' : ''}
                <div class="relative flex space-x-3">
                  <div>
                    <span class="h-8 w-8 rounded-full ${this.getActivityColor(activity.type)} flex items-center justify-center ring-8 ring-white">
                      <i class="fas ${this.getActivityIcon(activity.type)} text-white text-sm"></i>
                    </span>
                  </div>
                  <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                    <div>
                      <p class="text-sm text-gray-500">${activity.message}</p>
                    </div>
                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                      ${this.formatTime(activity.time)}
                    </div>
                  </div>
                </div>
              </div>
            </li>
        `).join('');
    }
    
    getActivityColor(type) {
        const colors = {
            'user_group_created': 'bg-green-500',
            'config_updated': 'bg-blue-500',
            'alert_resolved': 'bg-yellow-500',
            'quota_updated': 'bg-purple-500'
        };
        return colors[type] || 'bg-gray-500';
    }
    
    getActivityIcon(type) {
        const icons = {
            'user_group_created': 'fa-plus',
            'config_updated': 'fa-cog',
            'alert_resolved': 'fa-check',
            'quota_updated': 'fa-edit'
        };
        return icons[type] || 'fa-info';
    }
    
    async loadSystemAlerts() {
        try {
            const response = await fetch('/monitoring/alerts/active', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });
            if (response.ok) {
                const data = await response.json();
                this.updateSystemAlerts(data.alerts || []);
            }
        } catch (error) {
            console.error('加载系统告警失败:', error);
        }
    }
    
    updateSystemAlerts(alerts) {
        const container = document.getElementById('systemAlerts');
        const countElement = document.getElementById('alertCount');
        
        countElement.textContent = `${alerts.length} 个活跃告警`;
        countElement.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            alerts.length > 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
        }`;
        
        if (alerts.length === 0) {
            container.innerHTML = `
                <div class="flex items-center justify-center py-8">
                    <div class="text-center">
                        <i class="fas fa-shield-alt text-green-400 text-2xl mb-2"></i>
                        <p class="text-gray-500">系统运行正常</p>
                    </div>
                </div>
            `;
            return;
        }
        
        container.innerHTML = alerts.slice(0, 5).map(alert => `
            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-${this.getSeverityColor(alert.severity)}-500"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900">${alert.message}</p>
                    <p class="text-xs text-gray-500">${alert.group_id} • ${this.formatTime(new Date(alert.triggered_at))}</p>
                </div>
            </div>
        `).join('');
    }
    
    getSeverityColor(severity) {
        const colors = {
            'low': 'blue',
            'medium': 'yellow',
            'high': 'orange',
            'critical': 'red'
        };
        return colors[severity] || 'gray';
    }
    
    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) {
            return '刚刚';
        } else if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }
    
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }
    
    startAutoRefresh() {
        // 每60秒自动刷新
        setInterval(() => {
            this.loadDashboardData();
        }, 60000);
    }
}

// 快速操作函数
function createUserGroup() {
    window.location.href = '/web/user-groups';
}

function viewMonitoring() {
    window.location.href = '/web/monitoring';
}

function exportData() {
    // 实现数据导出功能
    alert('数据导出功能开发中...');
}

function systemSettings() {
    window.location.href = '/config';
}

// 初始化仪表板
const dashboardHome = new DashboardHome();
</script>
{% endblock %}
