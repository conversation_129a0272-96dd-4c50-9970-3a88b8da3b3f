{% extends "base.html" %}

{% block title %}用户组管理 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-users text-primary-600 mr-2"></i>
            用户组管理
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <button id="refreshBtn" class="btn-secondary">
            <i class="fas fa-sync-alt mr-2"></i>刷新
          </button>
          <button id="createGroupBtn" class="btn-primary">
            <i class="fas fa-plus mr-2"></i>创建用户组
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-users text-primary-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">总用户组</dt>
                <dd class="text-lg font-medium text-gray-900" id="totalGroups">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-chart-line text-success-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">活跃用户组</dt>
                <dd class="text-lg font-medium text-gray-900" id="activeGroups">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">告警用户组</dt>
                <dd class="text-lg font-medium text-gray-900" id="alertGroups">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-tachometer-alt text-blue-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">总请求数</dt>
                <dd class="text-lg font-medium text-gray-900" id="totalRequests">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div class="flex-1 min-w-0">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
              </div>
              <input type="text" id="searchInput" 
                     class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                     placeholder="搜索用户组...">
            </div>
          </div>
          <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-3">
            <select id="statusFilter" class="form-select">
              <option value="">所有状态</option>
              <option value="active">活跃</option>
              <option value="inactive">非活跃</option>
              <option value="suspended">暂停</option>
            </select>
            <select id="priorityFilter" class="form-select">
              <option value="">所有优先级</option>
              <option value="high">高</option>
              <option value="medium">中</option>
              <option value="low">低</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户组列表 -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">用户组列表</h3>
      </div>
      <div id="groupsList" class="divide-y divide-gray-200">
        <!-- 用户组项目将通过JavaScript动态加载 -->
        <div class="flex items-center justify-center py-12">
          <div class="text-center">
            <i class="fas fa-spinner fa-spin text-gray-400 text-3xl mb-4"></i>
            <p class="text-gray-500">加载中...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6">
      <div class="flex-1 flex justify-between sm:hidden">
        <button id="prevPageMobile" class="btn-secondary">上一页</button>
        <button id="nextPageMobile" class="btn-secondary">下一页</button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            显示第 <span id="startItem" class="font-medium">1</span> 到 
            <span id="endItem" class="font-medium">10</span> 项，
            共 <span id="totalItems" class="font-medium">0</span> 项
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
            <!-- 分页按钮将通过JavaScript动态生成 -->
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 创建/编辑用户组模态框 -->
<div id="groupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto shadow-lg rounded-md bg-white mb-10">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900" id="modalTitle">创建用户组</h3>
        <button id="closeModal" class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      
      <form id="groupForm" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="groupId" class="block text-sm font-medium text-gray-700">用户组ID</label>
            <input type="text" id="groupId" name="groupId" required
                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
          </div>
          
          <div>
            <label for="groupName" class="block text-sm font-medium text-gray-700">用户组名称</label>
            <input type="text" id="groupName" name="groupName" required
                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
          </div>
          
          <div>
            <label for="priority" class="block text-sm font-medium text-gray-700">优先级</label>
            <select id="priority" name="priority" required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
              <option value="8">高 (8)</option>
              <option value="5" selected>中 (5)</option>
              <option value="2">低 (2)</option>
            </select>
          </div>
          
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">状态</label>
            <select id="status" name="status" required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
              <option value="ACTIVE" selected>活跃</option>
              <option value="INACTIVE">非活跃</option>
              <option value="SUSPENDED">暂停</option>
            </select>
          </div>
        </div>
        
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700">描述</label>
          <textarea id="description" name="description" rows="3"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder="用户组描述..."></textarea>
        </div>

        <!-- Token配额设置 -->
        <div class="border-t pt-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">Token配额设置</h4>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="tokenLimit" class="block text-sm font-medium text-gray-700">Token限制</label>
              <input type="number" id="tokenLimit" name="tokenLimit" min="0" step="1000" value="0"
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                     placeholder="0表示无限制">
              <p class="text-xs text-gray-500 mt-1">总Token使用限制，0表示无限制</p>
            </div>

            <div>
              <label for="expiryDate" class="block text-sm font-medium text-gray-700">使用到期日期</label>
              <input type="date" id="expiryDate" name="expiryDate"
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
              <p class="text-xs text-gray-500 mt-1">留空表示没有截止日期</p>
            </div>
          </div>

          <!-- 模型Token消耗倍率 -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">模型Token消耗倍率</label>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="mb-3">
                <p class="text-sm text-blue-600 font-medium">🆓 Google AI Studio 免费配额模型 (推荐)</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- 2025年最新免费配额模型 -->
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-2.5-flash ⭐</span>
                  <input type="number" id="tokenRate_gemini-2.5-flash" step="0.1" min="0.1" value="1.0"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-2.5-flash-lite</span>
                  <input type="number" id="tokenRate_gemini-2.5-flash-lite" step="0.1" min="0.1" value="0.4"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-2.0-flash</span>
                  <input type="number" id="tokenRate_gemini-2.0-flash" step="0.1" min="0.1" value="0.8"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-2.0-flash-lite</span>
                  <input type="number" id="tokenRate_gemini-2.0-flash-lite" step="0.1" min="0.1" value="0.3"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-1.5-flash</span>
                  <input type="number" id="tokenRate_gemini-1.5-flash" step="0.1" min="0.1" value="0.9"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-1.5-flash-8b</span>
                  <input type="number" id="tokenRate_gemini-1.5-flash-8b" step="0.1" min="0.1" value="0.5"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <!-- 高性能模型 (付费推荐) -->
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-1.5-pro</span>
                  <input type="number" id="tokenRate_gemini-1.5-pro" step="0.1" min="0.1" value="2.5"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">gemini-2.5-pro</span>
                  <input type="number" id="tokenRate_gemini-2.5-pro" step="0.1" min="0.1" value="5.0"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">其他模型</span>
                  <input type="number" id="tokenRate_default" step="0.1" min="0.1" value="1.0"
                         class="w-20 px-2 py-1 text-sm border border-gray-300 rounded">
                </div>
              </div>
              <p class="text-xs text-gray-500 mt-2">倍率越高表示该模型消耗更多token配额。例如：2.0表示使用1000个token会消耗2000个配额。</p>
              <p class="text-xs text-green-600 mt-1">💡 推荐使用 gemini-2.5-flash (⭐基准模型) 或 gemini-2.0-flash-lite (最经济)，享受Google AI Studio免费配额</p>
              <p class="text-xs text-blue-600 mt-1">🔥 2025年最新: gemini-2.5-flash 为混合推理模型，性能卓越且免费</p>
            </div>
          </div>
        </div>

        <!-- 高级设置 -->
        <div class="border-t pt-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">高级设置</h4>
          <div class="space-y-4">
            <div class="flex items-center">
              <input type="checkbox" id="autoFallback" name="autoFallback" checked
                     class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
              <label for="autoFallback" class="ml-2 block text-sm text-gray-900">启用自动降级</label>
              <p class="text-xs text-gray-500 ml-6">当首选模型不可用时，自动降级到可用模型</p>
            </div>
            <div class="flex items-center">
              <input type="checkbox" id="contextIsolation" name="contextIsolation" checked
                     class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
              <label for="contextIsolation" class="ml-2 block text-sm text-gray-900">启用上下文隔离</label>
              <p class="text-xs text-gray-500 ml-6">隔离不同用户组的对话上下文</p>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-6 border-t">
          <button type="button" id="cancelBtn" class="btn-secondary">取消</button>
          <button type="submit" class="btn-primary">
            <span id="submitText">创建用户组</span>
            <i id="submitSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- 用户组详情模态框 -->
<div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">用户组详情</h3>
        <button id="closeDetailModal" class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      
      <div id="detailContent">
        <!-- 详情内容将通过JavaScript动态加载 -->
      </div>
    </div>
  </div>
</div>

<script src="/static/js/user_groups.js"></script>
{% endblock %}
