{% extends "base.html" %}

{% block title %}系统设置中心 - Gemini Balance{% endblock %}

{% block extra_styles %}
<style>
.config-tab {
  @apply px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200 flex items-center cursor-pointer border-2 border-transparent;
}

.config-tab.active {
  @apply text-blue-600 bg-blue-50 border-2 border-blue-600 font-semibold;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500;
}

.form-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.form-radio {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2;
}

.modal {
  @apply flex items-center justify-center;
}
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-cogs text-gray-600 mr-2"></i>
            系统设置中心
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <div class="relative">
            <input type="text" id="searchConfig" placeholder="搜索配置项..." 
                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm">
            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
          </div>
          <button id="exportConfigBtn" class="btn-secondary text-sm">
            <i class="fas fa-download mr-1"></i>导出配置
          </button>
          <button id="resetConfigBtn" class="btn-danger text-sm">
            <i class="fas fa-undo mr-1"></i>重置配置
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 加载指示器 -->
  <div id="loadingIndicator" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
          <i class="fas fa-spinner fa-spin text-gray-600 text-xl"></i>
        </div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">加载中...</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">正在加载系统设置，请稍候...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 配置分类导航 -->
    <div class="mb-6">
      <nav class="flex space-x-8" aria-label="Tabs">
        <button id="tab-context" class="config-tab active" data-section="context">
          <i class="fas fa-layer-group mr-2"></i>上下文隔离
        </button>
        <button id="tab-system" class="config-tab" data-section="system">
          <i class="fas fa-server mr-2"></i>系统参数
        </button>
        <button id="tab-integration" class="config-tab" data-section="integration">
          <i class="fas fa-plug mr-2"></i>集成配置
        </button>
        <button id="tab-advanced" class="config-tab" data-section="advanced">
          <i class="fas fa-tools mr-2"></i>高级选项
        </button>
      </nav>
    </div>

    <!-- 上下文隔离配置 -->
    <div id="section-context" class="config-section">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-layer-group text-blue-600 mr-2"></i>
            上下文隔离配置
          </h3>
          
          <!-- 用户组选择 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">配置范围</label>
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input type="radio" name="configScope" value="global" class="form-radio" checked>
                <span class="ml-2 text-sm text-gray-700">全局配置</span>
              </label>
              <label class="flex items-center">
                <input type="radio" name="configScope" value="group" class="form-radio">
                <span class="ml-2 text-sm text-gray-700">用户组配置</span>
              </label>
            </div>
            <select id="groupSelector" class="form-select mt-2 hidden">
              <option value="">选择用户组...</option>
            </select>
          </div>

          <!-- 上下文隔离设置 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">启用上下文隔离</label>
              <div class="flex items-center">
                <input type="checkbox" id="enableIsolation" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用智能上下文隔离</span>
              </div>
              <p class="text-xs text-gray-500 mt-1">启用后将自动管理上下文长度，避免内存溢出</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">最大上下文长度</label>
              <input type="number" id="maxContextLength" class="form-input" min="1024" max="32768" step="1024">
              <p class="text-xs text-gray-500 mt-1">单次请求的最大上下文token数量</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">上下文重叠长度</label>
              <input type="number" id="contextOverlap" class="form-input" min="0" max="1024" step="50">
              <p class="text-xs text-gray-500 mt-1">分段时保留的重叠token数量</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">隔离策略</label>
              <select id="isolationStrategy" class="form-select">
                <option value="sliding_window">滑动窗口</option>
                <option value="truncate_head">截断头部</option>
                <option value="truncate_tail">截断尾部</option>
                <option value="summarize">智能摘要</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">上下文超长时的处理策略</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">内存阈值</label>
              <input type="range" id="memoryThreshold" class="w-full" min="0.5" max="1.0" step="0.1">
              <div class="flex justify-between text-xs text-gray-500">
                <span>50%</span>
                <span id="memoryThresholdValue">80%</span>
                <span>100%</span>
              </div>
              <p class="text-xs text-gray-500 mt-1">触发上下文清理的内存使用阈值</p>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button id="resetContextBtn" class="btn-secondary">重置</button>
            <button id="saveContextBtn" class="btn-primary">保存配置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统参数配置 -->
    <div id="section-system" class="config-section hidden">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-server text-green-600 mr-2"></i>
            系统参数配置
          </h3>
          
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
              <i class="fas fa-info-circle text-blue-600 mt-0.5 mr-2"></i>
              <div class="text-sm text-blue-800">
                <strong>提示：</strong>基础系统配置请使用 
                <a href="/config" class="underline font-medium hover:text-blue-900">基础配置中心</a>。
                此处仅显示高级系统参数。
              </div>
            </div>
          </div>

          <!-- 降级功能开关 -->
          <div class="mb-6">
            <div class="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div>
                <h4 class="text-sm font-medium text-yellow-800">智能降级功能</h4>
                <p class="text-xs text-yellow-700 mt-1">当高级模型不可用时，自动降级到可用的低级模型</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" id="enableFallback" class="sr-only peer" checked>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          <div id="systemConfigContainer" class="space-y-6">
            <!-- 动态加载系统配置项 -->
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button id="resetSystemBtn" class="btn-secondary">重置</button>
            <button id="saveSystemBtn" class="btn-primary">保存配置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 集成配置 -->
    <div id="section-integration" class="config-section hidden">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-plug text-purple-600 mr-2"></i>
            集成配置
          </h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 外部服务集成 -->
            <div>
              <h4 class="text-md font-medium text-gray-700 mb-4">外部服务集成</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Redis连接</label>
                  <input type="text" id="redisUrl" class="form-input" placeholder="redis://localhost:6379">
                  <p class="text-xs text-gray-500 mt-1">Redis缓存服务器地址</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">数据库连接</label>
                  <input type="text" id="databaseUrl" class="form-input" placeholder="postgresql://user:pass@localhost/db">
                  <p class="text-xs text-gray-500 mt-1">PostgreSQL数据库连接字符串</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">监控服务</label>
                  <input type="text" id="monitoringUrl" class="form-input" placeholder="http://prometheus:9090">
                  <p class="text-xs text-gray-500 mt-1">Prometheus监控服务地址</p>
                </div>
              </div>
            </div>

            <!-- API集成设置 -->
            <div>
              <h4 class="text-md font-medium text-gray-700 mb-4">API集成设置</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">API超时时间</label>
                  <input type="number" id="apiTimeout" class="form-input" min="1" max="300" value="30">
                  <p class="text-xs text-gray-500 mt-1">API请求超时时间（秒）</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">重试次数</label>
                  <input type="number" id="retryCount" class="form-input" min="0" max="10" value="3">
                  <p class="text-xs text-gray-500 mt-1">API请求失败时的重试次数</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">并发限制</label>
                  <input type="number" id="concurrencyLimit" class="form-input" min="1" max="1000" value="100">
                  <p class="text-xs text-gray-500 mt-1">同时处理的最大请求数</p>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button id="testIntegrationBtn" class="btn-secondary">
              <i class="fas fa-vial mr-1"></i>测试连接
            </button>
            <button id="resetIntegrationBtn" class="btn-secondary">重置</button>
            <button id="saveIntegrationBtn" class="btn-primary">保存配置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级选项 -->
    <div id="section-advanced" class="config-section hidden">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-tools text-red-600 mr-2"></i>
            高级选项
          </h3>
          
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
              <i class="fas fa-exclamation-triangle text-yellow-600 mt-0.5 mr-2"></i>
              <div class="text-sm text-yellow-800">
                <strong>警告：</strong>高级选项可能影响系统稳定性，请谨慎修改。建议在测试环境中验证后再应用到生产环境。
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 性能调优 -->
            <div>
              <h4 class="text-md font-medium text-gray-700 mb-4">性能调优</h4>
              <div class="space-y-4">
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="enableDebugMode" class="form-checkbox">
                    <span class="ml-2 text-sm text-gray-700">启用调试模式</span>
                  </label>
                  <p class="text-xs text-gray-500 mt-1">启用详细日志和性能监控</p>
                </div>
                
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="enableProfiling" class="form-checkbox">
                    <span class="ml-2 text-sm text-gray-700">启用性能分析</span>
                  </label>
                  <p class="text-xs text-gray-500 mt-1">收集详细的性能分析数据</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">工作进程数</label>
                  <input type="number" id="workerProcesses" class="form-input" min="1" max="32" value="4">
                  <p class="text-xs text-gray-500 mt-1">并行处理的工作进程数量</p>
                </div>
              </div>
            </div>

            <!-- 安全设置 -->
            <div>
              <h4 class="text-md font-medium text-gray-700 mb-4">安全设置</h4>
              <div class="space-y-4">
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="enableStrictMode" class="form-checkbox">
                    <span class="ml-2 text-sm text-gray-700">启用严格模式</span>
                  </label>
                  <p class="text-xs text-gray-500 mt-1">启用更严格的安全检查</p>
                </div>
                
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="enableAuditLog" class="form-checkbox">
                    <span class="ml-2 text-sm text-gray-700">启用审计日志</span>
                  </label>
                  <p class="text-xs text-gray-500 mt-1">记录所有配置变更和敏感操作</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">会话超时时间</label>
                  <input type="number" id="sessionTimeout" class="form-input" min="300" max="86400" value="3600">
                  <p class="text-xs text-gray-500 mt-1">用户会话超时时间（秒）</p>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button id="resetAdvancedBtn" class="btn-secondary">重置</button>
            <button id="saveAdvancedBtn" class="btn-primary">保存配置</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 通知容器 -->
  <div id="notificationContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
</div>

<!-- 确认对话框 -->
<div id="confirmModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900" id="confirmTitle">确认操作</h3>
    </div>
    
    <div class="px-6 py-4">
      <p class="text-sm text-gray-700" id="confirmMessage">您确定要执行此操作吗？</p>
    </div>
    
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button id="cancelConfirm" class="btn-secondary">取消</button>
      <button id="confirmAction" class="btn-danger">确认</button>
    </div>
  </div>
</div>

{% endblock %}

{% block body_scripts %}
<script src="/static/js/system_settings.js"></script>
{% endblock %}
