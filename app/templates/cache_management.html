{% extends "base.html" %}

{% block title %}缓存管理中心 - Gemini Balance{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
  <!-- 导航栏 -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            <i class="fas fa-database text-blue-600 mr-2"></i>
            缓存管理中心
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <button id="refreshBtn" class="btn-secondary">
            <i class="fas fa-sync-alt mr-2"></i>刷新
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- 加载指示器 -->
  <div id="loadingIndicator" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
          <i class="fas fa-spinner fa-spin text-blue-600 text-xl"></i>
        </div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">加载中...</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">正在加载缓存管理数据，请稍候...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- 缓存统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-tachometer-alt text-green-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">命中率</dt>
                <dd class="text-lg font-medium text-gray-900" id="hitRate">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-memory text-blue-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">缓存大小</dt>
                <dd class="text-lg font-medium text-gray-900" id="cacheSize">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-clock text-yellow-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                <dd class="text-lg font-medium text-gray-900" id="avgResponseTime">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">缓存利用率</dt>
                <dd class="text-lg font-medium text-gray-900" id="utilization">-</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 缓存配置区域 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-cog text-blue-600 mr-2"></i>
            缓存配置
          </h3>

          <!-- 配置选项卡 -->
          <div class="border-b border-gray-200 mb-4">
            <nav class="-mb-px flex space-x-8">
              <button id="globalConfigTab" class="config-tab active border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                全局配置
              </button>
              <button id="groupConfigTab" class="config-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                用户组配置
              </button>
            </nav>
          </div>

          <!-- 全局配置面板 -->
          <div id="globalConfigPanel" class="config-panel">
            <form id="globalConfigForm">
              <div class="space-y-4">
                <div class="flex items-center">
                  <input type="checkbox" id="globalCacheEnabled" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <label for="globalCacheEnabled" class="ml-2 block text-sm text-gray-900">启用全局缓存</label>
                </div>

                <div>
                  <label for="globalCacheTtl" class="block text-sm font-medium text-gray-700">默认TTL (秒)</label>
                  <input type="number" id="globalCacheTtl" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" value="3600">
                </div>

                <div>
                  <label for="globalMaxCacheSize" class="block text-sm font-medium text-gray-700">最大缓存大小 (MB)</label>
                  <input type="number" id="globalMaxCacheSize" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" value="1000">
                </div>

                <div class="flex items-center">
                  <input type="checkbox" id="globalEnableSemanticCache" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <label for="globalEnableSemanticCache" class="ml-2 block text-sm text-gray-900">启用语义缓存</label>
                </div>

                <div class="pt-4">
                  <button type="button" id="saveGlobalConfigBtn" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>保存全局配置
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- 用户组配置面板 -->
          <div id="groupConfigPanel" class="config-panel hidden">
            <!-- 用户组选择器 -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <label for="groupConfigSelector" class="block text-sm font-medium text-gray-700 mb-2">选择用户组</label>
              <select id="groupConfigSelector" class="form-select w-full">
                <option value="">请选择用户组</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">选择要查看缓存统计和配置缓存设置的用户组</p>
            </div>

            <!-- 用户组缓存统计 -->
            <div id="groupCacheStats" class="mb-6 hidden">
              <h4 class="text-lg font-medium text-gray-900 mb-4">用户组缓存统计</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div class="bg-white p-4 rounded-lg shadow">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <i class="fas fa-tachometer-alt text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-500">命中率</p>
                      <p class="text-lg font-semibold text-gray-900" id="groupHitRate">-</p>
                    </div>
                  </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <i class="fas fa-database text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-500">缓存大小</p>
                      <p class="text-lg font-semibold text-gray-900" id="groupCacheSize">-</p>
                    </div>
                  </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-500">总请求数</p>
                      <p class="text-lg font-semibold text-gray-900" id="groupTotalRequests">-</p>
                    </div>
                  </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-500">平均响应时间</p>
                      <p class="text-lg font-semibold text-gray-900" id="groupAvgResponseTime">-</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          <form id="cacheConfigForm" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="cacheMode" class="block text-sm font-medium text-gray-700">缓存模式</label>
                <select id="cacheMode" class="form-select mt-1">
                  <option value="isolated">隔离模式</option>
                  <option value="shared">共享模式</option>
                  <option value="hybrid">混合模式</option>
                </select>
              </div>
              <div>
                <label for="cacheStrategy" class="block text-sm font-medium text-gray-700">缓存策略</label>
                <select id="cacheStrategy" class="form-select mt-1">
                  <option value="lru">LRU</option>
                  <option value="lfu">LFU</option>
                  <option value="ttl">TTL</option>
                  <option value="adaptive">自适应</option>
                </select>
              </div>
              <div>
                <label for="maxSize" class="block text-sm font-medium text-gray-700">最大大小</label>
                <input type="number" id="maxSize" class="form-input mt-1" min="1" max="10000">
              </div>
              <div>
                <label for="defaultTtl" class="block text-sm font-medium text-gray-700">默认TTL(秒)</label>
                <input type="number" id="defaultTtl" class="form-input mt-1" min="60" max="86400">
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input type="checkbox" id="enableSemanticCache" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用语义缓存</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" id="enableCrossModelCache" class="form-checkbox">
                <span class="ml-2 text-sm text-gray-700">启用跨模型缓存</span>
              </label>
            </div>
            <div class="flex space-x-4">
              <button type="submit" class="btn-primary">
                <i class="fas fa-save mr-2"></i>保存配置
              </button>
              <button type="button" id="clearCacheBtn" class="btn-danger">
                <i class="fas fa-trash mr-2"></i>清空缓存
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 缓存预热区域 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            <i class="fas fa-fire text-orange-600 mr-2"></i>
            缓存预热
          </h3>
          <div class="space-y-4">
            <div>
              <label for="warmupRequests" class="block text-sm font-medium text-gray-700">预热请求</label>
              <textarea id="warmupRequests" rows="6" class="form-textarea mt-1" 
                placeholder='[{"model": "gpt-4", "messages": [{"role": "user", "content": "Hello"}]}]'></textarea>
              <p class="mt-1 text-sm text-gray-500">输入JSON格式的请求数组</p>
            </div>
            <div class="flex space-x-4">
              <button id="startWarmupBtn" class="btn-primary">
                <i class="fas fa-play mr-2"></i>开始预热
              </button>
              <button id="stopWarmupBtn" class="btn-secondary" disabled>
                <i class="fas fa-stop mr-2"></i>停止预热
              </button>
            </div>
            <div id="warmupProgress" class="hidden">
              <div class="bg-gray-200 rounded-full h-2">
                <div id="warmupProgressBar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
              </div>
              <p class="text-sm text-gray-600 mt-1" id="warmupStatus">预热进行中...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 监控数据重定向提示 -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-600 text-xl"></i>
          </div>
          <div class="ml-3">
            <h3 class="text-lg leading-6 font-medium text-blue-900 mb-2">
              查看缓存性能监控
            </h3>
            <p class="text-blue-800 mb-4">
              缓存性能图表和详细监控数据已整合到统一监控中心，避免功能重复。
            </p>
            <div class="flex space-x-3">
              <a href="/web/monitoring"
                 class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                <i class="fas fa-chart-line"></i>
                查看监控大屏
              </a>
              <a href="/web/analytics"
                 class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                <i class="fas fa-chart-bar"></i>
                查看分析报告
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 通知容器 -->
<div id="notificationContainer" class="fixed top-4 right-4 z-50 space-y-2">
  <!-- 通知将在这里动态添加 -->
</div>

<script src="/static/js/cache_management.js"></script>
{% endblock %}
