"""
安全管理API路由
提供用户组安全管理的REST API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

from app.core.security import SecurityService
from app.handler.error_handler import handle_route_errors
from app.log.logger import get_security_logger
from app.service.security.enhanced_security_service import (
    enhanced_security_service, 
    SecurityLevel, 
    ThreatType
)

# 创建安全路由
router = APIRouter(prefix="/security", tags=["security"])
logger = get_security_logger()

security_service = SecurityService()


# ==================== Pydantic模型 ====================

class SecurityConfigUpdate(BaseModel):
    """安全配置更新请求模型"""
    enable_ip_whitelist: Optional[bool] = None
    enable_rate_limiting: Optional[bool] = None
    enable_anomaly_detection: Optional[bool] = None
    enable_audit_logging: Optional[bool] = None
    security_level: Optional[str] = None  # low, medium, high, critical
    auto_block_suspicious_ips: Optional[bool] = None
    key_rotation_interval_days: Optional[int] = None
    rate_limit: Optional[Dict[str, int]] = None


class IPManagementRequest(BaseModel):
    """IP管理请求模型"""
    ip_address: str
    reason: Optional[str] = ""


class SecurityEventQuery(BaseModel):
    """安全事件查询模型"""
    severity: Optional[str] = None
    event_type: Optional[str] = None
    limit: int = 100


# ==================== 权限验证 ====================

async def verify_security_permission():
    """验证安全管理权限"""
    # 这里应该实现真正的安全管理权限验证
    # 目前使用简化的验证逻辑
    return True


# ==================== 安全配置API ====================

@router.get("/groups/{group_id}/config")
async def get_group_security_config(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """获取用户组安全配置"""
    operation_name = f"get_group_security_config_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting security config for group: {group_id}")
        
        config = await enhanced_security_service.get_group_security_config(group_id)
        
        # 转换为可序列化的格式
        serializable_config = {}
        for key, value in config.items():
            if hasattr(value, 'value'):  # 枚举类型
                serializable_config[key] = value.value
            elif hasattr(value, '__dict__'):  # 数据类
                serializable_config[key] = value.__dict__
            else:
                serializable_config[key] = value
        
        return {
            "group_id": group_id,
            "config": serializable_config
        }


@router.put("/groups/{group_id}/config")
async def update_group_security_config(
    group_id: str,
    request: SecurityConfigUpdate,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """更新用户组安全配置"""
    operation_name = f"update_group_security_config_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Updating security config for group: {group_id}")
        
        # 验证枚举值
        config_updates = request.dict(exclude_unset=True)
        
        if "security_level" in config_updates:
            try:
                SecurityLevel(config_updates["security_level"])
            except ValueError:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Invalid security_level: {config_updates['security_level']}"
                )
        
        success = await enhanced_security_service.update_security_config(group_id, config_updates)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update security config")
        
        return {
            "group_id": group_id,
            "status": "updated",
            "config_updates": config_updates,
            "updated_at": "2025-01-21T04:30:00Z"
        }


# ==================== IP管理API ====================

@router.post("/groups/{group_id}/whitelist")
async def add_ip_to_whitelist(
    group_id: str,
    request: IPManagementRequest,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """添加IP到白名单"""
    operation_name = f"add_ip_to_whitelist_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Adding IP {request.ip_address} to whitelist for group {group_id}")
        
        success = await enhanced_security_service.add_ip_to_whitelist(
            group_id, request.ip_address, request.reason
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to add IP to whitelist")
        
        return {
            "group_id": group_id,
            "ip_address": request.ip_address,
            "status": "added_to_whitelist",
            "reason": request.reason
        }


@router.post("/groups/{group_id}/blacklist")
async def add_ip_to_blacklist(
    group_id: str,
    request: IPManagementRequest,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """添加IP到黑名单"""
    operation_name = f"add_ip_to_blacklist_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Adding IP {request.ip_address} to blacklist for group {group_id}")
        
        success = await enhanced_security_service.add_ip_to_blacklist(
            group_id, request.ip_address, request.reason
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to add IP to blacklist")
        
        return {
            "group_id": group_id,
            "ip_address": request.ip_address,
            "status": "added_to_blacklist",
            "reason": request.reason
        }


@router.delete("/groups/{group_id}/blacklist/{ip_address}")
async def remove_ip_from_blacklist(
    group_id: str,
    ip_address: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """从黑名单移除IP"""
    operation_name = f"remove_ip_from_blacklist_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Removing IP {ip_address} from blacklist for group {group_id}")
        
        success = await enhanced_security_service.remove_ip_from_blacklist(group_id, ip_address)
        
        if not success:
            raise HTTPException(status_code=404, detail="IP not found in blacklist")
        
        return {
            "group_id": group_id,
            "ip_address": ip_address,
            "status": "removed_from_blacklist"
        }


# ==================== 安全事件API ====================

@router.get("/groups/{group_id}/events")
async def get_group_security_events(
    group_id: str,
    severity: Optional[str] = Query(None, description="Filter by severity"),
    limit: int = Query(100, ge=1, le=1000, description="Number of events to return"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """获取用户组安全事件"""
    operation_name = f"get_group_security_events_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting security events for group {group_id}")
        
        # 验证严重级别
        severity_filter = None
        if severity:
            try:
                severity_filter = SecurityLevel(severity)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid severity: {severity}")
        
        events = await enhanced_security_service.get_security_events(
            group_id, severity_filter, limit
        )
        
        # 转换为可序列化的格式
        serializable_events = []
        for event in events:
            serializable_events.append({
                "event_id": event.event_id,
                "group_id": event.group_id,
                "event_type": event.event_type.value,
                "severity": event.severity.value,
                "source_ip": event.source_ip,
                "user_agent": event.user_agent,
                "timestamp": event.timestamp.isoformat(),
                "details": event.details,
                "resolved": event.resolved
            })
        
        return {
            "group_id": group_id,
            "events": serializable_events,
            "total": len(serializable_events),
            "limit": limit
        }


@router.get("/events/global")
async def get_global_security_events(
    severity: Optional[str] = Query(None, description="Filter by severity"),
    limit: int = Query(100, ge=1, le=1000, description="Number of events to return"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """获取全局安全事件"""
    operation_name = "get_global_security_events"
    async with handle_route_errors(logger, operation_name):
        logger.info("Getting global security events")
        
        # 验证严重级别
        severity_filter = None
        if severity:
            try:
                severity_filter = SecurityLevel(severity)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid severity: {severity}")
        
        events = await enhanced_security_service.get_security_events(
            None, severity_filter, limit
        )
        
        # 转换为可序列化的格式
        serializable_events = []
        for event in events:
            serializable_events.append({
                "event_id": event.event_id,
                "group_id": event.group_id,
                "event_type": event.event_type.value,
                "severity": event.severity.value,
                "source_ip": event.source_ip,
                "user_agent": event.user_agent,
                "timestamp": event.timestamp.isoformat(),
                "details": event.details,
                "resolved": event.resolved
            })
        
        return {
            "events": serializable_events,
            "total": len(serializable_events),
            "limit": limit
        }


# ==================== 安全统计API ====================

@router.get("/groups/{group_id}/statistics")
async def get_group_security_statistics(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """获取用户组安全统计"""
    operation_name = f"get_group_security_statistics_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting security statistics for group: {group_id}")
        
        stats = await enhanced_security_service.get_security_statistics(group_id)
        return stats


# ==================== API密钥管理API ====================

@router.get("/groups/{group_id}/key-rotation-plan")
async def get_key_rotation_plan(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_security_permission)
):
    """获取API密钥轮换计划"""
    operation_name = f"get_key_rotation_plan_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting key rotation plan for group: {group_id}")
        
        plan = await enhanced_security_service.generate_api_key_rotation_plan(group_id)
        return plan


# ==================== 安全健康检查API ====================

@router.get("/health")
async def security_health_check():
    """安全系统健康检查"""
    try:
        # 检查安全服务状态
        test_config = await enhanced_security_service.get_group_security_config("health_check")
        
        # 统计安全事件
        recent_events = await enhanced_security_service.get_security_events(limit=10)
        
        # 评估安全状态
        security_status = "healthy"
        issues = []
        
        # 检查最近的高危事件
        critical_events = [
            e for e in recent_events 
            if e.severity in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]
        ]
        
        if len(critical_events) > 5:
            security_status = "warning"
            issues.append("High number of critical security events")
        
        return {
            "status": security_status,
            "service": "gemini-balance-security",
            "version": "1.0.0",
            "timestamp": "2025-01-21T04:30:00Z",
            "components": {
                "enhanced_security_service": "healthy",
                "security_middleware": "healthy",
                "audit_logging": "healthy"
            },
            "recent_events": len(recent_events),
            "critical_events": len(critical_events),
            "issues": issues
        }
        
    except Exception as e:
        logger.error(f"Error in security health check: {str(e)}")
        return {
            "status": "error",
            "service": "gemini-balance-security",
            "error": str(e),
            "timestamp": "2025-01-21T04:30:00Z"
        }


# ==================== 安全信息API ====================

@router.get("/info")
async def security_system_info():
    """安全系统信息"""
    return {
        "service": "Gemini Balance Security System",
        "version": "1.0.0",
        "description": "Enhanced security features for user group protection",
        "features": [
            "User group access control",
            "IP whitelist/blacklist management",
            "Rate limiting",
            "Anomaly detection",
            "Security event logging",
            "Audit trail",
            "API key rotation",
            "Threat response automation"
        ],
        "security_levels": [
            {"level": "low", "description": "Basic security checks"},
            {"level": "medium", "description": "Standard security with monitoring"},
            {"level": "high", "description": "Enhanced security with strict controls"},
            {"level": "critical", "description": "Maximum security with real-time monitoring"}
        ],
        "threat_types": [
            {"type": "rate_limit_exceeded", "description": "Request rate limits exceeded"},
            {"type": "suspicious_ip", "description": "Suspicious IP address activity"},
            {"type": "unusual_pattern", "description": "Unusual usage patterns detected"},
            {"type": "invalid_access", "description": "Invalid access attempts"},
            {"type": "api_key_abuse", "description": "API key abuse detected"}
        ],
        "endpoints": {
            "configuration": "/security/groups/{group_id}/config",
            "ip_management": "/security/groups/{group_id}/whitelist",
            "events": "/security/groups/{group_id}/events",
            "statistics": "/security/groups/{group_id}/statistics",
            "key_rotation": "/security/groups/{group_id}/key-rotation-plan"
        }
    }
