"""
Web界面路由
提供用户组管理和监控的Web界面
"""
from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates

from app.core.security import SecurityService, verify_auth_token
from app.log.logger import get_logger
from app.log.logger import get_openai_logger

# 创建Web路由
router = APIRouter(prefix="/web", tags=["web"])
logger = get_openai_logger()

# 模板引擎
templates = Jinja2Templates(directory="app/templates")
security_service = SecurityService()


@router.get("/user-groups", response_class=HTMLResponse)
async def user_groups_page(request: Request):
    """用户组管理页面"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to user groups page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("User groups page accessed successfully")
        return templates.TemplateResponse("user_groups.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing user groups page: {str(e)}")
        raise


@router.get("/monitoring", response_class=HTMLResponse)
async def monitoring_dashboard_page(request: Request):
    """监控大屏页面"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to monitoring page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("Monitoring page accessed successfully")
        return templates.TemplateResponse("monitoring_dashboard.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing monitoring page: {str(e)}")
        raise

@router.get("/monitoring-dashboard", response_class=HTMLResponse)
async def monitoring_dashboard_page_alt(request: Request):
    """监控大屏页面 - 别名路由"""
    return await monitoring_dashboard_page(request)



@router.get("/settings", response_class=HTMLResponse)
async def system_settings_page(request: Request):
    """系统设置页面"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to system settings page")
            return RedirectResponse(url="/", status_code=302)

        logger.info("System settings page accessed successfully")
        return templates.TemplateResponse("system_settings.html", {"request": request})
    except Exception as e:
        logger.error(f"Error accessing system settings page: {str(e)}")
        raise


# ==================== Web API端点 (使用cookie认证) ====================
# 注意：安全管理和缓存管理功能已移至擴展模塊 (extensions/management)


@router.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    """管理界面首页"""
    try:
        auth_token = request.cookies.get("auth_token")
        if not auth_token or not verify_auth_token(auth_token):
            logger.warning("Unauthorized access attempt to dashboard home")
            return RedirectResponse(url="/", status_code=302)

        # 獲取擴展導航項目
        extension_nav_items = []
        try:
            if hasattr(request.app.state, 'extension_manager'):
                extension_nav_items = request.app.state.extension_manager.get_navigation_items()
        except Exception as e:
            logger.warning(f"獲取擴展導航項目失敗: {str(e)}")

        logger.info("Dashboard home accessed successfully")
        return templates.TemplateResponse("dashboard_home.html", {
            "request": request,
            "extension_nav_items": extension_nav_items
        })
    except Exception as e:
        logger.error(f"Error accessing dashboard home: {str(e)}")
        raise
