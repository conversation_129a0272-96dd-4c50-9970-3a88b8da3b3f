"""
缓存管理API路由
提供用户组缓存管理的REST API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

from app.core.security import SecurityService
from app.handler.error_handler import handle_route_errors
from app.log.logger import get_openai_logger
from app.service.cache.cache_integration_service import cache_integration_service
from app.service.cache.user_group_cache import user_group_cache, CacheMode, CacheStrategy
from app.service.cache.enhanced_cache_service import get_enhanced_cache_service
from app.service.cache.cache_warmup_service import get_cache_warmup_service

# 创建缓存路由
router = APIRouter(prefix="/cache", tags=["cache"])
logger = get_openai_logger()

security_service = SecurityService()


# ==================== Pydantic模型 ====================

class CacheConfigUpdate(BaseModel):
    """缓存配置更新请求模型"""
    cache_mode: Optional[str] = None  # isolated, shared, hybrid
    strategy: Optional[str] = None    # lru, lfu, ttl, adaptive
    max_size: Optional[int] = None
    default_ttl: Optional[int] = None
    enable_semantic_cache: Optional[bool] = None
    enable_cross_model_cache: Optional[bool] = None
    cache_priority: Optional[int] = None


class CacheWarmupRequest(BaseModel):
    """缓存预热请求模型"""
    common_requests: List[Dict[str, Any]]


# ==================== 权限验证 ====================

async def verify_cache_permission():
    """验证缓存管理权限"""
    # 这里应该实现真正的缓存管理权限验证
    # 目前使用简化的验证逻辑
    return True


# ==================== 缓存统计API ====================

@router.get("/groups/{group_id}/statistics")
async def get_group_cache_statistics(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """获取用户组缓存统计"""
    operation_name = f"get_group_cache_statistics_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting cache statistics for group: {group_id}")
        
        stats = await cache_integration_service.get_cache_statistics(group_id)
        return stats


@router.get("/statistics/global")
async def get_global_cache_statistics(
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """获取全局缓存统计"""
    operation_name = "get_global_cache_statistics"
    async with handle_route_errors(logger, operation_name):
        logger.info("Getting global cache statistics")

        stats = await cache_integration_service.get_cache_statistics()
        return stats

@router.get("/config/global")
async def get_global_cache_config(
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """获取全局缓存配置"""
    operation_name = "get_global_cache_config"
    async with handle_route_errors(logger, operation_name):
        logger.info("Getting global cache configuration")

        config = await cache_integration_service.get_global_cache_config()
        return config

@router.put("/config/global")
async def update_global_cache_config(
    config: Dict[str, Any],
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """更新全局缓存配置"""
    operation_name = "update_global_cache_config"
    async with handle_route_errors(logger, operation_name):
        logger.info("Updating global cache configuration")

        result = await cache_integration_service.update_global_cache_config(config)
        return result


# ==================== 缓存管理API ====================

@router.delete("/groups/{group_id}/clear")
async def clear_group_cache(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """清除用户组缓存"""
    operation_name = f"clear_group_cache_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Clearing cache for group: {group_id}")
        
        result = await cache_integration_service.clear_group_cache(group_id)
        return result


@router.put("/groups/{group_id}/config")
async def update_group_cache_config(
    group_id: str,
    request: CacheConfigUpdate,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """更新用户组缓存配置"""
    operation_name = f"update_group_cache_config_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Updating cache config for group: {group_id}")
        
        # 验证枚举值
        config_updates = request.dict(exclude_unset=True)
        
        if "cache_mode" in config_updates:
            try:
                CacheMode(config_updates["cache_mode"])
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid cache_mode: {config_updates['cache_mode']}")
        
        if "strategy" in config_updates:
            try:
                CacheStrategy(config_updates["strategy"])
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid strategy: {config_updates['strategy']}")
        
        success = await cache_integration_service.update_cache_config(group_id, config_updates)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update cache config")
        
        return {
            "group_id": group_id,
            "status": "updated",
            "config_updates": config_updates,
            "updated_at": "2025-01-21T04:00:00Z"
        }


@router.get("/groups/{group_id}/config")
async def get_group_cache_config(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """获取用户组缓存配置"""
    operation_name = f"get_group_cache_config_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting cache config for group: {group_id}")
        
        config = await user_group_cache.get_group_config(group_id)
        
        return {
            "group_id": config.group_id,
            "cache_mode": config.cache_mode.value,
            "strategy": config.strategy.value,
            "max_size": config.max_size,
            "default_ttl": config.default_ttl,
            "enable_semantic_cache": config.enable_semantic_cache,
            "enable_cross_model_cache": config.enable_cross_model_cache,
            "cache_priority": config.cache_priority
        }


# ==================== 缓存优化API ====================

@router.post("/groups/{group_id}/warmup")
async def warmup_group_cache(
    group_id: str,
    request: CacheWarmupRequest,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """预热用户组缓存"""
    operation_name = f"warmup_group_cache_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Warming up cache for group {group_id} with {len(request.common_requests)} requests")
        
        result = await cache_integration_service.warm_up_cache(group_id, request.common_requests)
        return JSONResponse(content=result, status_code=200)


@router.get("/groups/{group_id}/analysis")
async def analyze_group_cache_performance(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """分析用户组缓存性能"""
    operation_name = f"analyze_group_cache_performance_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Analyzing cache performance for group: {group_id}")
        
        analysis = await cache_integration_service.analyze_cache_performance(group_id)
        return analysis


@router.get("/analysis/global")
async def analyze_global_cache_performance(
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_cache_permission)
):
    """分析全局缓存性能"""
    operation_name = "analyze_global_cache_performance"
    async with handle_route_errors(logger, operation_name):
        logger.info("Analyzing global cache performance")
        
        analysis = await cache_integration_service.analyze_cache_performance()
        return analysis


# ==================== 缓存健康检查API ====================

@router.get("/health")
async def cache_health_check():
    """缓存系统健康检查"""
    try:
        # 检查用户组缓存
        ug_stats = await user_group_cache.get_global_cache_stats()
        
        # 检查智能缓存
        ic_stats = cache_integration_service.intelligent_cache.get_stats()
        
        # 评估健康状态
        health_status = "healthy"
        issues = []
        
        # 检查缓存利用率
        ug_utilization = ug_stats.get("utilization", 0)
        if ug_utilization > 95:
            health_status = "warning"
            issues.append("User group cache utilization is very high")
        
        # 检查命中率
        ug_hit_rate = ug_stats.get("global_hit_rate", 0)
        if ug_hit_rate < 30:
            health_status = "warning"
            issues.append("Cache hit rate is low")
        
        return {
            "status": health_status,
            "service": "gemini-balance-cache",
            "version": "1.0.0",
            "timestamp": "2025-01-21T04:00:00Z",
            "components": {
                "user_group_cache": {
                    "status": "healthy",
                    "utilization": f"{ug_utilization}%",
                    "hit_rate": f"{ug_hit_rate}%"
                },
                "intelligent_cache": {
                    "status": "healthy",
                    "hits": ic_stats.get("hits", 0),
                    "misses": ic_stats.get("misses", 0)
                }
            },
            "issues": issues
        }
        
    except Exception as e:
        logger.error(f"Error in cache health check: {str(e)}")
        return {
            "status": "error",
            "service": "gemini-balance-cache",
            "error": str(e),
            "timestamp": "2025-01-21T04:00:00Z"
        }


# ==================== 缓存信息API ====================

@router.get("/info")
async def cache_system_info():
    """缓存系统信息"""
    return {
        "service": "Gemini Balance Cache System",
        "version": "1.0.0",
        "description": "User group level caching with intelligent fallback",
        "features": [
            "User group cache isolation",
            "Shared cache mode",
            "Hybrid cache mode",
            "Semantic similarity caching",
            "Cross-model caching",
            "Multiple eviction strategies",
            "Cache performance analysis",
            "Cache warmup",
            "Real-time statistics"
        ],
        "cache_modes": [
            {"mode": "isolated", "description": "Complete isolation between user groups"},
            {"mode": "shared", "description": "Shared cache across all user groups"},
            {"mode": "hybrid", "description": "Isolated with limited sharing during fallback"}
        ],
        "cache_strategies": [
            {"strategy": "lru", "description": "Least Recently Used"},
            {"strategy": "lfu", "description": "Least Frequently Used"},
            {"strategy": "ttl", "description": "Time To Live based"},
            {"strategy": "adaptive", "description": "Adaptive strategy based on usage patterns"}
        ],
        "endpoints": {
            "statistics": "/cache/groups/{group_id}/statistics",
            "management": "/cache/groups/{group_id}/clear",
            "configuration": "/cache/groups/{group_id}/config",
            "optimization": "/cache/groups/{group_id}/warmup",
            "analysis": "/cache/groups/{group_id}/analysis",
            "enhanced": "/cache/enhanced/analytics",
            "warmup": "/cache/warmup/start",
            "predictive": "/cache/predictive/optimize"
        }
    }


# ==================== 增强缓存API ====================

@router.get("/enhanced/analytics", summary="获取增强缓存分析")
async def get_enhanced_cache_analytics():
    """
    获取增强缓存的分析数据

    Returns:
        Dict[str, Any]: 增强缓存分析数据
    """
    try:
        enhanced_cache = await get_enhanced_cache_service()
        analytics = enhanced_cache.get_cache_analytics()

        return {
            "status": "success",
            "analytics": analytics,
            "timestamp": "2025-01-21T04:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to get enhanced cache analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")


@router.post("/warmup/start", summary="启动缓存预热")
async def start_cache_warmup(
    schedule_type: str = Query("startup", description="预热类型: startup, hourly, daily")
):
    """
    启动缓存预热

    Args:
        schedule_type: 预热类型

    Returns:
        Dict[str, Any]: 预热结果
    """
    try:
        warmup_service = get_cache_warmup_service()
        result = await warmup_service.start_warmup(schedule_type)

        return {
            "status": "success",
            "warmup_result": result,
            "timestamp": "2025-01-21T04:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to start cache warmup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start warmup: {str(e)}")


@router.get("/warmup/status", summary="获取缓存预热状态")
async def get_cache_warmup_status():
    """
    获取缓存预热状态

    Returns:
        Dict[str, Any]: 预热状态信息
    """
    try:
        warmup_service = get_cache_warmup_service()
        status = warmup_service.get_warmup_status()

        return {
            "status": "success",
            "warmup_status": status,
            "timestamp": "2025-01-21T04:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to get warmup status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get warmup status: {str(e)}")


@router.post("/predictive/optimize/{group_id}", summary="优化用户组缓存策略")
async def optimize_user_group_cache(group_id: str):
    """
    优化指定用户组的缓存策略

    Args:
        group_id: 用户组ID

    Returns:
        Dict[str, Any]: 优化建议
    """
    try:
        enhanced_cache = await get_enhanced_cache_service()
        optimization = await enhanced_cache.optimize_cache_strategy(group_id)

        return {
            "status": "success",
            "optimization": optimization,
            "timestamp": "2025-01-21T04:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to optimize cache for group {group_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to optimize cache: {str(e)}")


@router.post("/predictive/preload/{group_id}", summary="预测性预加载缓存")
async def predictive_preload_cache(
    group_id: str,
    cache_keys: List[str] = Query(..., description="要预加载的缓存键列表")
):
    """
    为用户组预测性预加载缓存

    Args:
        group_id: 用户组ID
        cache_keys: 缓存键列表

    Returns:
        Dict[str, Any]: 预加载结果
    """
    try:
        enhanced_cache = await get_enhanced_cache_service()

        # 这里需要根据实际业务逻辑实现预加载
        # 暂时返回模拟结果
        preload_results = []
        for cache_key in cache_keys:
            # 模拟预加载逻辑
            preload_results.append({
                "cache_key": cache_key,
                "status": "scheduled",
                "estimated_completion": "2025-01-21T04:05:00Z"
            })

        return {
            "status": "success",
            "group_id": group_id,
            "preload_results": preload_results,
            "total_scheduled": len(cache_keys),
            "timestamp": "2025-01-21T04:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to preload cache for group {group_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to preload cache: {str(e)}")


@router.get("/hit-rate/analysis", summary="缓存命中率分析")
async def get_cache_hit_rate_analysis(
    time_range: str = Query("24h", description="时间范围: 1h, 24h, 7d, 30d")
):
    """
    获取缓存命中率分析

    Args:
        time_range: 分析时间范围

    Returns:
        Dict[str, Any]: 命中率分析数据
    """
    try:
        enhanced_cache = await get_enhanced_cache_service()
        analytics = enhanced_cache.get_cache_analytics()

        # 模拟命中率分析数据
        hit_rate_analysis = {
            "time_range": time_range,
            "overall_hit_rate": 75.5,  # 当前命中率
            "target_hit_rate": 80.0,   # 目标命中率
            "improvement_potential": 4.5,
            "trends": [
                {"time": "2025-01-21T00:00:00Z", "hit_rate": 72.1},
                {"time": "2025-01-21T01:00:00Z", "hit_rate": 74.3},
                {"time": "2025-01-21T02:00:00Z", "hit_rate": 75.8},
                {"time": "2025-01-21T03:00:00Z", "hit_rate": 75.5}
            ],
            "recommendations": [
                "启用预测性缓存可提升命中率3-5%",
                "优化TTL策略可减少过期缓存",
                "增加缓存预热可提升冷启动性能"
            ]
        }

        return {
            "status": "success",
            "hit_rate_analysis": hit_rate_analysis,
            "enhanced_analytics": analytics,
            "timestamp": "2025-01-21T04:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to get hit rate analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get hit rate analysis: {str(e)}")
