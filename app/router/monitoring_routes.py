"""
监控API路由
提供用户组监控和告警的REST API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime

from app.core.security import SecurityService
from app.handler.error_handler import handle_route_errors
from app.log.logger import get_openai_logger
from app.service.monitoring.user_group_monitoring import user_group_monitoring_service
from app.service.monitoring.alert_manager import alert_manager, AlertSeverity, MetricType, ComparisonOperator

# 创建监控路由
router = APIRouter(prefix="/monitoring", tags=["monitoring"])
logger = get_openai_logger()

security_service = SecurityService()


# ==================== Pydantic模型 ====================

class AlertRuleCreate(BaseModel):
    """告警规则创建请求模型"""
    rule_id: str
    group_id: str
    rule_name: str
    metric_type: str  # error_rate, quota_utilization, response_time, request_rate, success_rate
    threshold: float
    comparison: str  # gt, lt, gte, lte, eq, ne
    duration_minutes: int
    severity: str  # low, medium, high, critical
    description: Optional[str] = ""
    is_active: bool = True


class AlertRuleUpdate(BaseModel):
    """告警规则更新请求模型"""
    rule_name: Optional[str] = None
    threshold: Optional[float] = None
    comparison: Optional[str] = None
    duration_minutes: Optional[int] = None
    severity: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class AlertAcknowledge(BaseModel):
    """告警确认请求模型"""
    acknowledged_by: str


# ==================== 权限验证 ====================

async def verify_monitoring_permission():
    """验证监控权限"""
    # 这里应该实现真正的监控权限验证
    # 目前使用简化的验证逻辑
    return True


# ==================== 实时监控API ====================

@router.get("/groups/{group_id}/metrics/realtime")
async def get_group_realtime_metrics(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取用户组实时监控指标"""
    operation_name = f"get_group_realtime_metrics_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting realtime metrics for group: {group_id}")
        
        metrics = await user_group_monitoring_service.get_group_real_time_metrics(group_id)
        return metrics


@router.get("/groups/{group_id}/metrics/historical")
async def get_group_historical_metrics(
    group_id: str,
    period: str = Query("24h", description="Time period (e.g., 1h, 24h, 7d)"),
    interval: str = Query("1h", description="Data interval (e.g., 1m, 1h)"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取用户组历史监控数据"""
    operation_name = f"get_group_historical_metrics_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting historical metrics for group {group_id}: period={period}, interval={interval}")
        
        metrics = await user_group_monitoring_service.get_group_historical_metrics(
            group_id, period, interval
        )
        return metrics


@router.get("/groups/{group_id}/metrics/performance")
async def get_group_performance_stats(
    group_id: str,
    period: str = Query("24h", description="Time period (e.g., 1h, 24h, 7d)"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取用户组性能统计"""
    operation_name = f"get_group_performance_stats_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting performance stats for group {group_id}: period={period}")
        
        stats = await user_group_monitoring_service.get_group_performance_stats(group_id, period)
        return stats


# ==================== 告警规则管理API ====================

@router.post("/groups/{group_id}/alert-rules")
async def create_alert_rule(
    group_id: str,
    request: AlertRuleCreate,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """创建告警规则"""
    operation_name = f"create_alert_rule_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Creating alert rule for group {group_id}: {request.rule_name}")
        
        # 验证枚举值
        try:
            MetricType(request.metric_type)
            ComparisonOperator(request.comparison)
            AlertSeverity(request.severity)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid enum value: {str(e)}")
        
        # 确保group_id一致
        rule_data = request.dict()
        rule_data["group_id"] = group_id
        
        rule = await alert_manager.create_alert_rule(rule_data)
        
        return {
            "rule_id": rule.rule_id,
            "group_id": rule.group_id,
            "rule_name": rule.rule_name,
            "status": "created",
            "created_at": rule.created_at.isoformat()
        }


@router.get("/groups/{group_id}/alert-rules")
async def get_group_alert_rules(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取用户组告警规则列表"""
    operation_name = f"get_group_alert_rules_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting alert rules for group: {group_id}")
        
        rules = await alert_manager.get_alert_rules(group_id)
        
        return {
            "group_id": group_id,
            "rules": [
                {
                    "rule_id": rule.rule_id,
                    "rule_name": rule.rule_name,
                    "metric_type": rule.metric_type.value,
                    "threshold": rule.threshold,
                    "comparison": rule.comparison.value,
                    "duration_minutes": rule.duration_minutes,
                    "severity": rule.severity.value,
                    "is_active": rule.is_active,
                    "description": rule.description,
                    "created_at": rule.created_at.isoformat(),
                    "updated_at": rule.updated_at.isoformat()
                }
                for rule in rules
            ],
            "total": len(rules)
        }


@router.put("/alert-rules/{rule_id}")
async def update_alert_rule(
    rule_id: str,
    request: AlertRuleUpdate,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """更新告警规则"""
    operation_name = f"update_alert_rule_{rule_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Updating alert rule: {rule_id}")
        
        # 验证枚举值
        updates = request.dict(exclude_unset=True)
        if "comparison" in updates:
            try:
                ComparisonOperator(updates["comparison"])
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid comparison operator: {updates['comparison']}")
        
        if "severity" in updates:
            try:
                AlertSeverity(updates["severity"])
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid severity: {updates['severity']}")
        
        rule = await alert_manager.update_alert_rule(rule_id, updates)
        
        if not rule:
            raise HTTPException(status_code=404, detail=f"Alert rule '{rule_id}' not found")
        
        return {
            "rule_id": rule.rule_id,
            "status": "updated",
            "updated_at": rule.updated_at.isoformat(),
            "changes": updates
        }


@router.delete("/alert-rules/{rule_id}")
async def delete_alert_rule(
    rule_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """删除告警规则"""
    operation_name = f"delete_alert_rule_{rule_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Deleting alert rule: {rule_id}")
        
        success = await alert_manager.delete_alert_rule(rule_id)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Alert rule '{rule_id}' not found")
        
        return {
            "rule_id": rule_id,
            "status": "deleted",
            "deleted_at": datetime.now().isoformat()
        }


# ==================== 告警管理API ====================

@router.get("/groups/{group_id}/alerts/active")
async def get_group_active_alerts(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取用户组活跃告警"""
    operation_name = f"get_group_active_alerts_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting active alerts for group: {group_id}")
        
        alerts = await alert_manager.get_active_alerts(group_id)
        
        return {
            "group_id": group_id,
            "alerts": [
                {
                    "alert_id": alert.alert_id,
                    "rule_id": alert.rule_id,
                    "metric_type": alert.metric_type.value,
                    "current_value": alert.current_value,
                    "threshold": alert.threshold,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "message": alert.message,
                    "triggered_at": alert.triggered_at.isoformat(),
                    "acknowledged_at": alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                    "acknowledged_by": alert.acknowledged_by
                }
                for alert in alerts
            ],
            "total": len(alerts)
        }


@router.get("/groups/{group_id}/alerts/history")
async def get_group_alert_history(
    group_id: str,
    limit: int = Query(100, ge=1, le=1000, description="Number of alerts to return"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取用户组告警历史"""
    operation_name = f"get_group_alert_history_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting alert history for group {group_id}: limit={limit}")
        
        alerts = await alert_manager.get_alert_history(group_id, limit)
        
        return {
            "group_id": group_id,
            "alerts": [
                {
                    "alert_id": alert.alert_id,
                    "rule_id": alert.rule_id,
                    "metric_type": alert.metric_type.value,
                    "current_value": alert.current_value,
                    "threshold": alert.threshold,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "message": alert.message,
                    "triggered_at": alert.triggered_at.isoformat(),
                    "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None,
                    "acknowledged_at": alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                    "acknowledged_by": alert.acknowledged_by
                }
                for alert in alerts
            ],
            "total": len(alerts),
            "limit": limit
        }


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: str,
    request: AlertAcknowledge,
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """确认告警"""
    operation_name = f"acknowledge_alert_{alert_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Acknowledging alert {alert_id} by {request.acknowledged_by}")
        
        success = await alert_manager.acknowledge_alert(alert_id, request.acknowledged_by)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Alert '{alert_id}' not found")
        
        return {
            "alert_id": alert_id,
            "status": "acknowledged",
            "acknowledged_by": request.acknowledged_by,
            "acknowledged_at": datetime.now().isoformat()
        }


# ==================== 系统监控API ====================

@router.get("/system/overview")
async def get_system_monitoring_overview(
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取系统监控概览"""
    operation_name = "get_system_monitoring_overview"
    async with handle_route_errors(logger, operation_name):
        logger.info("Getting system monitoring overview")
        
        # 获取所有活跃告警
        all_active_alerts = await alert_manager.get_active_alerts()
        
        # 按严重级别分组
        alerts_by_severity = {}
        for alert in all_active_alerts:
            severity = alert.severity.value
            alerts_by_severity[severity] = alerts_by_severity.get(severity, 0) + 1
        
        return {
            "system_status": "healthy" if len(all_active_alerts) == 0 else "warning",
            "total_active_alerts": len(all_active_alerts),
            "alerts_by_severity": alerts_by_severity,
            "monitoring_services": {
                "user_group_monitoring": "active",
                "alert_manager": "active"
            },
            "last_updated": datetime.now().isoformat()
        }


# ==================== 健康检查API ====================

@router.get("/metrics/requests")
async def get_request_metrics(
    period: str = Query("24h", description="Time period"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取请求指标数据"""
    operation_name = "get_request_metrics"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting request metrics for period: {period}")

        return {
            "data_points": [],
            "period": period,
            "total_requests": 0
        }

@router.get("/metrics/success-rate")
async def get_success_rate_metrics(
    period: str = Query("24h", description="Time period"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取成功率指标数据"""
    operation_name = "get_success_rate_metrics"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting success rate metrics for period: {period}")

        return {
            "data_points": [],
            "period": period,
            "average_success_rate": 0
        }

@router.get("/metrics/models")
async def get_model_metrics(
    period: str = Query("24h", description="Time period"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取模型使用指标数据"""
    operation_name = "get_model_metrics"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting model metrics for period: {period}")

        return {
            "model_usage": {},
            "period": period
        }

@router.get("/metrics/response-time")
async def get_response_time_metrics(
    period: str = Query("24h", description="Time period"),
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取响应时间指标数据"""
    operation_name = "get_response_time_metrics"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Getting response time metrics for period: {period}")

        return {
            "data_points": [],
            "period": period,
            "average_response_time": 0
        }

@router.get("/alerts/active")
async def get_active_alerts(
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取活跃告警"""
    operation_name = "get_active_alerts"
    async with handle_route_errors(logger, operation_name):
        logger.info("Getting active alerts")

        return {
            "alerts": []
        }

@router.get("/system/resources")
async def get_system_resources(
    _=Depends(security_service.verify_authorization),
    __=Depends(verify_monitoring_permission)
):
    """获取系统资源使用情况"""
    operation_name = "get_system_resources"
    async with handle_route_errors(logger, operation_name):
        logger.info("Getting system resources")

        return {
            "cpu_usage": 0,
            "memory_usage": 0,
            "cache_hit_rate": 0,
            "db_connections": 0
        }

@router.get("/health")
async def monitoring_health_check():
    """监控服务健康检查"""
    return {
        "status": "healthy",
        "service": "gemini-balance-monitoring",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }
