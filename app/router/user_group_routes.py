"""
用户组API路由
提供用户组专属的OpenAI兼容API端点
支持自定义网址和Cloudflare代理
"""
from fastapi import APIRouter, Depends, HTTPException, Request, Header
from fastapi.responses import StreamingResponse
from typing import Optional

from app.config.config import settings
from app.core.security import SecurityService
from app.domain.openai_models import (
    ChatRequest,
    EmbeddingRequest,
    ImageGenerationRequest,
    TTSRequest,
)
from app.handler.retry_handler import RetryHandler
from app.handler.error_handler import handle_route_errors
from app.log.logger import get_openai_logger
from app.service.chat.openai_chat_service import OpenAIChatService
from app.service.embedding.embedding_service import EmbeddingService
from app.service.image.image_create_service import ImageCreateService
from app.service.tts.tts_service import TTSService
from app.service.key.key_manager import KeyManager, get_key_manager_instance
from app.service.model.model_service import ModelService
from app.database.services import get_user_group
from app.service.quota.user_group_quota_manager import user_group_quota_manager
from app.service.context.context_integration_service import context_integration_service
from app.service.fallback.fallback_manager import fallback_manager

# 创建用户组专属路由
router = APIRouter()
logger = get_openai_logger()

security_service = SecurityService()
model_service = ModelService()
embedding_service = EmbeddingService()
image_create_service = ImageCreateService()
tts_service = TTSService()


async def get_key_manager():
    """获取密钥管理器实例"""
    return await get_key_manager_instance()


async def verify_user_group(group_id: str):
    """验证用户组是否存在且有效"""
    user_group = await get_user_group(group_id)
    if not user_group:
        raise HTTPException(status_code=404, detail=f"User group '{group_id}' not found")
    
    if user_group.get("status") != "ACTIVE":
        raise HTTPException(status_code=403, detail=f"User group '{group_id}' is not active")
    
    return user_group


async def get_user_group_chat_service(
    group_id: str,
    key_manager: KeyManager = Depends(get_key_manager)
):
    """获取用户组专属的聊天服务实例"""
    await verify_user_group(group_id)
    return OpenAIChatService(settings.BASE_URL, key_manager)


# ==================== 用户组专属API端点 ====================

@router.get("/v1/groups/{group_id}/models")
async def list_group_models(
    group_id: str,
    _=Depends(security_service.verify_authorization),
    key_manager: KeyManager = Depends(get_key_manager),
):
    """获取用户组可用的模型列表"""
    operation_name = f"list_group_models_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        user_group = await verify_user_group(group_id)
        
        logger.info(f"Handling models list request for group {group_id}")
        
        # 获取用户组专属密钥或全局密钥
        api_key = await key_manager.get_first_valid_key()
        logger.info(f"Using API key for group {group_id}: {api_key[:8]}...")
        
        # 返回模型列表（可以根据用户组配置过滤）
        return await model_service.get_models(api_key)


@router.post("/v1/groups/{group_id}/chat/completions")
async def create_group_chat_completion(
    group_id: str,
    request: ChatRequest,
    chat_service: OpenAIChatService = Depends(get_user_group_chat_service),
    _=Depends(security_service.verify_authorization),
    conversation_id: Optional[str] = Header(None, alias="X-Conversation-ID"),
):
    """用户组专属的聊天完成API"""
    operation_name = f"create_group_chat_completion_{group_id}"
    async with handle_route_errors(logger, operation_name):
        logger.info(f"Handling chat completion request for group {group_id}")

        # 验证用户组
        user_group = await verify_user_group(group_id)

        # 检查配额
        estimated_tokens = sum(len(str(msg.get("content", ""))) for msg in request.messages) // 4
        quota_check = await user_group_quota_manager.check_request_allowed(
            group_id, request.model, estimated_tokens
        )

        if not quota_check.get("allowed", False):
            raise HTTPException(
                status_code=429,
                detail=f"Quota exceeded: {quota_check.get('reason', 'Unknown')}"
            )

        # 准备上下文（如果提供了conversation_id）
        if conversation_id:
            prepared_messages, context_metadata = await context_integration_service.prepare_chat_context(
                request, group_id, conversation_id
            )
            # 使用准备好的消息更新请求
            request.messages = prepared_messages
            logger.info(f"Context prepared for {group_id}:{conversation_id}, "
                       f"merged {context_metadata.get('merged_message_count', 0)} messages")

        # 应用用户组特定的配置
        if user_group.get("no_fallback_models") and request.model in user_group["no_fallback_models"]:
            logger.info(f"Model {request.model} is in no-fallback list for group {group_id}")

        # 处理聊天请求
        if request.stream:
            return StreamingResponse(
                chat_service.create_chat_completion_stream(request, group_id=group_id),
                media_type="text/plain"
            )
        else:
            response = await chat_service.create_chat_completion(request, group_id=group_id)

            # 保存上下文（如果提供了conversation_id）
            if conversation_id and response:
                # 提取响应消息
                response_content = ""
                if hasattr(response, 'choices') and response.choices:
                    choice = response.choices[0]
                    if hasattr(choice, 'message') and choice.message:
                        response_content = choice.message.content or ""

                if response_content:
                    response_message = {"role": "assistant", "content": response_content}
                    await context_integration_service.save_chat_context(
                        group_id, conversation_id, request.messages,
                        request.model, estimated_tokens, response_message
                    )

            return response


@router.post("/v1/groups/{group_id}/embeddings")
async def create_group_embeddings(
    group_id: str,
    request: EmbeddingRequest,
    _=Depends(security_service.verify_authorization),
    key_manager: KeyManager = Depends(get_key_manager),
):
    """用户组专属的嵌入API"""
    operation_name = f"create_group_embeddings_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)

        logger.info(f"Handling embeddings request for group {group_id}")

        # 检查配额
        estimated_tokens = len(str(request.input)) // 4 if hasattr(request, 'input') else 100
        quota_check = await user_group_quota_manager.check_request_allowed(
            group_id, request.model, estimated_tokens
        )

        if not quota_check.get("allowed", False):
            raise HTTPException(
                status_code=429,
                detail=f"Quota exceeded: {quota_check.get('reason', 'Unknown')}"
            )

        # 获取用户组专属密钥
        api_key = await key_manager.get_next_working_key()

        # 创建嵌入
        return await embedding_service.create_embeddings(request, api_key, group_id=group_id)


@router.post("/v1/groups/{group_id}/images/generations")
async def create_group_image(
    group_id: str,
    request: ImageGenerationRequest,
    _=Depends(security_service.verify_authorization),
    key_manager: KeyManager = Depends(get_key_manager),
):
    """用户组专属的图像生成API"""
    operation_name = f"create_group_image_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)
        
        logger.info(f"Handling image generation request for group {group_id}")
        
        # 获取用户组专属密钥
        api_key = await key_manager.get_next_working_key()
        
        # 生成图像
        return await image_create_service.create_image(request, api_key, group_id=group_id)


@router.post("/v1/groups/{group_id}/audio/speech")
async def create_group_speech(
    group_id: str,
    request: TTSRequest,
    _=Depends(security_service.verify_authorization),
    key_manager: KeyManager = Depends(get_key_manager),
):
    """用户组专属的文本转语音API"""
    operation_name = f"create_group_speech_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)
        
        logger.info(f"Handling TTS request for group {group_id}")
        
        # 获取用户组专属密钥
        api_key = await key_manager.get_next_working_key()
        
        # 生成语音
        return await tts_service.create_speech(request, api_key, group_id=group_id)


# ==================== 用户组管理端点 ====================

@router.get("/v1/groups/{group_id}/quota/status")
async def get_group_quota_status(
    group_id: str,
    _=Depends(security_service.verify_authorization),
):
    """获取用户组配额状态"""
    operation_name = f"get_group_quota_status_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)

        logger.info(f"Getting quota status for group {group_id}")

        # 获取配额状态
        quota_status = await user_group_quota_manager.get_group_quota_status(group_id)

        return {
            "group_id": group_id,
            "quota_status": quota_status,
            "timestamp": "2025-01-21T03:20:00Z"
        }


@router.get("/v1/groups/{group_id}/context/conversations")
async def list_group_conversations(
    group_id: str,
    _=Depends(security_service.verify_authorization),
):
    """列出用户组的所有对话"""
    operation_name = f"list_group_conversations_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)

        logger.info(f"Listing conversations for group {group_id}")

        # 获取对话列表
        conversations = await context_integration_service.list_group_conversations(group_id)

        return {
            "group_id": group_id,
            "conversations": conversations,
            "total_count": len(conversations)
        }


@router.delete("/v1/groups/{group_id}/context/conversations/{conversation_id}")
async def clear_conversation_context(
    group_id: str,
    conversation_id: str,
    _=Depends(security_service.verify_authorization),
):
    """清除指定对话的上下文"""
    operation_name = f"clear_conversation_context_{group_id}_{conversation_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)

        logger.info(f"Clearing context for {group_id}:{conversation_id}")

        # 清除上下文
        success = await context_integration_service.clear_conversation_context(group_id, conversation_id)

        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found or already cleared")

        return {
            "group_id": group_id,
            "conversation_id": conversation_id,
            "status": "cleared"
        }


@router.get("/v1/groups/{group_id}/context/conversations/{conversation_id}/summary")
async def get_conversation_summary(
    group_id: str,
    conversation_id: str,
    _=Depends(security_service.verify_authorization),
):
    """获取对话摘要"""
    operation_name = f"get_conversation_summary_{group_id}_{conversation_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)

        logger.info(f"Getting summary for {group_id}:{conversation_id}")

        # 获取对话摘要
        summary = await context_integration_service.get_conversation_summary(group_id, conversation_id)

        if not summary:
            raise HTTPException(status_code=404, detail="Conversation not found")

        return summary


@router.get("/v1/groups/{group_id}/fallback/statistics")
async def get_group_fallback_statistics(
    group_id: str,
    hours: int = 24,
    _=Depends(security_service.verify_authorization),
):
    """获取用户组降级统计"""
    operation_name = f"get_group_fallback_statistics_{group_id}"
    async with handle_route_errors(logger, operation_name):
        # 验证用户组
        await verify_user_group(group_id)

        logger.info(f"Getting fallback statistics for group {group_id}")

        # 获取降级统计
        statistics = await fallback_manager.get_fallback_statistics(group_id, hours)

        return {
            "group_id": group_id,
            "statistics": statistics,
            "time_range_hours": hours
        }


# ==================== 兼容性路由 ====================

# 支持不同的URL格式
@router.get("/groups/{group_id}/v1/models")
async def list_group_models_alt(group_id: str, _=Depends(security_service.verify_authorization)):
    """备用模型列表端点"""
    return await list_group_models(group_id, _)


@router.post("/groups/{group_id}/v1/chat/completions")
async def create_group_chat_completion_alt(
    group_id: str,
    request: ChatRequest,
    chat_service: OpenAIChatService = Depends(get_user_group_chat_service),
    _=Depends(security_service.verify_authorization),
):
    """备用聊天完成端点"""
    return await create_group_chat_completion(group_id, request, chat_service, _)


# ==================== 自定义域名支持 ====================

@router.get("/health")
async def health_check():
    """健康检查端点，用于负载均衡器"""
    return {"status": "healthy", "service": "gemini-balance-user-groups"}


@router.get("/api-info")
async def root_info(request: Request):
    """API信息，显示API使用说明"""
    base_url = str(request.base_url).rstrip('/')
    
    return {
        "service": "Gemini Balance User Groups API",
        "version": "1.0.0",
        "openai_compatible": True,
        "endpoints": {
            "models": f"{base_url}/v1/groups/{{group_id}}/models",
            "chat": f"{base_url}/v1/groups/{{group_id}}/chat/completions",
            "embeddings": f"{base_url}/v1/groups/{{group_id}}/embeddings",
            "images": f"{base_url}/v1/groups/{{group_id}}/images/generations",
            "speech": f"{base_url}/v1/groups/{{group_id}}/audio/speech"
        },
        "usage": {
            "description": "Replace {group_id} with your actual user group ID",
            "example": f"{base_url}/v1/groups/my-group/chat/completions",
            "custom_domain": "You can use this API with any custom domain or Cloudflare proxy"
        }
    }
