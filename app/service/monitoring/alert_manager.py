"""
告警管理器
提供用户组级别的告警规则管理和告警触发功能
"""
import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from app.service.monitoring.user_group_monitoring import user_group_monitoring_service
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class AlertSeverity(Enum):
    """告警严重级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class MetricType(Enum):
    """监控指标类型"""
    ERROR_RATE = "error_rate"
    QUOTA_UTILIZATION = "quota_utilization"
    RESPONSE_TIME = "response_time"
    REQUEST_RATE = "request_rate"
    SUCCESS_RATE = "success_rate"


class ComparisonOperator(Enum):
    """比较操作符"""
    GT = "gt"  # 大于
    LT = "lt"  # 小于
    GTE = "gte"  # 大于等于
    LTE = "lte"  # 小于等于
    EQ = "eq"  # 等于
    NE = "ne"  # 不等于


@dataclass
class AlertRule:
    """告警规则"""
    rule_id: str
    group_id: str
    rule_name: str
    metric_type: MetricType
    threshold: float
    comparison: ComparisonOperator
    duration_minutes: int
    severity: AlertSeverity
    is_active: bool = True
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class Alert:
    """告警实例"""
    alert_id: str
    rule_id: str
    group_id: str
    metric_type: MetricType
    current_value: float
    threshold: float
    severity: AlertSeverity
    status: AlertStatus
    message: str
    triggered_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None


class AlertManager:
    """
    告警管理器
    负责告警规则管理和告警触发
    """
    
    def __init__(self):
        self.monitoring_service = user_group_monitoring_service
        
        # 告警规则存储
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # 活跃告警存储
        self.active_alerts: Dict[str, Alert] = {}
        
        # 告警历史（最近1000条）
        self.alert_history: List[Alert] = []
        
        # 告警回调函数
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # 告警抑制规则
        self.suppression_rules: Dict[str, Dict[str, Any]] = {}
        
        # 默认告警规则
        self._setup_default_rules()
        
        # 启动告警检查任务
        self._alert_check_task = None
        self._initialized = False

    async def _ensure_initialized(self):
        """确保告警管理器已初始化"""
        if not self._initialized:
            self._start_alert_checking()
            self._initialized = True

    def _setup_default_rules(self):
        """设置默认告警规则"""
        default_rules = [
            {
                "rule_id": "default_error_rate",
                "group_id": "*",  # 适用于所有用户组
                "rule_name": "高错误率告警",
                "metric_type": MetricType.ERROR_RATE,
                "threshold": 10.0,  # 错误率超过10%
                "comparison": ComparisonOperator.GT,
                "duration_minutes": 5,
                "severity": AlertSeverity.HIGH,
                "description": "错误率超过10%持续5分钟"
            },
            {
                "rule_id": "default_quota_utilization",
                "group_id": "*",
                "rule_name": "配额使用率告警",
                "metric_type": MetricType.QUOTA_UTILIZATION,
                "threshold": 90.0,  # 配额使用率超过90%
                "comparison": ComparisonOperator.GT,
                "duration_minutes": 1,
                "severity": AlertSeverity.MEDIUM,
                "description": "配额使用率超过90%"
            },
            {
                "rule_id": "default_response_time",
                "group_id": "*",
                "rule_name": "响应时间告警",
                "metric_type": MetricType.RESPONSE_TIME,
                "threshold": 5000.0,  # 响应时间超过5秒
                "comparison": ComparisonOperator.GT,
                "duration_minutes": 3,
                "severity": AlertSeverity.MEDIUM,
                "description": "平均响应时间超过5秒持续3分钟"
            }
        ]
        
        for rule_data in default_rules:
            rule = AlertRule(**rule_data)
            self.alert_rules[rule.rule_id] = rule
    
    def _start_alert_checking(self):
        """启动告警检查任务"""
        try:
            if self._alert_check_task is None or self._alert_check_task.done():
                self._alert_check_task = asyncio.create_task(self._background_alert_checking())
        except RuntimeError:
            # 如果没有运行的事件循环，稍后再启动
            pass
    
    async def _background_alert_checking(self):
        """后台告警检查任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                await self.check_all_alerts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background alert checking: {str(e)}")
    
    async def create_alert_rule(self, rule_data: Dict[str, Any]) -> AlertRule:
        """创建告警规则"""
        try:
            rule = AlertRule(
                rule_id=rule_data["rule_id"],
                group_id=rule_data["group_id"],
                rule_name=rule_data["rule_name"],
                metric_type=MetricType(rule_data["metric_type"]),
                threshold=float(rule_data["threshold"]),
                comparison=ComparisonOperator(rule_data["comparison"]),
                duration_minutes=int(rule_data["duration_minutes"]),
                severity=AlertSeverity(rule_data["severity"]),
                description=rule_data.get("description", ""),
                is_active=rule_data.get("is_active", True)
            )
            
            self.alert_rules[rule.rule_id] = rule
            logger.info(f"Created alert rule: {rule.rule_id} for group {rule.group_id}")
            
            return rule
        except Exception as e:
            logger.error(f"Error creating alert rule: {str(e)}")
            raise
    
    async def update_alert_rule(self, rule_id: str, updates: Dict[str, Any]) -> Optional[AlertRule]:
        """更新告警规则"""
        try:
            if rule_id not in self.alert_rules:
                return None
            
            rule = self.alert_rules[rule_id]
            
            # 更新字段
            if "rule_name" in updates:
                rule.rule_name = updates["rule_name"]
            if "threshold" in updates:
                rule.threshold = float(updates["threshold"])
            if "comparison" in updates:
                rule.comparison = ComparisonOperator(updates["comparison"])
            if "duration_minutes" in updates:
                rule.duration_minutes = int(updates["duration_minutes"])
            if "severity" in updates:
                rule.severity = AlertSeverity(updates["severity"])
            if "is_active" in updates:
                rule.is_active = bool(updates["is_active"])
            if "description" in updates:
                rule.description = updates["description"]
            
            rule.updated_at = datetime.now()
            
            logger.info(f"Updated alert rule: {rule_id}")
            return rule
        except Exception as e:
            logger.error(f"Error updating alert rule {rule_id}: {str(e)}")
            raise
    
    async def delete_alert_rule(self, rule_id: str) -> bool:
        """删除告警规则"""
        try:
            if rule_id in self.alert_rules:
                del self.alert_rules[rule_id]
                logger.info(f"Deleted alert rule: {rule_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting alert rule {rule_id}: {str(e)}")
            return False
    
    async def get_alert_rules(self, group_id: Optional[str] = None) -> List[AlertRule]:
        """获取告警规则列表"""
        try:
            if group_id:
                return [rule for rule in self.alert_rules.values() 
                       if rule.group_id == group_id or rule.group_id == "*"]
            return list(self.alert_rules.values())
        except Exception as e:
            logger.error(f"Error getting alert rules: {str(e)}")
            return []
    
    async def check_all_alerts(self):
        """检查所有告警规则"""
        try:
            # 获取所有活跃的用户组（这里简化为固定列表）
            active_groups = ["test_group", "company_a", "company_b"]  # 实际应该从数据库获取
            
            for group_id in active_groups:
                await self.check_group_alerts(group_id)
        except Exception as e:
            logger.error(f"Error checking all alerts: {str(e)}")
    
    async def check_group_alerts(self, group_id: str):
        """检查特定用户组的告警"""
        try:
            # 获取适用于该用户组的告警规则
            applicable_rules = [rule for rule in self.alert_rules.values() 
                              if (rule.group_id == group_id or rule.group_id == "*") and rule.is_active]
            
            # 获取当前监控指标
            metrics = await self.monitoring_service.get_group_real_time_metrics(group_id)
            
            for rule in applicable_rules:
                await self._evaluate_rule(rule, group_id, metrics)
        except Exception as e:
            logger.error(f"Error checking alerts for group {group_id}: {str(e)}")
    
    async def _evaluate_rule(self, rule: AlertRule, group_id: str, metrics: Dict[str, Any]):
        """评估单个告警规则"""
        try:
            # 提取对应的指标值
            current_value = self._extract_metric_value(rule.metric_type, metrics)
            
            # 检查是否触发告警条件
            is_triggered = self._compare_values(current_value, rule.threshold, rule.comparison)
            
            alert_key = f"{rule.rule_id}_{group_id}"
            
            if is_triggered:
                if alert_key not in self.active_alerts:
                    # 创建新告警
                    alert = Alert(
                        alert_id=f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{rule.rule_id}",
                        rule_id=rule.rule_id,
                        group_id=group_id,
                        metric_type=rule.metric_type,
                        current_value=current_value,
                        threshold=rule.threshold,
                        severity=rule.severity,
                        status=AlertStatus.ACTIVE,
                        message=f"{rule.rule_name}: {rule.metric_type.value} = {current_value:.2f} {rule.comparison.value} {rule.threshold}",
                        triggered_at=datetime.now()
                    )
                    
                    self.active_alerts[alert_key] = alert
                    self.alert_history.append(alert)
                    
                    # 触发告警回调
                    await self._trigger_alert_callbacks(alert)
                    
                    logger.warning(f"Alert triggered: {alert.message} for group {group_id}")
            else:
                if alert_key in self.active_alerts:
                    # 解决告警
                    alert = self.active_alerts[alert_key]
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.now()
                    
                    del self.active_alerts[alert_key]
                    
                    logger.info(f"Alert resolved: {alert.message} for group {group_id}")
        except Exception as e:
            logger.error(f"Error evaluating rule {rule.rule_id}: {str(e)}")
    
    def _extract_metric_value(self, metric_type: MetricType, metrics: Dict[str, Any]) -> float:
        """从监控指标中提取对应的值"""
        try:
            if metric_type == MetricType.ERROR_RATE:
                return metrics.get("performance", {}).get("error_rate", 0.0)
            elif metric_type == MetricType.QUOTA_UTILIZATION:
                return metrics.get("quota", {}).get("utilization_percent", 0.0)
            elif metric_type == MetricType.RESPONSE_TIME:
                return metrics.get("performance", {}).get("avg_response_time_ms", 0.0)
            elif metric_type == MetricType.REQUEST_RATE:
                return float(metrics.get("requests", {}).get("total", 0))
            elif metric_type == MetricType.SUCCESS_RATE:
                return metrics.get("requests", {}).get("success_rate", 0.0)
            else:
                return 0.0
        except Exception as e:
            logger.error(f"Error extracting metric value for {metric_type}: {str(e)}")
            return 0.0
    
    def _compare_values(self, current: float, threshold: float, operator: ComparisonOperator) -> bool:
        """比较值"""
        if operator == ComparisonOperator.GT:
            return current > threshold
        elif operator == ComparisonOperator.LT:
            return current < threshold
        elif operator == ComparisonOperator.GTE:
            return current >= threshold
        elif operator == ComparisonOperator.LTE:
            return current <= threshold
        elif operator == ComparisonOperator.EQ:
            return abs(current - threshold) < 0.001  # 浮点数比较
        elif operator == ComparisonOperator.NE:
            return abs(current - threshold) >= 0.001
        else:
            return False
    
    async def _trigger_alert_callbacks(self, alert: Alert):
        """触发告警回调"""
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {str(e)}")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    async def get_active_alerts(self, group_id: Optional[str] = None) -> List[Alert]:
        """获取活跃告警列表"""
        try:
            if group_id:
                return [alert for alert in self.active_alerts.values() if alert.group_id == group_id]
            return list(self.active_alerts.values())
        except Exception as e:
            logger.error(f"Error getting active alerts: {str(e)}")
            return []
    
    async def get_alert_history(self, group_id: Optional[str] = None, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        try:
            history = self.alert_history
            if group_id:
                history = [alert for alert in history if alert.group_id == group_id]
            
            # 按时间倒序排列，返回最近的记录
            history.sort(key=lambda x: x.triggered_at, reverse=True)
            return history[:limit]
        except Exception as e:
            logger.error(f"Error getting alert history: {str(e)}")
            return []
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """确认告警"""
        try:
            for alert in self.active_alerts.values():
                if alert.alert_id == alert_id:
                    alert.acknowledged_at = datetime.now()
                    alert.acknowledged_by = acknowledged_by
                    logger.info(f"Alert acknowledged: {alert_id} by {acknowledged_by}")
                    return True
            return False
        except Exception as e:
            logger.error(f"Error acknowledging alert {alert_id}: {str(e)}")
            return False


# 全局实例
alert_manager = AlertManager()
