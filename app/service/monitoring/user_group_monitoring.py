"""
用户组监控服务
基于现有StatsService扩展，提供用户组级别的监控和统计功能
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

from sqlalchemy import and_, case, func, or_, select, desc
from app.database.connection import database
from app.database.models import RequestLog
from app.service.stats.stats_service import StatsService
from app.service.quota.user_group_quota_manager import user_group_quota_manager
from app.service.context.context_integration_service import context_integration_service
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


@dataclass
class MonitoringMetrics:
    """监控指标数据结构"""
    group_id: str
    timestamp: datetime
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    error_rate: float
    quota_utilization: float
    active_models: List[str]


@dataclass
class AlertRule:
    """告警规则数据结构"""
    rule_id: str
    group_id: str
    metric_type: str  # error_rate, quota_utilization, response_time
    threshold: float
    comparison: str  # gt, lt, gte, lte
    duration_minutes: int
    is_active: bool


class UserGroupMonitoringService:
    """
    用户组监控服务
    提供实时监控、性能统计和告警功能
    """
    
    def __init__(self):
        self.stats_service = StatsService()
        self.quota_manager = user_group_quota_manager
        self.context_service = context_integration_service
        
        # 内存缓存监控数据
        self.metrics_cache: Dict[str, List[MonitoringMetrics]] = defaultdict(list)
        self.alert_rules: Dict[str, List[AlertRule]] = defaultdict(list)
        
        # 监控配置
        self.cache_duration_minutes = 60  # 缓存1小时的数据
        self.metrics_collection_interval = 60  # 每分钟收集一次指标
        
        # 启动后台监控任务
        self._monitoring_task = None
        self._initialized = False

    async def _ensure_initialized(self):
        """确保监控服务已初始化"""
        if not self._initialized:
            self._start_monitoring()
            self._initialized = True

    def _start_monitoring(self):
        """启动后台监控任务"""
        try:
            if self._monitoring_task is None or self._monitoring_task.done():
                self._monitoring_task = asyncio.create_task(self._background_monitoring())
        except RuntimeError:
            # 如果没有运行的事件循环，稍后再启动
            pass
    
    async def _background_monitoring(self):
        """后台监控任务"""
        while True:
            try:
                await asyncio.sleep(self.metrics_collection_interval)
                await self._collect_all_metrics()
                await self._check_alerts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background monitoring: {str(e)}")
    
    async def get_group_real_time_metrics(self, group_id: str) -> Dict[str, Any]:
        """获取用户组实时监控指标"""
        try:
            # 获取最近1分钟的统计数据
            recent_stats = await self._get_group_stats_in_period(group_id, minutes=1)
            
            # 获取配额使用情况
            quota_status = await self.quota_manager.get_group_quota_status(group_id)
            
            # 计算配额利用率
            quota_utilization = self._calculate_quota_utilization(quota_status)
            
            # 获取活跃模型
            active_models = await self._get_active_models(group_id, minutes=5)
            
            # 获取平均响应时间
            avg_response_time = await self._get_avg_response_time(group_id, minutes=1)
            
            return {
                "group_id": group_id,
                "timestamp": datetime.now().isoformat(),
                "requests": {
                    "total": recent_stats.get("total", 0),
                    "successful": recent_stats.get("success", 0),
                    "failed": recent_stats.get("failure", 0),
                    "success_rate": self._calculate_success_rate(recent_stats)
                },
                "performance": {
                    "avg_response_time_ms": avg_response_time,
                    "error_rate": self._calculate_error_rate(recent_stats)
                },
                "quota": {
                    "utilization_percent": quota_utilization,
                    "status": quota_status
                },
                "models": {
                    "active_models": active_models,
                    "total_active": len(active_models)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting real-time metrics for group {group_id}: {str(e)}")
            return self._get_empty_metrics(group_id)
    
    async def get_group_historical_metrics(
        self, 
        group_id: str, 
        period: str = "24h",
        interval: str = "1h"
    ) -> Dict[str, Any]:
        """获取用户组历史监控数据"""
        try:
            # 解析时间段
            period_minutes = self._parse_period_to_minutes(period)
            interval_minutes = self._parse_period_to_minutes(interval)
            
            # 获取历史数据点
            data_points = []
            current_time = datetime.now()
            
            for i in range(0, period_minutes, interval_minutes):
                start_time = current_time - timedelta(minutes=i + interval_minutes)
                end_time = current_time - timedelta(minutes=i)
                
                stats = await self._get_group_stats_in_time_range(group_id, start_time, end_time)
                quota_util = await self._get_quota_utilization_at_time(group_id, end_time)
                
                data_points.append({
                    "timestamp": end_time.isoformat(),
                    "requests": stats.get("total", 0),
                    "success_rate": self._calculate_success_rate(stats),
                    "error_rate": self._calculate_error_rate(stats),
                    "quota_utilization": quota_util
                })
            
            # 反转数据点，使其按时间顺序排列
            data_points.reverse()
            
            return {
                "group_id": group_id,
                "period": period,
                "interval": interval,
                "data_points": data_points,
                "summary": {
                    "total_requests": sum(dp["requests"] for dp in data_points),
                    "avg_success_rate": sum(dp["success_rate"] for dp in data_points) / len(data_points) if data_points else 0,
                    "avg_error_rate": sum(dp["error_rate"] for dp in data_points) / len(data_points) if data_points else 0,
                    "peak_requests": max(dp["requests"] for dp in data_points) if data_points else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting historical metrics for group {group_id}: {str(e)}")
            return {"group_id": group_id, "error": str(e)}
    
    async def get_group_performance_stats(self, group_id: str, period: str = "24h") -> Dict[str, Any]:
        """获取用户组性能统计"""
        try:
            period_minutes = self._parse_period_to_minutes(period)
            cutoff_time = datetime.now() - timedelta(minutes=period_minutes)
            
            # 查询性能数据
            query = select(
                func.count(RequestLog.id).label("total_requests"),
                func.avg(RequestLog.response_time).label("avg_response_time"),
                func.min(RequestLog.response_time).label("min_response_time"),
                func.max(RequestLog.response_time).label("max_response_time"),
                func.percentile_cont(0.5).within_group(RequestLog.response_time).label("p50_response_time"),
                func.percentile_cont(0.95).within_group(RequestLog.response_time).label("p95_response_time"),
                func.percentile_cont(0.99).within_group(RequestLog.response_time).label("p99_response_time"),
                RequestLog.model_name,
                func.count(RequestLog.id).label("model_requests")
            ).where(
                and_(
                    RequestLog.request_time >= cutoff_time,
                    RequestLog.group_id == group_id
                )
            ).group_by(RequestLog.model_name)
            
            results = await database.fetch_all(query)
            
            # 处理结果
            model_stats = []
            total_requests = 0
            
            for result in results:
                model_stats.append({
                    "model_name": result["model_name"],
                    "requests": result["model_requests"],
                    "avg_response_time": float(result["avg_response_time"] or 0),
                    "min_response_time": float(result["min_response_time"] or 0),
                    "max_response_time": float(result["max_response_time"] or 0),
                    "p50_response_time": float(result["p50_response_time"] or 0),
                    "p95_response_time": float(result["p95_response_time"] or 0),
                    "p99_response_time": float(result["p99_response_time"] or 0)
                })
                total_requests += result["model_requests"]
            
            # 计算整体性能指标
            overall_query = select(
                func.avg(RequestLog.response_time).label("overall_avg"),
                func.percentile_cont(0.95).within_group(RequestLog.response_time).label("overall_p95")
            ).where(
                and_(
                    RequestLog.request_time >= cutoff_time,
                    RequestLog.group_id == group_id
                )
            )
            
            overall_result = await database.fetch_one(overall_query)
            
            return {
                "group_id": group_id,
                "period": period,
                "total_requests": total_requests,
                "overall_performance": {
                    "avg_response_time": float(overall_result["overall_avg"] or 0),
                    "p95_response_time": float(overall_result["overall_p95"] or 0)
                },
                "model_performance": model_stats,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting performance stats for group {group_id}: {str(e)}")
            return {"group_id": group_id, "error": str(e)}

    async def _get_group_stats_in_period(self, group_id: str, minutes: int) -> Dict[str, int]:
        """获取指定时间段内的用户组统计数据"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            query = select(
                func.count(RequestLog.id).label("total"),
                func.sum(
                    case(
                        (
                            and_(
                                RequestLog.status_code >= 200,
                                RequestLog.status_code < 300,
                            ),
                            1,
                        ),
                        else_=0,
                    )
                ).label("success"),
                func.sum(
                    case(
                        (
                            or_(
                                RequestLog.status_code < 200,
                                RequestLog.status_code >= 300,
                            ),
                            1,
                        ),
                        (RequestLog.status_code is None, 1),
                        else_=0,
                    )
                ).label("failure"),
            ).where(
                and_(
                    RequestLog.request_time >= cutoff_time,
                    RequestLog.group_id == group_id
                )
            )

            result = await database.fetch_one(query)
            if result:
                return {
                    "total": result["total"] or 0,
                    "success": result["success"] or 0,
                    "failure": result["failure"] or 0,
                }
            return {"total": 0, "success": 0, "failure": 0}
        except Exception as e:
            logger.error(f"Error getting group stats for {group_id}: {str(e)}")
            return {"total": 0, "success": 0, "failure": 0}

    async def _get_group_stats_in_time_range(self, group_id: str, start_time: datetime, end_time: datetime) -> Dict[str, int]:
        """获取指定时间范围内的用户组统计数据"""
        try:
            query = select(
                func.count(RequestLog.id).label("total"),
                func.sum(
                    case(
                        (
                            and_(
                                RequestLog.status_code >= 200,
                                RequestLog.status_code < 300,
                            ),
                            1,
                        ),
                        else_=0,
                    )
                ).label("success"),
                func.sum(
                    case(
                        (
                            or_(
                                RequestLog.status_code < 200,
                                RequestLog.status_code >= 300,
                            ),
                            1,
                        ),
                        (RequestLog.status_code is None, 1),
                        else_=0,
                    )
                ).label("failure"),
            ).where(
                and_(
                    RequestLog.request_time >= start_time,
                    RequestLog.request_time < end_time,
                    RequestLog.group_id == group_id
                )
            )

            result = await database.fetch_one(query)
            if result:
                return {
                    "total": result["total"] or 0,
                    "success": result["success"] or 0,
                    "failure": result["failure"] or 0,
                }
            return {"total": 0, "success": 0, "failure": 0}
        except Exception as e:
            logger.error(f"Error getting group stats in time range for {group_id}: {str(e)}")
            return {"total": 0, "success": 0, "failure": 0}

    async def _get_active_models(self, group_id: str, minutes: int) -> List[str]:
        """获取活跃模型列表"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            query = select(RequestLog.model_name).distinct().where(
                and_(
                    RequestLog.request_time >= cutoff_time,
                    RequestLog.group_id == group_id,
                    RequestLog.model_name.isnot(None)
                )
            )

            results = await database.fetch_all(query)
            return [result["model_name"] for result in results if result["model_name"]]
        except Exception as e:
            logger.error(f"Error getting active models for {group_id}: {str(e)}")
            return []

    async def _get_avg_response_time(self, group_id: str, minutes: int) -> float:
        """获取平均响应时间"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            query = select(func.avg(RequestLog.response_time)).where(
                and_(
                    RequestLog.request_time >= cutoff_time,
                    RequestLog.group_id == group_id,
                    RequestLog.response_time.isnot(None)
                )
            )

            result = await database.fetch_one(query)
            return float(result[0] or 0) if result and result[0] else 0.0
        except Exception as e:
            logger.error(f"Error getting avg response time for {group_id}: {str(e)}")
            return 0.0

    def _calculate_quota_utilization(self, quota_status: List[Dict[str, Any]]) -> float:
        """计算配额利用率"""
        if not quota_status:
            return 0.0

        total_utilization = 0.0
        count = 0

        for quota in quota_status:
            if isinstance(quota, dict) and "utilization_percent" in quota:
                total_utilization += quota["utilization_percent"]
                count += 1

        return total_utilization / count if count > 0 else 0.0

    async def _get_quota_utilization_at_time(self, group_id: str, timestamp: datetime) -> float:
        """获取指定时间点的配额利用率（简化实现）"""
        # 这里应该从历史数据中获取，目前返回当前利用率
        quota_status = await self.quota_manager.get_group_quota_status(group_id)
        return self._calculate_quota_utilization(quota_status)

    def _calculate_success_rate(self, stats: Dict[str, int]) -> float:
        """计算成功率"""
        total = stats.get("total", 0)
        success = stats.get("success", 0)
        return (success / total * 100) if total > 0 else 0.0

    def _calculate_error_rate(self, stats: Dict[str, int]) -> float:
        """计算错误率"""
        total = stats.get("total", 0)
        failure = stats.get("failure", 0)
        return (failure / total * 100) if total > 0 else 0.0

    def _parse_period_to_minutes(self, period: str) -> int:
        """解析时间段字符串为分钟数"""
        period = period.lower()
        if period.endswith('m'):
            return int(period[:-1])
        elif period.endswith('h'):
            return int(period[:-1]) * 60
        elif period.endswith('d'):
            return int(period[:-1]) * 24 * 60
        else:
            # 默认为小时
            return int(period) * 60

    def _get_empty_metrics(self, group_id: str) -> Dict[str, Any]:
        """获取空的监控指标"""
        return {
            "group_id": group_id,
            "timestamp": datetime.now().isoformat(),
            "requests": {"total": 0, "successful": 0, "failed": 0, "success_rate": 0.0},
            "performance": {"avg_response_time_ms": 0.0, "error_rate": 0.0},
            "quota": {"utilization_percent": 0.0, "status": []},
            "models": {"active_models": [], "total_active": 0}
        }

    async def _collect_all_metrics(self):
        """收集所有用户组的监控指标"""
        try:
            # 这里应该获取所有活跃的用户组列表
            # 目前简化实现，只记录日志
            logger.debug("Collecting metrics for all user groups")
        except Exception as e:
            logger.error(f"Error collecting all metrics: {str(e)}")

    async def _check_alerts(self):
        """检查告警规则"""
        try:
            # 这里应该检查所有告警规则
            # 目前简化实现，只记录日志
            logger.debug("Checking alert rules")
        except Exception as e:
            logger.error(f"Error checking alerts: {str(e)}")


# 全局实例
user_group_monitoring_service = UserGroupMonitoringService()
