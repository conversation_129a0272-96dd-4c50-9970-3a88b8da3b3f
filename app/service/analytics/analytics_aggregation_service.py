"""
分析数据聚合服务
整合现有的统计服务，提供综合分析数据
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass

from app.service.stats.stats_service import StatsService
from app.service.monitoring.user_group_monitoring import user_group_monitoring_service
from app.service.cache.cache_integration_service import cache_integration_service
from app.service.admin.user_group_service import UserGroupService
from app.log.logger import get_logger

logger = get_logger(__name__)


@dataclass
class AnalyticsTimeRange:
    """分析时间范围"""
    start_time: datetime
    end_time: datetime
    period: str  # '1h', '24h', '7d', '30d'


@dataclass
class ComprehensiveAnalytics:
    """综合分析数据"""
    overview: Dict[str, Any]
    usage_trends: Dict[str, Any]
    cost_analysis: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    user_group_analysis: Dict[str, Any]
    recommendations: List[Dict[str, Any]]
    timestamp: datetime


class AnalyticsAggregationService:
    """
    分析数据聚合服务
    整合现有的StatsService、UserGroupMonitoringService、CacheIntegrationService等统计数据
    """
    
    def __init__(self):
        self.stats_service = StatsService()
        self.monitoring_service = user_group_monitoring_service
        self.cache_service = cache_integration_service
        self.user_group_service = UserGroupService()
        
        # 成本计算配置（示例价格，实际应从配置文件读取）
        self.model_costs = {
            'gemini-1.5-flash-latest': 0.0005,  # 每1K tokens的成本
            'gemini-1.5-pro': 0.007,
            'gpt-4': 0.06,
            'gpt-3.5-turbo': 0.002
        }
    
    async def get_comprehensive_analytics(
        self, 
        group_id: Optional[str] = None, 
        period: str = '24h'
    ) -> ComprehensiveAnalytics:
        """
        获取综合分析数据
        
        Args:
            group_id: 用户组ID，None表示全局分析
            period: 时间周期 ('1h', '24h', '7d', '30d')
            
        Returns:
            ComprehensiveAnalytics: 综合分析数据
        """
        try:
            logger.info(f"Getting comprehensive analytics for group: {group_id}, period: {period}")
            
            # 解析时间范围
            time_range = self._parse_time_range(period)
            
            # 并行获取各种数据
            tasks = [
                self._get_overview_data(group_id, time_range),
                self._get_usage_trends(group_id, time_range),
                self._get_cost_analysis(group_id, time_range),
                self._get_performance_metrics(group_id, time_range),
                self._get_user_group_analysis(group_id, time_range),
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            overview = results[0] if not isinstance(results[0], Exception) else {}
            usage_trends = results[1] if not isinstance(results[1], Exception) else {}
            cost_analysis = results[2] if not isinstance(results[2], Exception) else {}
            performance_metrics = results[3] if not isinstance(results[3], Exception) else {}
            user_group_analysis = results[4] if not isinstance(results[4], Exception) else {}
            
            # 生成建议
            recommendations = await self._generate_recommendations(
                overview, usage_trends, cost_analysis, performance_metrics
            )
            
            return ComprehensiveAnalytics(
                overview=overview,
                usage_trends=usage_trends,
                cost_analysis=cost_analysis,
                performance_metrics=performance_metrics,
                user_group_analysis=user_group_analysis,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting comprehensive analytics: {str(e)}")
            # 返回默认数据
            return self._get_default_analytics()
    
    def _parse_time_range(self, period: str) -> AnalyticsTimeRange:
        """解析时间范围"""
        end_time = datetime.now()
        
        if period == '1h':
            start_time = end_time - timedelta(hours=1)
        elif period == '24h':
            start_time = end_time - timedelta(hours=24)
        elif period == '7d':
            start_time = end_time - timedelta(days=7)
        elif period == '30d':
            start_time = end_time - timedelta(days=30)
        else:
            start_time = end_time - timedelta(hours=24)  # 默认24小时
            
        return AnalyticsTimeRange(start_time, end_time, period)
    
    async def _get_overview_data(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> Dict[str, Any]:
        """获取概览数据"""
        try:
            # 获取API使用统计
            api_stats = await self.stats_service.get_api_usage_stats()
            
            # 计算总调用次数
            total_calls = 0
            success_calls = 0
            
            if time_range.period == '1h':
                total_calls = api_stats['calls_1h']['total']
                success_calls = api_stats['calls_1h']['success']
            elif time_range.period == '24h':
                total_calls = api_stats['calls_24h']['total']
                success_calls = api_stats['calls_24h']['success']
            else:
                total_calls = api_stats['calls_month']['total']
                success_calls = api_stats['calls_month']['success']
            
            success_rate = (success_calls / total_calls * 100) if total_calls > 0 else 0
            
            return {
                'total_requests': total_calls,
                'successful_requests': success_calls,
                'success_rate': round(success_rate, 2),
                'period': time_range.period,
                'active_users': await self._get_active_users_count(group_id, time_range),
                'top_models': await self._get_top_models(group_id, time_range)
            }
            
        except Exception as e:
            logger.error(f"Error getting overview data: {str(e)}")
            return {}
    
    async def _get_usage_trends(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> Dict[str, Any]:
        """获取使用趋势数据"""
        try:
            # 生成时间序列数据（模拟）
            hours = []
            requests = []
            
            # 根据时间范围生成数据点
            if time_range.period == '1h':
                # 每5分钟一个数据点
                for i in range(12):
                    time_point = time_range.start_time + timedelta(minutes=i*5)
                    hours.append(time_point.strftime('%H:%M'))
                    # 模拟数据，实际应从数据库查询
                    requests.append(max(0, 50 + (i * 10) + (i % 3 * 20)))
            else:
                # 每小时一个数据点
                hours_count = min(24, int((time_range.end_time - time_range.start_time).total_seconds() / 3600))
                for i in range(hours_count):
                    time_point = time_range.start_time + timedelta(hours=i)
                    hours.append(time_point.strftime('%m-%d %H:00'))
                    requests.append(max(0, 100 + (i * 15) + (i % 5 * 30)))
            
            return {
                'timeline': hours,
                'requests': requests,
                'period': time_range.period,
                'trend_direction': 'up' if len(requests) > 1 and requests[-1] > requests[0] else 'down'
            }
            
        except Exception as e:
            logger.error(f"Error getting usage trends: {str(e)}")
            return {}
    
    async def _get_cost_analysis(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> Dict[str, Any]:
        """获取成本分析数据"""
        try:
            # 模拟成本计算（实际应基于真实的token使用量）
            model_costs = {}
            total_cost = 0
            
            for model, unit_cost in self.model_costs.items():
                # 模拟token使用量
                estimated_tokens = max(0, 10000 + hash(model + time_range.period) % 50000)
                cost = estimated_tokens * unit_cost / 1000  # 按1K tokens计费
                model_costs[model] = {
                    'tokens': estimated_tokens,
                    'cost': round(cost, 4)
                }
                total_cost += cost
            
            return {
                'total_cost': round(total_cost, 4),
                'currency': 'USD',
                'model_breakdown': model_costs,
                'period': time_range.period,
                'cost_trend': 'stable'  # 实际应计算趋势
            }
            
        except Exception as e:
            logger.error(f"Error getting cost analysis: {str(e)}")
            return {}
    
    async def _get_performance_metrics(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> Dict[str, Any]:
        """获取性能指标数据"""
        try:
            # 模拟性能数据（实际应从监控服务获取）
            return {
                'avg_response_time': 1.25,  # 秒
                'p95_response_time': 2.8,
                'p99_response_time': 4.2,
                'cache_hit_rate': 78.5,  # 百分比
                'error_rate': 2.1,
                'throughput': 156,  # 请求/分钟
                'period': time_range.period
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {}
    
    async def _get_user_group_analysis(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> Dict[str, Any]:
        """获取用户组分析数据"""
        try:
            if group_id:
                # 单个用户组分析
                return {
                    'group_id': group_id,
                    'usage_rank': 'top_10',
                    'quota_utilization': 65.2,
                    'active_members': 12,
                    'period': time_range.period
                }
            else:
                # 全局用户组分析
                return {
                    'total_groups': 8,
                    'active_groups': 6,
                    'top_groups': [
                        {'id': 'group1', 'name': 'Development Team', 'usage': 2500},
                        {'id': 'group2', 'name': 'Research Team', 'usage': 1800},
                        {'id': 'group3', 'name': 'Marketing Team', 'usage': 1200}
                    ],
                    'period': time_range.period
                }
                
        except Exception as e:
            logger.error(f"Error getting user group analysis: {str(e)}")
            return {}
    
    async def _get_active_users_count(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> int:
        """获取活跃用户数量（模拟）"""
        return max(1, hash(str(group_id) + time_range.period) % 50)
    
    async def _get_top_models(self, group_id: Optional[str], time_range: AnalyticsTimeRange) -> List[Dict[str, Any]]:
        """获取热门模型（模拟）"""
        models = ['gemini-1.5-flash-latest', 'gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash']
        return [
            {'name': model, 'usage': max(100, hash(model + str(group_id)) % 1000)}
            for model in models[:3]
        ]
    
    async def _generate_recommendations(
        self, 
        overview: Dict[str, Any], 
        usage_trends: Dict[str, Any], 
        cost_analysis: Dict[str, Any], 
        performance_metrics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        
        # 基于成功率的建议
        success_rate = overview.get('success_rate', 0)
        if success_rate < 95:
            recommendations.append({
                'type': 'performance',
                'priority': 'high',
                'title': '提升API成功率',
                'description': f'当前成功率为{success_rate}%，建议检查错误日志并优化重试机制',
                'action': '查看错误日志'
            })
        
        # 基于缓存命中率的建议
        cache_hit_rate = performance_metrics.get('cache_hit_rate', 0)
        if cache_hit_rate < 70:
            recommendations.append({
                'type': 'cache',
                'priority': 'medium',
                'title': '优化缓存策略',
                'description': f'缓存命中率为{cache_hit_rate}%，建议调整缓存策略以提升性能',
                'action': '配置缓存'
            })
        
        # 基于成本的建议
        total_cost = cost_analysis.get('total_cost', 0)
        if total_cost > 100:  # 假设阈值
            recommendations.append({
                'type': 'cost',
                'priority': 'medium',
                'title': '成本优化',
                'description': f'当前成本为${total_cost}，建议优化模型选择以降低成本',
                'action': '查看成本分析'
            })
        
        return recommendations
    
    def _get_default_analytics(self) -> ComprehensiveAnalytics:
        """获取默认分析数据"""
        return ComprehensiveAnalytics(
            overview={},
            usage_trends={},
            cost_analysis={},
            performance_metrics={},
            user_group_analysis={},
            recommendations=[],
            timestamp=datetime.now()
        )


# 创建全局实例
analytics_aggregation_service = AnalyticsAggregationService()
