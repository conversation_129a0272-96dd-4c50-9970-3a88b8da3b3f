"""
智能路由管理器
实现负载感知、健康监控、自动故障转移的智能路由系统
"""
import asyncio
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import statistics

from app.core.connection_pool import get_connection_pool_manager
from app.log.logger import get_logger

logger = get_logger("intelligent_router")


class EndpointStatus(Enum):
    """端点状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    UNHEALTHY = "unhealthy"
    MAINTENANCE = "maintenance"


@dataclass
class EndpointInfo:
    """端点信息"""
    url: str
    provider: str  # gemini, openai, vertex
    region: str = "default"
    priority: int = 1  # 1=高优先级, 2=中优先级, 3=低优先级
    weight: float = 1.0  # 负载权重
    max_concurrent: int = 100  # 最大并发数
    timeout: float = 30.0  # 超时时间
    
    # 运行时状态
    status: EndpointStatus = EndpointStatus.HEALTHY
    current_load: int = 0  # 当前负载
    response_times: List[float] = field(default_factory=list)
    error_count: int = 0
    success_count: int = 0
    last_check: Optional[datetime] = None
    last_error: Optional[str] = None


class LoadBalancingStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    RESPONSE_TIME = "response_time"
    ADAPTIVE = "adaptive"


class IntelligentRouter:
    """智能路由管理器"""
    
    def __init__(self):
        self.endpoints: Dict[str, List[EndpointInfo]] = {}
        self.strategy = LoadBalancingStrategy.ADAPTIVE
        self.health_check_interval = 30  # 健康检查间隔（秒）
        self.health_check_task = None
        self.routing_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'failover_count': 0
        }
        self._round_robin_counters: Dict[str, int] = {}
        self._setup_default_endpoints()
        self._start_health_monitoring()
    
    def _setup_default_endpoints(self):
        """设置默认端点"""
        # Gemini API端点
        self.endpoints['gemini'] = [
            EndpointInfo(
                url="https://generativelanguage.googleapis.com/v1beta",
                provider="gemini",
                region="global",
                priority=1,
                weight=1.0,
                max_concurrent=50
            ),
            EndpointInfo(
                url="https://generativelanguage.googleapis.com/v1",
                provider="gemini",
                region="global",
                priority=2,
                weight=0.8,
                max_concurrent=30
            )
        ]
        
        # OpenAI兼容端点
        self.endpoints['openai'] = [
            EndpointInfo(
                url="https://api.openai.com/v1",
                provider="openai",
                region="us",
                priority=1,
                weight=1.0,
                max_concurrent=40
            )
        ]
        
        # Vertex AI端点
        self.endpoints['vertex'] = [
            EndpointInfo(
                url="https://us-central1-aiplatform.googleapis.com/v1",
                provider="vertex",
                region="us-central1",
                priority=1,
                weight=1.0,
                max_concurrent=30
            )
        ]
        
        # 初始化轮询计数器
        for provider in self.endpoints:
            self._round_robin_counters[provider] = 0
    
    def _start_health_monitoring(self):
        """启动健康监控"""
        try:
            self.health_check_task = asyncio.create_task(self._health_monitor_loop())
        except RuntimeError:
            # 如果没有运行的事件循环，稍后再启动
            pass
    
    async def _health_monitor_loop(self):
        """健康监控循环"""
        while True:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitor error: {str(e)}")
                await asyncio.sleep(5)
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        pool_manager = await get_connection_pool_manager()
        
        for provider, endpoints in self.endpoints.items():
            for endpoint in endpoints:
                try:
                    start_time = time.time()
                    
                    # 简单的健康检查 - 尝试连接
                    client = await pool_manager.get_client(provider)
                    
                    # 模拟健康检查请求（实际应该发送真实的健康检查请求）
                    # 这里只是更新时间戳
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    # 更新端点状态
                    endpoint.last_check = datetime.now()
                    endpoint.response_times.append(response_time)
                    
                    # 保持最近100次响应时间
                    if len(endpoint.response_times) > 100:
                        endpoint.response_times = endpoint.response_times[-100:]
                    
                    # 计算平均响应时间
                    avg_response_time = statistics.mean(endpoint.response_times)
                    
                    # 更新健康状态
                    if avg_response_time < 1.0 and endpoint.error_count < 5:
                        endpoint.status = EndpointStatus.HEALTHY
                    elif avg_response_time < 3.0 and endpoint.error_count < 10:
                        endpoint.status = EndpointStatus.WARNING
                    else:
                        endpoint.status = EndpointStatus.UNHEALTHY
                    
                    endpoint.success_count += 1
                    
                except Exception as e:
                    endpoint.error_count += 1
                    endpoint.last_error = str(e)
                    endpoint.last_check = datetime.now()
                    
                    # 如果错误过多，标记为不健康
                    if endpoint.error_count > 10:
                        endpoint.status = EndpointStatus.UNHEALTHY
                    
                    logger.warning(f"Health check failed for {endpoint.url}: {str(e)}")
    
    async def route_request(self, provider: str, request_info: Dict[str, Any]) -> Optional[EndpointInfo]:
        """
        路由请求到最佳端点
        
        Args:
            provider: 提供商类型
            request_info: 请求信息
            
        Returns:
            Optional[EndpointInfo]: 选择的端点，如果没有可用端点则返回None
        """
        if provider not in self.endpoints:
            logger.error(f"Unknown provider: {provider}")
            return None
        
        available_endpoints = [
            ep for ep in self.endpoints[provider]
            if ep.status in [EndpointStatus.HEALTHY, EndpointStatus.WARNING]
            and ep.current_load < ep.max_concurrent
        ]
        
        if not available_endpoints:
            logger.warning(f"No available endpoints for provider: {provider}")
            return None
        
        # 根据策略选择端点
        selected_endpoint = await self._select_endpoint(available_endpoints, request_info)
        
        if selected_endpoint:
            selected_endpoint.current_load += 1
            self.routing_stats['total_requests'] += 1
        
        return selected_endpoint
    
    async def _select_endpoint(self, endpoints: List[EndpointInfo], request_info: Dict[str, Any]) -> Optional[EndpointInfo]:
        """根据策略选择端点"""
        if not endpoints:
            return None
        
        if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_select(endpoints)
        elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(endpoints)
        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(endpoints)
        elif self.strategy == LoadBalancingStrategy.RESPONSE_TIME:
            return self._response_time_select(endpoints)
        elif self.strategy == LoadBalancingStrategy.ADAPTIVE:
            return self._adaptive_select(endpoints, request_info)
        else:
            return random.choice(endpoints)
    
    def _round_robin_select(self, endpoints: List[EndpointInfo]) -> EndpointInfo:
        """轮询选择"""
        provider = endpoints[0].provider
        counter = self._round_robin_counters.get(provider, 0)
        selected = endpoints[counter % len(endpoints)]
        self._round_robin_counters[provider] = counter + 1
        return selected
    
    def _weighted_round_robin_select(self, endpoints: List[EndpointInfo]) -> EndpointInfo:
        """加权轮询选择"""
        weights = [ep.weight for ep in endpoints]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return random.choice(endpoints)
        
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        
        for endpoint in endpoints:
            cumulative_weight += endpoint.weight
            if r <= cumulative_weight:
                return endpoint
        
        return endpoints[-1]
    
    def _least_connections_select(self, endpoints: List[EndpointInfo]) -> EndpointInfo:
        """最少连接选择"""
        return min(endpoints, key=lambda ep: ep.current_load)
    
    def _response_time_select(self, endpoints: List[EndpointInfo]) -> EndpointInfo:
        """响应时间选择"""
        def avg_response_time(ep):
            return statistics.mean(ep.response_times) if ep.response_times else float('inf')
        
        return min(endpoints, key=avg_response_time)
    
    def _adaptive_select(self, endpoints: List[EndpointInfo], request_info: Dict[str, Any]) -> EndpointInfo:
        """自适应选择"""
        # 综合考虑多个因素的评分
        scored_endpoints = []
        
        for endpoint in endpoints:
            score = 0
            
            # 健康状态评分
            if endpoint.status == EndpointStatus.HEALTHY:
                score += 100
            elif endpoint.status == EndpointStatus.WARNING:
                score += 50
            
            # 负载评分（负载越低分数越高）
            load_ratio = endpoint.current_load / endpoint.max_concurrent
            score += (1 - load_ratio) * 50
            
            # 响应时间评分
            if endpoint.response_times:
                avg_time = statistics.mean(endpoint.response_times)
                score += max(0, 50 - avg_time * 10)  # 响应时间越短分数越高
            
            # 优先级评分
            score += (4 - endpoint.priority) * 20  # 优先级越高分数越高
            
            # 成功率评分
            total_requests = endpoint.success_count + endpoint.error_count
            if total_requests > 0:
                success_rate = endpoint.success_count / total_requests
                score += success_rate * 30
            
            scored_endpoints.append((endpoint, score))
        
        # 选择分数最高的端点
        scored_endpoints.sort(key=lambda x: x[1], reverse=True)
        return scored_endpoints[0][0]
    
    async def release_endpoint(self, endpoint: EndpointInfo, success: bool, response_time: float = None):
        """释放端点资源"""
        endpoint.current_load = max(0, endpoint.current_load - 1)
        
        if success:
            self.routing_stats['successful_requests'] += 1
            if response_time is not None:
                endpoint.response_times.append(response_time)
                if len(endpoint.response_times) > 100:
                    endpoint.response_times = endpoint.response_times[-100:]
        else:
            self.routing_stats['failed_requests'] += 1
            endpoint.error_count += 1
            
            # 如果错误率过高，临时降低权重
            total_requests = endpoint.success_count + endpoint.error_count
            if total_requests > 10:
                error_rate = endpoint.error_count / total_requests
                if error_rate > 0.1:  # 错误率超过10%
                    endpoint.weight = max(0.1, endpoint.weight * 0.9)
    
    def add_endpoint(self, provider: str, endpoint_info: EndpointInfo):
        """添加端点"""
        if provider not in self.endpoints:
            self.endpoints[provider] = []
        
        self.endpoints[provider].append(endpoint_info)
        logger.info(f"Added endpoint for {provider}: {endpoint_info.url}")
    
    def remove_endpoint(self, provider: str, url: str) -> bool:
        """移除端点"""
        if provider not in self.endpoints:
            return False
        
        for i, endpoint in enumerate(self.endpoints[provider]):
            if endpoint.url == url:
                del self.endpoints[provider][i]
                logger.info(f"Removed endpoint for {provider}: {url}")
                return True
        
        return False
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        total_requests = self.routing_stats['total_requests']
        if total_requests > 0:
            success_rate = (self.routing_stats['successful_requests'] / total_requests) * 100
        else:
            success_rate = 0
        
        endpoint_stats = {}
        for provider, endpoints in self.endpoints.items():
            endpoint_stats[provider] = []
            for endpoint in endpoints:
                total_ep_requests = endpoint.success_count + endpoint.error_count
                ep_success_rate = (endpoint.success_count / max(total_ep_requests, 1)) * 100
                avg_response_time = statistics.mean(endpoint.response_times) if endpoint.response_times else 0
                
                endpoint_stats[provider].append({
                    'url': endpoint.url,
                    'status': endpoint.status.value,
                    'current_load': endpoint.current_load,
                    'max_concurrent': endpoint.max_concurrent,
                    'success_rate': ep_success_rate,
                    'avg_response_time': avg_response_time,
                    'priority': endpoint.priority,
                    'weight': endpoint.weight,
                    'last_check': endpoint.last_check.isoformat() if endpoint.last_check else None
                })
        
        return {
            'strategy': self.strategy.value,
            'total_requests': total_requests,
            'success_rate': success_rate,
            'failover_count': self.routing_stats['failover_count'],
            'endpoints': endpoint_stats
        }
    
    def set_strategy(self, strategy: LoadBalancingStrategy):
        """设置负载均衡策略"""
        self.strategy = strategy
        logger.info(f"Load balancing strategy changed to: {strategy.value}")
    
    async def cleanup(self):
        """清理资源"""
        if self.health_check_task and not self.health_check_task.done():
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass


# 全局智能路由器实例
_intelligent_router = None

async def get_intelligent_router() -> IntelligentRouter:
    """获取智能路由器实例"""
    global _intelligent_router
    if _intelligent_router is None:
        _intelligent_router = IntelligentRouter()
    return _intelligent_router
