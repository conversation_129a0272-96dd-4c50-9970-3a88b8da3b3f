"""
智能降级管理器
支持用户组级别的降级控制和策略配置
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

from app.database.services import get_user_group
from app.service.quota.quota_integration_service import quota_integration_service
from app.service.model.model_switch_service import model_switch_service
from app.service.model.smart_model_selector import smart_model_selector
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class FallbackStrategy(Enum):
    """降级策略类型"""
    PERFORMANCE = "performance"  # 性能优先降级
    COST = "cost"  # 成本优先降级
    QUOTA = "quota"  # 配额优先降级
    HYBRID = "hybrid"  # 混合策略


@dataclass
class FallbackDecision:
    """降级决策结果"""
    should_fallback: bool
    target_model: Optional[str]
    reason: str
    strategy_used: FallbackStrategy
    confidence: float  # 决策置信度 0-1
    estimated_success_rate: float  # 预估成功率


@dataclass
class ModelPerformanceMetrics:
    """模型性能指标"""
    model_name: str
    success_rate: float
    avg_response_time: float
    quota_availability: float
    cost_efficiency: float
    last_updated: datetime


class FallbackManager:
    """
    智能降级管理器
    支持用户组级别的降级策略和配置
    """
    
    def __init__(self):
        # 用户组降级配置
        self.group_fallback_configs: Dict[str, Dict[str, Any]] = {}
        
        # 模型性能指标缓存
        self.model_metrics: Dict[str, ModelPerformanceMetrics] = {}
        
        # 降级决策历史
        self.decision_history: List[Dict[str, Any]] = []
        
        # 性能指标更新锁
        self._metrics_lock = asyncio.Lock()
        
        # 默认降级策略配置
        self.default_strategies = {
            FallbackStrategy.PERFORMANCE: {
                "weight_success_rate": 0.4,
                "weight_response_time": 0.3,
                "weight_quota": 0.2,
                "weight_cost": 0.1
            },
            FallbackStrategy.COST: {
                "weight_success_rate": 0.2,
                "weight_response_time": 0.1,
                "weight_quota": 0.2,
                "weight_cost": 0.5
            },
            FallbackStrategy.QUOTA: {
                "weight_success_rate": 0.2,
                "weight_response_time": 0.1,
                "weight_quota": 0.6,
                "weight_cost": 0.1
            },
            FallbackStrategy.HYBRID: {
                "weight_success_rate": 0.3,
                "weight_response_time": 0.2,
                "weight_quota": 0.3,
                "weight_cost": 0.2
            }
        }
    
    async def should_fallback(
        self, 
        group_id: str, 
        current_model: str, 
        error_message: str, 
        estimated_tokens: int = 1000
    ) -> FallbackDecision:
        """
        判断是否应该进行降级
        
        Args:
            group_id: 用户组ID
            current_model: 当前模型
            error_message: 错误信息
            estimated_tokens: 预估令牌数量
            
        Returns:
            FallbackDecision: 降级决策结果
        """
        try:
            # 获取用户组配置
            group_config = await self._get_group_fallback_config(group_id)
            
            # 检查是否启用降级
            if not group_config.get("auto_fallback", True):
                return FallbackDecision(
                    should_fallback=False,
                    target_model=None,
                    reason="Auto fallback disabled for this group",
                    strategy_used=FallbackStrategy.HYBRID,
                    confidence=1.0,
                    estimated_success_rate=0.0
                )
            
            # 检查不降级模型列表
            no_fallback_models = group_config.get("no_fallback_models", [])
            if current_model in no_fallback_models:
                return FallbackDecision(
                    should_fallback=False,
                    target_model=None,
                    reason=f"Model {current_model} is in no-fallback list",
                    strategy_used=FallbackStrategy.HYBRID,
                    confidence=1.0,
                    estimated_success_rate=0.0
                )
            
            # 分析错误类型
            error_type = self._analyze_error_type(error_message)
            
            # 根据错误类型决定降级策略
            strategy = self._select_fallback_strategy(error_type, group_config)
            
            # 获取候选模型
            candidate_models = await self._get_candidate_models(
                group_id, current_model, strategy, estimated_tokens
            )
            
            if not candidate_models:
                return FallbackDecision(
                    should_fallback=False,
                    target_model=None,
                    reason="No suitable fallback models available",
                    strategy_used=strategy,
                    confidence=0.0,
                    estimated_success_rate=0.0
                )
            
            # 选择最佳降级模型
            best_model, confidence, success_rate = await self._select_best_fallback_model(
                candidate_models, strategy, group_id, estimated_tokens
            )
            
            decision = FallbackDecision(
                should_fallback=True,
                target_model=best_model,
                reason=f"Fallback due to {error_type}, using {strategy.value} strategy",
                strategy_used=strategy,
                confidence=confidence,
                estimated_success_rate=success_rate
            )
            
            # 记录决策历史
            await self._record_decision(group_id, current_model, decision, error_message)
            
            return decision
            
        except Exception as e:
            logger.error(f"Error in fallback decision for group {group_id}: {str(e)}")
            return FallbackDecision(
                should_fallback=False,
                target_model=None,
                reason=f"Internal error: {str(e)}",
                strategy_used=FallbackStrategy.HYBRID,
                confidence=0.0,
                estimated_success_rate=0.0
            )
    
    async def _get_group_fallback_config(self, group_id: str) -> Dict[str, Any]:
        """获取用户组降级配置"""
        if group_id not in self.group_fallback_configs:
            # 从数据库获取用户组配置
            user_group = await get_user_group(group_id)
            if user_group:
                self.group_fallback_configs[group_id] = {
                    "auto_fallback": user_group.get("auto_fallback", True),
                    "no_fallback_models": user_group.get("no_fallback_models", []),
                    "priority": user_group.get("priority", 5),
                    "preferred_strategy": FallbackStrategy.HYBRID
                }
            else:
                # 默认配置
                self.group_fallback_configs[group_id] = {
                    "auto_fallback": True,
                    "no_fallback_models": [],
                    "priority": 5,
                    "preferred_strategy": FallbackStrategy.HYBRID
                }
        
        return self.group_fallback_configs[group_id]
    
    def _analyze_error_type(self, error_message: str) -> str:
        """分析错误类型"""
        error_lower = error_message.lower()
        
        if any(keyword in error_lower for keyword in ["quota", "rate limit", "429"]):
            return "quota_exceeded"
        elif any(keyword in error_lower for keyword in ["timeout", "502", "503", "504"]):
            return "service_unavailable"
        elif any(keyword in error_lower for keyword in ["401", "403", "invalid key"]):
            return "authentication_error"
        elif any(keyword in error_lower for keyword in ["400", "invalid request"]):
            return "request_error"
        else:
            return "unknown_error"
    
    def _select_fallback_strategy(self, error_type: str, group_config: Dict[str, Any]) -> FallbackStrategy:
        """根据错误类型选择降级策略"""
        if error_type == "quota_exceeded":
            return FallbackStrategy.QUOTA
        elif error_type == "service_unavailable":
            return FallbackStrategy.PERFORMANCE
        else:
            return group_config.get("preferred_strategy", FallbackStrategy.HYBRID)
    
    async def _get_candidate_models(
        self, 
        group_id: str, 
        current_model: str, 
        strategy: FallbackStrategy, 
        estimated_tokens: int
    ) -> List[str]:
        """获取候选降级模型"""
        # 从现有的模型切换服务获取候选模型
        fallback_models = model_switch_service.MODEL_FALLBACK_MAP.get(current_model, [])
        
        # 过滤掉配额不足的模型
        available_models = []
        for model in fallback_models:
            quota_check = await quota_integration_service.check_request_allowed(
                group_id, model, estimated_tokens
            )
            if quota_check.get("allowed", False):
                available_models.append(model)
        
        return available_models

    async def _select_best_fallback_model(
        self,
        candidate_models: List[str],
        strategy: FallbackStrategy,
        group_id: str,
        estimated_tokens: int
    ) -> Tuple[str, float, float]:
        """
        选择最佳降级模型

        Returns:
            Tuple[model_name, confidence, estimated_success_rate]
        """
        if not candidate_models:
            return None, 0.0, 0.0

        # 更新模型性能指标
        await self._update_model_metrics(candidate_models)

        # 获取策略权重
        weights = self.default_strategies.get(strategy, self.default_strategies[FallbackStrategy.HYBRID])

        best_model = None
        best_score = -1.0
        best_success_rate = 0.0

        for model in candidate_models:
            metrics = self.model_metrics.get(model)
            if not metrics:
                continue

            # 计算综合评分
            score = (
                metrics.success_rate * weights["weight_success_rate"] +
                (1.0 - min(metrics.avg_response_time / 10.0, 1.0)) * weights["weight_response_time"] +
                metrics.quota_availability * weights["weight_quota"] +
                metrics.cost_efficiency * weights["weight_cost"]
            )

            if score > best_score:
                best_score = score
                best_model = model
                best_success_rate = metrics.success_rate

        confidence = min(best_score, 1.0) if best_model else 0.0

        return best_model, confidence, best_success_rate

    async def _update_model_metrics(self, models: List[str]):
        """更新模型性能指标"""
        async with self._metrics_lock:
            for model in models:
                if model not in self.model_metrics or self._should_update_metrics(model):
                    # 从智能模型选择器获取性能数据
                    try:
                        performance_data = smart_model_selector.get_model_performance(model)

                        self.model_metrics[model] = ModelPerformanceMetrics(
                            model_name=model,
                            success_rate=performance_data.get("success_rate", 0.8),
                            avg_response_time=performance_data.get("avg_response_time", 2.0),
                            quota_availability=performance_data.get("quota_availability", 0.5),
                            cost_efficiency=self._calculate_cost_efficiency(model),
                            last_updated=datetime.now()
                        )
                    except Exception as e:
                        logger.warning(f"Failed to update metrics for model {model}: {str(e)}")
                        # 使用默认指标
                        self.model_metrics[model] = ModelPerformanceMetrics(
                            model_name=model,
                            success_rate=0.8,
                            avg_response_time=2.0,
                            quota_availability=0.5,
                            cost_efficiency=0.5,
                            last_updated=datetime.now()
                        )

    def _should_update_metrics(self, model: str) -> bool:
        """判断是否需要更新指标"""
        if model not in self.model_metrics:
            return True

        last_updated = self.model_metrics[model].last_updated
        return datetime.now() - last_updated > timedelta(minutes=5)

    def _calculate_cost_efficiency(self, model: str) -> float:
        """计算成本效率（简化实现）"""
        # 基于模型名称的简单成本效率评估
        if "lite" in model.lower():
            return 0.9  # lite版本成本效率高
        elif "pro" in model.lower():
            return 0.3  # pro版本成本效率低
        elif "flash" in model.lower():
            return 0.7  # flash版本中等成本效率
        else:
            return 0.5  # 默认中等

    async def _record_decision(
        self,
        group_id: str,
        original_model: str,
        decision: FallbackDecision,
        error_message: str
    ):
        """记录降级决策历史"""
        try:
            decision_record = {
                "timestamp": datetime.now().isoformat(),
                "group_id": group_id,
                "original_model": original_model,
                "target_model": decision.target_model,
                "reason": decision.reason,
                "strategy": decision.strategy_used.value,
                "confidence": decision.confidence,
                "estimated_success_rate": decision.estimated_success_rate,
                "error_message": error_message[:200]  # 截断错误信息
            }

            self.decision_history.append(decision_record)

            # 保持历史记录在合理范围内
            if len(self.decision_history) > 1000:
                self.decision_history = self.decision_history[-500:]

            logger.info(f"Recorded fallback decision for group {group_id}: {original_model} -> {decision.target_model}")
        except Exception as e:
            logger.error(f"Failed to record decision: {str(e)}")

    async def get_fallback_statistics(self, group_id: Optional[str] = None, hours: int = 24) -> Dict[str, Any]:
        """获取降级统计信息"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            # 过滤历史记录
            filtered_history = [
                record for record in self.decision_history
                if datetime.fromisoformat(record["timestamp"]) > cutoff_time
                and (group_id is None or record["group_id"] == group_id)
            ]

            if not filtered_history:
                return {"total_decisions": 0, "statistics": {}}

            # 统计分析
            total_decisions = len(filtered_history)
            successful_fallbacks = len([r for r in filtered_history if r["target_model"]])

            strategy_counts = {}
            model_transitions = {}

            for record in filtered_history:
                # 策略统计
                strategy = record["strategy"]
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

                # 模型转换统计
                if record["target_model"]:
                    transition = f"{record['original_model']} -> {record['target_model']}"
                    model_transitions[transition] = model_transitions.get(transition, 0) + 1

            return {
                "total_decisions": total_decisions,
                "successful_fallbacks": successful_fallbacks,
                "success_rate": successful_fallbacks / total_decisions if total_decisions > 0 else 0,
                "strategy_distribution": strategy_counts,
                "common_transitions": dict(sorted(model_transitions.items(), key=lambda x: x[1], reverse=True)[:10]),
                "time_range_hours": hours,
                "group_id": group_id
            }
        except Exception as e:
            logger.error(f"Failed to get fallback statistics: {str(e)}")
            return {"error": str(e)}

    async def update_group_fallback_config(
        self,
        group_id: str,
        auto_fallback: Optional[bool] = None,
        no_fallback_models: Optional[List[str]] = None,
        preferred_strategy: Optional[FallbackStrategy] = None
    ) -> bool:
        """更新用户组降级配置"""
        try:
            if group_id not in self.group_fallback_configs:
                await self._get_group_fallback_config(group_id)

            config = self.group_fallback_configs[group_id]

            if auto_fallback is not None:
                config["auto_fallback"] = auto_fallback
            if no_fallback_models is not None:
                config["no_fallback_models"] = no_fallback_models
            if preferred_strategy is not None:
                config["preferred_strategy"] = preferred_strategy

            logger.info(f"Updated fallback config for group {group_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to update fallback config for group {group_id}: {str(e)}")
            return False


# 全局实例
fallback_manager = FallbackManager()
