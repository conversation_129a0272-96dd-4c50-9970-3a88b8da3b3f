"""
智能模型选择器
基于性能指标、配额状态和用户偏好选择最佳模型
"""
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict

from app.service.fallback.fallback_manager import FallbackStrategy, ModelPerformanceMetrics
from app.service.quota.quota_integration_service import quota_integration_service
from app.service.model.smart_model_selector import smart_model_selector
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


@dataclass
class ModelScore:
    """模型评分结果"""
    model_name: str
    total_score: float
    performance_score: float
    quota_score: float
    cost_score: float
    availability_score: float
    confidence: float


@dataclass
class SelectionCriteria:
    """模型选择标准"""
    group_id: str
    preferred_models: List[str]
    estimated_tokens: int
    strategy: FallbackStrategy
    max_response_time: Optional[float] = None
    min_success_rate: Optional[float] = None
    budget_priority: bool = False


class IntelligentModelSelector:
    """
    智能模型选择器
    基于多维度指标选择最优模型
    """
    
    def __init__(self):
        # 模型性能历史数据
        self.performance_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # 模型可用性缓存
        self.availability_cache: Dict[str, Dict[str, Any]] = {}
        
        # 选择决策历史
        self.selection_history: List[Dict[str, Any]] = []
        
        # 模型成本映射（相对成本，1.0为基准）
        self.model_costs = {
            "gemini-2.5-pro": 1.0,
            "gemini-2.5-flash": 0.3,
            "gemini-2.0-flash": 0.25,
            "gemini-2.5-flash-lite": 0.1,
            "gemini-2.0-flash-lite": 0.08,
            "gemini-1.5-pro": 0.8,
            "gemini-1.5-flash": 0.2
        }
        
        # 模型性能基准（相对性能，1.0为最高）
        self.model_performance_baseline = {
            "gemini-2.5-pro": 1.0,
            "gemini-2.5-flash": 0.85,
            "gemini-2.0-flash": 0.8,
            "gemini-2.5-flash-lite": 0.7,
            "gemini-2.0-flash-lite": 0.65,
            "gemini-1.5-pro": 0.9,
            "gemini-1.5-flash": 0.75
        }
    
    async def select_optimal_model(self, criteria: SelectionCriteria) -> Optional[ModelScore]:
        """
        选择最优模型
        
        Args:
            criteria: 选择标准
            
        Returns:
            ModelScore: 最优模型评分结果，如果没有合适模型则返回None
        """
        try:
            # 获取候选模型
            candidate_models = await self._get_candidate_models(criteria)
            
            if not candidate_models:
                logger.warning(f"No candidate models available for group {criteria.group_id}")
                return None
            
            # 评估每个候选模型
            model_scores = []
            for model in candidate_models:
                score = await self._evaluate_model(model, criteria)
                if score:
                    model_scores.append(score)
            
            if not model_scores:
                logger.warning(f"No suitable models found for group {criteria.group_id}")
                return None
            
            # 选择最高分模型
            best_model = max(model_scores, key=lambda x: x.total_score)
            
            # 记录选择决策
            await self._record_selection(criteria, best_model, model_scores)
            
            logger.info(f"Selected model {best_model.model_name} for group {criteria.group_id} with score {best_model.total_score:.3f}")
            
            return best_model
            
        except Exception as e:
            logger.error(f"Error selecting optimal model for group {criteria.group_id}: {str(e)}")
            return None
    
    async def _get_candidate_models(self, criteria: SelectionCriteria) -> List[str]:
        """获取候选模型列表"""
        # 从偏好模型开始
        candidates = criteria.preferred_models.copy() if criteria.preferred_models else []
        
        # 如果偏好模型为空，使用所有可用模型
        if not candidates:
            candidates = list(self.model_costs.keys())
        
        # 过滤掉配额不足的模型
        available_models = []
        for model in candidates:
            try:
                quota_check = await quota_integration_service.check_request_allowed(
                    criteria.group_id, model, criteria.estimated_tokens
                )
                if quota_check.get("allowed", False):
                    available_models.append(model)
                else:
                    logger.debug(f"Model {model} filtered out due to quota: {quota_check.get('reason', 'Unknown')}")
            except Exception as e:
                logger.warning(f"Error checking quota for model {model}: {str(e)}")
        
        return available_models
    
    async def _evaluate_model(self, model: str, criteria: SelectionCriteria) -> Optional[ModelScore]:
        """评估单个模型"""
        try:
            # 获取性能指标
            performance_metrics = await self._get_performance_metrics(model)
            
            # 获取配额状态
            quota_status = await self._get_quota_status(model, criteria.group_id, criteria.estimated_tokens)
            
            # 计算各维度评分
            performance_score = self._calculate_performance_score(model, performance_metrics, criteria)
            quota_score = self._calculate_quota_score(quota_status)
            cost_score = self._calculate_cost_score(model, criteria)
            availability_score = self._calculate_availability_score(model)
            
            # 根据策略计算总分
            total_score = self._calculate_total_score(
                performance_score, quota_score, cost_score, availability_score, criteria.strategy
            )
            
            # 计算置信度
            confidence = self._calculate_confidence(model, performance_metrics, quota_status)
            
            return ModelScore(
                model_name=model,
                total_score=total_score,
                performance_score=performance_score,
                quota_score=quota_score,
                cost_score=cost_score,
                availability_score=availability_score,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error evaluating model {model}: {str(e)}")
            return None
    
    async def _get_performance_metrics(self, model: str) -> Dict[str, float]:
        """获取模型性能指标"""
        try:
            # 尝试从智能模型选择器获取实时数据
            performance_data = smart_model_selector.get_model_performance(model)
            
            return {
                "success_rate": performance_data.get("success_rate", 0.8),
                "avg_response_time": performance_data.get("avg_response_time", 2.0),
                "error_rate": performance_data.get("error_rate", 0.1)
            }
        except Exception as e:
            logger.debug(f"Failed to get performance metrics for {model}, using defaults: {str(e)}")
            # 使用基准性能数据
            baseline_performance = self.model_performance_baseline.get(model, 0.7)
            return {
                "success_rate": baseline_performance,
                "avg_response_time": 2.0 / baseline_performance,  # 性能越高响应时间越短
                "error_rate": 1.0 - baseline_performance
            }
    
    async def _get_quota_status(self, model: str, group_id: str, estimated_tokens: int) -> Dict[str, Any]:
        """获取配额状态"""
        try:
            quota_check = await quota_integration_service.check_request_allowed(
                group_id, model, estimated_tokens
            )
            return quota_check
        except Exception as e:
            logger.warning(f"Error getting quota status for {model}: {str(e)}")
            return {"allowed": False, "reason": "quota_check_failed"}
    
    def _calculate_performance_score(self, model: str, metrics: Dict[str, float], criteria: SelectionCriteria) -> float:
        """计算性能评分"""
        success_rate = metrics.get("success_rate", 0.8)
        response_time = metrics.get("avg_response_time", 2.0)
        
        # 成功率评分
        success_score = success_rate
        
        # 响应时间评分（越短越好）
        max_acceptable_time = criteria.max_response_time or 10.0
        time_score = max(0, 1.0 - (response_time / max_acceptable_time))
        
        # 基准性能评分
        baseline_score = self.model_performance_baseline.get(model, 0.7)
        
        # 综合性能评分
        performance_score = (success_score * 0.4 + time_score * 0.3 + baseline_score * 0.3)
        
        return min(performance_score, 1.0)
    
    def _calculate_quota_score(self, quota_status: Dict[str, Any]) -> float:
        """计算配额评分"""
        if not quota_status.get("allowed", False):
            return 0.0
        
        # 基于配额状态计算评分
        quota_info = quota_status.get("quota_status", [])
        if not quota_info:
            return 0.5  # 默认中等评分
        
        # 计算平均配额利用率
        total_utilization = 0.0
        count = 0
        
        for quota in quota_info:
            if isinstance(quota, dict):
                utilization = quota.get("utilization_percent", 50.0) / 100.0
                total_utilization += utilization
                count += 1
        
        if count == 0:
            return 0.5
        
        avg_utilization = total_utilization / count
        # 利用率越低，配额评分越高
        quota_score = max(0.0, 1.0 - avg_utilization)
        
        return quota_score
    
    def _calculate_cost_score(self, model: str, criteria: SelectionCriteria) -> float:
        """计算成本评分"""
        model_cost = self.model_costs.get(model, 0.5)
        
        # 成本越低，评分越高
        cost_score = 1.0 - min(model_cost, 1.0)
        
        # 如果预算优先，提高成本权重
        if criteria.budget_priority:
            cost_score = cost_score ** 0.5  # 平方根，增强低成本模型的优势
        
        return cost_score
    
    def _calculate_availability_score(self, model: str) -> float:
        """计算可用性评分"""
        # 基于历史可用性数据计算
        # 这里简化实现，实际可以基于历史故障率
        
        # 新模型通常更稳定
        if "2.5" in model or "2.0" in model:
            return 0.9
        elif "1.5" in model:
            return 0.7
        else:
            return 0.8

    def _calculate_total_score(
        self,
        performance_score: float,
        quota_score: float,
        cost_score: float,
        availability_score: float,
        strategy: FallbackStrategy
    ) -> float:
        """根据策略计算总评分"""

        # 策略权重配置
        strategy_weights = {
            FallbackStrategy.PERFORMANCE: {
                "performance": 0.5, "quota": 0.2, "cost": 0.1, "availability": 0.2
            },
            FallbackStrategy.COST: {
                "performance": 0.2, "quota": 0.2, "cost": 0.5, "availability": 0.1
            },
            FallbackStrategy.QUOTA: {
                "performance": 0.2, "quota": 0.5, "cost": 0.1, "availability": 0.2
            },
            FallbackStrategy.HYBRID: {
                "performance": 0.3, "quota": 0.3, "cost": 0.2, "availability": 0.2
            }
        }

        weights = strategy_weights.get(strategy, strategy_weights[FallbackStrategy.HYBRID])

        total_score = (
            performance_score * weights["performance"] +
            quota_score * weights["quota"] +
            cost_score * weights["cost"] +
            availability_score * weights["availability"]
        )

        return min(total_score, 1.0)

    def _calculate_confidence(self, model: str, metrics: Dict[str, float], quota_status: Dict[str, Any]) -> float:
        """计算选择置信度"""
        confidence_factors = []

        # 基于成功率的置信度
        success_rate = metrics.get("success_rate", 0.8)
        confidence_factors.append(success_rate)

        # 基于配额状态的置信度
        if quota_status.get("allowed", False):
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.1)

        # 基于模型成熟度的置信度
        if model in self.model_performance_baseline:
            baseline = self.model_performance_baseline[model]
            confidence_factors.append(baseline)
        else:
            confidence_factors.append(0.5)

        # 计算平均置信度
        return sum(confidence_factors) / len(confidence_factors)

    async def _record_selection(
        self,
        criteria: SelectionCriteria,
        selected_model: ModelScore,
        all_scores: List[ModelScore]
    ):
        """记录选择决策"""
        try:
            selection_record = {
                "timestamp": datetime.now().isoformat(),
                "group_id": criteria.group_id,
                "strategy": criteria.strategy.value,
                "estimated_tokens": criteria.estimated_tokens,
                "selected_model": selected_model.model_name,
                "selected_score": selected_model.total_score,
                "confidence": selected_model.confidence,
                "alternatives": [
                    {"model": score.model_name, "score": score.total_score}
                    for score in sorted(all_scores, key=lambda x: x.total_score, reverse=True)[:5]
                ]
            }

            self.selection_history.append(selection_record)

            # 保持历史记录在合理范围内
            if len(self.selection_history) > 1000:
                self.selection_history = self.selection_history[-500:]

            logger.debug(f"Recorded model selection for group {criteria.group_id}")
        except Exception as e:
            logger.error(f"Failed to record selection: {str(e)}")

    async def get_selection_statistics(self, group_id: Optional[str] = None, hours: int = 24) -> Dict[str, Any]:
        """获取模型选择统计"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            # 过滤历史记录
            filtered_history = [
                record for record in self.selection_history
                if datetime.fromisoformat(record["timestamp"]) > cutoff_time
                and (group_id is None or record["group_id"] == group_id)
            ]

            if not filtered_history:
                return {"total_selections": 0, "statistics": {}}

            # 统计分析
            total_selections = len(filtered_history)

            # 模型使用频率
            model_usage = {}
            strategy_usage = {}
            avg_confidence = 0.0

            for record in filtered_history:
                model = record["selected_model"]
                strategy = record["strategy"]
                confidence = record["confidence"]

                model_usage[model] = model_usage.get(model, 0) + 1
                strategy_usage[strategy] = strategy_usage.get(strategy, 0) + 1
                avg_confidence += confidence

            avg_confidence = avg_confidence / total_selections if total_selections > 0 else 0.0

            return {
                "total_selections": total_selections,
                "model_usage": dict(sorted(model_usage.items(), key=lambda x: x[1], reverse=True)),
                "strategy_usage": strategy_usage,
                "average_confidence": avg_confidence,
                "time_range_hours": hours,
                "group_id": group_id
            }
        except Exception as e:
            logger.error(f"Failed to get selection statistics: {str(e)}")
            return {"error": str(e)}

    async def recommend_models_for_group(
        self,
        group_id: str,
        use_case: str = "general",
        budget_constraint: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """为用户组推荐模型"""
        try:
            # 创建推荐标准
            criteria = SelectionCriteria(
                group_id=group_id,
                preferred_models=[],  # 评估所有模型
                estimated_tokens=1000,  # 标准估算
                strategy=FallbackStrategy.HYBRID,
                budget_priority=budget_constraint is not None
            )

            # 获取所有候选模型
            all_models = list(self.model_costs.keys())

            # 评估每个模型
            recommendations = []
            for model in all_models:
                score = await self._evaluate_model(model, criteria)
                if score and score.total_score > 0.3:  # 过滤低分模型

                    # 检查预算约束
                    if budget_constraint:
                        model_cost = self.model_costs.get(model, 0.5)
                        if model_cost > budget_constraint:
                            continue

                    recommendations.append({
                        "model": model,
                        "score": score.total_score,
                        "confidence": score.confidence,
                        "performance_score": score.performance_score,
                        "cost_score": score.cost_score,
                        "quota_score": score.quota_score,
                        "relative_cost": self.model_costs.get(model, 0.5),
                        "use_case_fit": self._calculate_use_case_fit(model, use_case)
                    })

            # 按总分排序
            recommendations.sort(key=lambda x: x["score"], reverse=True)

            return recommendations[:10]  # 返回前10个推荐

        except Exception as e:
            logger.error(f"Failed to recommend models for group {group_id}: {str(e)}")
            return []

    def _calculate_use_case_fit(self, model: str, use_case: str) -> float:
        """计算模型与用例的匹配度"""
        use_case_preferences = {
            "general": {"gemini-2.0-flash": 0.9, "gemini-2.5-flash": 0.85},
            "high_performance": {"gemini-2.5-pro": 1.0, "gemini-2.5-flash": 0.8},
            "cost_sensitive": {"gemini-2.0-flash-lite": 1.0, "gemini-2.5-flash-lite": 0.9},
            "high_volume": {"gemini-2.0-flash-lite": 0.95, "gemini-2.0-flash": 0.8}
        }

        preferences = use_case_preferences.get(use_case, {})
        return preferences.get(model, 0.7)  # 默认适配度


# 全局实例
intelligent_model_selector = IntelligentModelSelector()
