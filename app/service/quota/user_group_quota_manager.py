"""
用户组配额管理器
基于现有QuotaMonitor扩展，支持多级配额控制
"""
import asyncio
from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

from app.service.model.quota_monitor import QuotaMonitor, QuotaUsage
from app.service.quota.token_bucket import TokenBucket, TokenBucketConfig, SlidingWindowCounter, HybridRateLimiter
from app.database.services import get_user_group
from app.database.models import QuotaType
from app.log.logger import get_quota_logger

logger = get_quota_logger()


@dataclass
class UserGroupQuotaConfig:
    """用户组配额配置"""
    group_id: str
    model_name: Optional[str]  # None表示全局配额
    quota_type: QuotaType
    quota_limit: int
    window_duration_minutes: int = 1


@dataclass
class QuotaStatus:
    """配额状态"""
    group_id: str
    model_name: Optional[str]
    quota_type: QuotaType
    current_usage: int
    quota_limit: int
    utilization_percent: float
    is_exceeded: bool
    time_until_reset: Optional[int]  # 秒


class UserGroupQuotaManager(QuotaMonitor):
    """
    用户组配额管理器
    扩展现有QuotaMonitor，支持多级配额控制
    """
    
    def __init__(self):
        super().__init__()
        
        # 用户组配额配置
        self.group_quotas: Dict[str, Dict[str, UserGroupQuotaConfig]] = defaultdict(dict)
        
        # 用户组令牌桶 - 用于平滑限流
        self.group_token_buckets: Dict[str, Dict[str, TokenBucket]] = defaultdict(dict)
        
        # 用户组滑动窗口计数器
        self.group_sliding_windows: Dict[str, Dict[str, SlidingWindowCounter]] = defaultdict(dict)
        
        # 用户组混合限流器
        self.group_rate_limiters: Dict[str, Dict[str, HybridRateLimiter]] = defaultdict(dict)
        
        # 用户组使用统计
        self.group_usage_stats: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # 配额预警阈值
        self.WARNING_THRESHOLD = 0.8  # 80%
        self.CRITICAL_THRESHOLD = 0.95  # 95%
    
    async def add_group_quota(self, config: UserGroupQuotaConfig):
        """添加用户组配额配置"""
        try:
            # 验证用户组是否存在
            user_group = await get_user_group(config.group_id)
            if not user_group:
                logger.warning(f"User group {config.group_id} not found")
                return False
            
            quota_key = self._get_quota_key(config.model_name, config.quota_type)
            self.group_quotas[config.group_id][quota_key] = config
            
            # 创建对应的限流器
            await self._create_rate_limiter(config)
            
            logger.info(f"Added quota for group {config.group_id}: {config.quota_type.value} = {config.quota_limit}")
            return True
        except Exception as e:
            logger.error(f"Failed to add group quota: {str(e)}")
            return False
    
    async def _create_rate_limiter(self, config: UserGroupQuotaConfig):
        """为配额配置创建限流器"""
        quota_key = self._get_quota_key(config.model_name, config.quota_type)
        
        if config.quota_type in [QuotaType.RPM, QuotaType.RPD]:
            # 请求频率限制 - 使用滑动窗口
            window_size = config.window_duration_minutes * 60  # 转换为秒
            self.group_sliding_windows[config.group_id][quota_key] = SlidingWindowCounter(
                window_size=window_size,
                max_requests=config.quota_limit
            )
        elif config.quota_type in [QuotaType.TPM, QuotaType.TPD]:
            # 令牌使用限制 - 使用令牌桶
            refill_rate = config.quota_limit / (config.window_duration_minutes * 60)  # 每秒补充速率
            bucket_config = TokenBucketConfig(
                capacity=config.quota_limit,
                refill_rate=refill_rate,
                initial_tokens=config.quota_limit
            )
            self.group_token_buckets[config.group_id][quota_key] = TokenBucket(bucket_config)
        
        # 创建混合限流器（如果需要）
        if config.quota_type == QuotaType.RPM:
            # 为RPM创建混合限流器，结合令牌桶平滑突发请求
            bucket_config = TokenBucketConfig(
                capacity=min(config.quota_limit, 100),  # 桶容量不超过100
                refill_rate=config.quota_limit / 60,  # 每秒补充速率
                initial_tokens=min(config.quota_limit, 100)
            )
            self.group_rate_limiters[config.group_id][quota_key] = HybridRateLimiter(
                bucket_config=bucket_config,
                window_size=config.window_duration_minutes * 60,
                max_requests=config.quota_limit
            )
    
    def _get_quota_key(self, model_name: Optional[str], quota_type: QuotaType) -> str:
        """生成配额键"""
        model_part = model_name if model_name else "global"
        return f"{model_part}:{quota_type.value}"
    
    async def check_group_quota(self, group_id: str, model_name: str, quota_type: QuotaType, amount: int = 1) -> bool:
        """
        检查用户组配额是否可用
        
        Args:
            group_id: 用户组ID
            model_name: 模型名称
            quota_type: 配额类型
            amount: 请求数量或令牌数量
            
        Returns:
            bool: 是否允许请求
        """
        try:
            # 检查模型特定配额
            model_quota_key = self._get_quota_key(model_name, quota_type)
            if await self._check_specific_quota(group_id, model_quota_key, amount):
                return True
            
            # 检查全局配额
            global_quota_key = self._get_quota_key(None, quota_type)
            if await self._check_specific_quota(group_id, global_quota_key, amount):
                return True
            
            # 如果用户组配额都不可用，检查全局配额监控器
            if quota_type in [QuotaType.RPM, QuotaType.TPM, QuotaType.RPD]:
                # 这里可以调用父类的配额检查作为后备
                return self.is_quota_available(model_name, f"group_{group_id}", amount if quota_type == QuotaType.TPM else 1000)
            
            return False
        except Exception as e:
            logger.error(f"Error checking group quota: {str(e)}")
            return False
    
    async def _check_specific_quota(self, group_id: str, quota_key: str, amount: int) -> bool:
        """检查特定配额"""
        # 检查滑动窗口（用于请求频率）
        if quota_key in self.group_sliding_windows.get(group_id, {}):
            window = self.group_sliding_windows[group_id][quota_key]
            for _ in range(amount):
                if not await window.is_allowed():
                    return False
            return True
        
        # 检查令牌桶（用于令牌使用）
        if quota_key in self.group_token_buckets.get(group_id, {}):
            bucket = self.group_token_buckets[group_id][quota_key]
            return await bucket.consume(amount)
        
        # 检查混合限流器
        if quota_key in self.group_rate_limiters.get(group_id, {}):
            limiter = self.group_rate_limiters[group_id][quota_key]
            return await limiter.is_allowed(amount)
        
        return False
    
    async def track_group_usage(self, group_id: str, model_name: str, tokens_used: int = 0):
        """跟踪用户组使用情况"""
        try:
            # 更新使用统计
            current_time = datetime.now()
            usage_key = f"{model_name}:{current_time.strftime('%Y-%m-%d-%H-%M')}"
            
            if usage_key not in self.group_usage_stats[group_id]:
                self.group_usage_stats[group_id][usage_key] = {
                    "model": model_name,
                    "requests": 0,
                    "tokens": 0,
                    "timestamp": current_time
                }
            
            self.group_usage_stats[group_id][usage_key]["requests"] += 1
            self.group_usage_stats[group_id][usage_key]["tokens"] += tokens_used
            
            # 同时调用父类方法进行全局跟踪
            self.track_request(model_name, f"group_{group_id}", tokens_used)
            
            logger.debug(f"Tracked usage for group {group_id}: {model_name}, tokens: {tokens_used}")
        except Exception as e:
            logger.error(f"Error tracking group usage: {str(e)}")
    
    async def get_group_quota_status(self, group_id: str) -> List[QuotaStatus]:
        """获取用户组配额状态"""
        try:
            statuses = []
            
            if group_id not in self.group_quotas:
                return statuses
            
            for quota_key, config in self.group_quotas[group_id].items():
                current_usage = await self._get_current_usage(group_id, quota_key, config.quota_type)
                utilization = (current_usage / config.quota_limit) * 100 if config.quota_limit > 0 else 0
                
                status = QuotaStatus(
                    group_id=group_id,
                    model_name=config.model_name,
                    quota_type=config.quota_type,
                    current_usage=current_usage,
                    quota_limit=config.quota_limit,
                    utilization_percent=utilization,
                    is_exceeded=current_usage >= config.quota_limit,
                    time_until_reset=self._calculate_reset_time(config)
                )
                statuses.append(status)
            
            return statuses
        except Exception as e:
            logger.error(f"Error getting group quota status: {str(e)}")
            return []

    async def _get_current_usage(self, group_id: str, quota_key: str, quota_type: QuotaType) -> int:
        """获取当前使用量"""
        try:
            # 从滑动窗口获取
            if quota_key in self.group_sliding_windows.get(group_id, {}):
                window = self.group_sliding_windows[group_id][quota_key]
                return await window.get_current_count()

            # 从令牌桶获取（计算已使用的令牌）
            if quota_key in self.group_token_buckets.get(group_id, {}):
                bucket = self.group_token_buckets[group_id][quota_key]
                status = await bucket.get_status()
                return status["capacity"] - status["current_tokens"]

            return 0
        except Exception as e:
            logger.error(f"Error getting current usage: {str(e)}")
            return 0

    def _calculate_reset_time(self, config: UserGroupQuotaConfig) -> Optional[int]:
        """计算配额重置时间（秒）"""
        if config.quota_type in [QuotaType.RPM, QuotaType.TPM]:
            # 分钟级配额，计算到下一分钟的秒数
            now = datetime.now()
            next_minute = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
            return int((next_minute - now).total_seconds())
        elif config.quota_type in [QuotaType.RPD, QuotaType.TPD]:
            # 日级配额，计算到明天的秒数
            now = datetime.now()
            next_day = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            return int((next_day - now).total_seconds())
        return None

    async def predict_group_quota_exhaustion(self, group_id: str) -> List[str]:
        """预测用户组配额耗尽情况"""
        try:
            warnings = []
            statuses = await self.get_group_quota_status(group_id)

            for status in statuses:
                if status.utilization_percent >= self.CRITICAL_THRESHOLD * 100:
                    warnings.append(f"CRITICAL: {status.quota_type.value} usage at {status.utilization_percent:.1f}%")
                elif status.utilization_percent >= self.WARNING_THRESHOLD * 100:
                    warnings.append(f"WARNING: {status.quota_type.value} usage at {status.utilization_percent:.1f}%")

            return warnings
        except Exception as e:
            logger.error(f"Error predicting quota exhaustion: {str(e)}")
            return []

    async def get_best_available_model_for_group(self, group_id: str, preferred_models: List[str], estimated_tokens: int = 1000) -> Optional[str]:
        """为用户组获取最佳可用模型"""
        try:
            for model in preferred_models:
                # 检查RPM配额
                if await self.check_group_quota(group_id, model, QuotaType.RPM, 1):
                    # 检查TPM配额
                    if await self.check_group_quota(group_id, model, QuotaType.TPM, estimated_tokens):
                        # 检查RPD配额
                        if await self.check_group_quota(group_id, model, QuotaType.RPD, 1):
                            return model

            # 如果用户组配额都不可用，尝试全局配额
            return self.get_best_available_model(preferred_models, f"group_{group_id}")
        except Exception as e:
            logger.error(f"Error getting best available model for group: {str(e)}")
            return None

    async def reset_group_quota(self, group_id: str, quota_type: Optional[QuotaType] = None):
        """重置用户组配额"""
        try:
            if group_id not in self.group_quotas:
                return False

            for quota_key in self.group_quotas[group_id].keys():
                config = self.group_quotas[group_id][quota_key]

                # 如果指定了配额类型，只重置该类型
                if quota_type and config.quota_type != quota_type:
                    continue

                # 重置滑动窗口
                if quota_key in self.group_sliding_windows.get(group_id, {}):
                    await self.group_sliding_windows[group_id][quota_key].reset()

                # 重置令牌桶
                if quota_key in self.group_token_buckets.get(group_id, {}):
                    await self.group_token_buckets[group_id][quota_key].reset()

                # 重置混合限流器
                if quota_key in self.group_rate_limiters.get(group_id, {}):
                    await self.group_rate_limiters[group_id][quota_key].reset()

            logger.info(f"Reset quota for group {group_id}, type: {quota_type}")
            return True
        except Exception as e:
            logger.error(f"Error resetting group quota: {str(e)}")
            return False

    async def get_group_usage_summary(self, group_id: str, hours: int = 24) -> Dict[str, Any]:
        """获取用户组使用摘要"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            summary = {
                "group_id": group_id,
                "time_range_hours": hours,
                "models": {},
                "total_requests": 0,
                "total_tokens": 0
            }

            if group_id not in self.group_usage_stats:
                return summary

            for usage_key, usage_data in self.group_usage_stats[group_id].items():
                if usage_data["timestamp"] < cutoff_time:
                    continue

                model = usage_data["model"]
                if model not in summary["models"]:
                    summary["models"][model] = {"requests": 0, "tokens": 0}

                summary["models"][model]["requests"] += usage_data["requests"]
                summary["models"][model]["tokens"] += usage_data["tokens"]
                summary["total_requests"] += usage_data["requests"]
                summary["total_tokens"] += usage_data["tokens"]

            return summary
        except Exception as e:
            logger.error(f"Error getting group usage summary: {str(e)}")
            return {"error": str(e)}


# 全局实例
user_group_quota_manager = UserGroupQuotaManager()
