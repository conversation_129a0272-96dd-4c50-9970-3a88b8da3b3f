"""
配额集成服务
将用户组配额管理集成到现有的重试处理流程中
"""
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.service.quota.user_group_quota_manager import (
    UserGroupQuotaManager, 
    UserGroupQuotaConfig, 
    user_group_quota_manager
)
from app.service.quota.token_bucket import TokenBucketConfig
from app.database.models import QuotaType
from app.database.services import get_user_group
from app.log.logger import get_quota_logger

logger = get_quota_logger()


class QuotaIntegrationService:
    """
    配额集成服务
    负责将用户组配额管理集成到现有系统中
    """
    
    def __init__(self):
        self.quota_manager = user_group_quota_manager
        self.initialized_groups = set()
    
    async def initialize_group_quotas_from_database(self, group_id: str) -> bool:
        """从数据库初始化用户组配额"""
        try:
            if group_id in self.initialized_groups:
                return True
            
            # 获取用户组信息
            user_group = await get_user_group(group_id)
            if not user_group:
                logger.warning(f"User group {group_id} not found")
                return False
            
            # 这里应该从数据库的UserGroupQuota表中读取配额配置
            # 由于数据库服务还没有相关方法，我们先使用默认配置
            default_quotas = [
                UserGroupQuotaConfig(
                    group_id=group_id,
                    model_name=None,  # 全局配额
                    quota_type=QuotaType.RPM,
                    quota_limit=100,  # 每分钟100请求
                    window_duration_minutes=1
                ),
                UserGroupQuotaConfig(
                    group_id=group_id,
                    model_name=None,  # 全局配额
                    quota_type=QuotaType.TPM,
                    quota_limit=50000,  # 每分钟50000令牌
                    window_duration_minutes=1
                ),
                UserGroupQuotaConfig(
                    group_id=group_id,
                    model_name=None,  # 全局配额
                    quota_type=QuotaType.RPD,
                    quota_limit=1000,  # 每天1000请求
                    window_duration_minutes=1440  # 24小时
                )
            ]
            
            # 添加配额配置
            for quota_config in default_quotas:
                await self.quota_manager.add_group_quota(quota_config)
            
            self.initialized_groups.add(group_id)
            logger.info(f"Initialized quotas for group {group_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize group quotas: {str(e)}")
            return False
    
    async def check_request_allowed(self, group_id: str, model_name: str, estimated_tokens: int = 1000) -> Dict[str, Any]:
        """
        检查请求是否被允许
        
        Args:
            group_id: 用户组ID
            model_name: 模型名称
            estimated_tokens: 预估令牌数量
            
        Returns:
            Dict包含是否允许、原因、建议等信息
        """
        try:
            # 确保用户组配额已初始化
            await self.initialize_group_quotas_from_database(group_id)
            
            result = {
                "allowed": False,
                "reason": "",
                "suggestions": [],
                "quota_status": {},
                "alternative_model": None
            }
            
            # 检查RPM配额
            rpm_allowed = await self.quota_manager.check_group_quota(group_id, model_name, QuotaType.RPM, 1)
            if not rpm_allowed:
                result["reason"] = "RPM quota exceeded"
                result["suggestions"].append("请降低请求频率或等待配额重置")
                return result
            
            # 检查TPM配额
            tpm_allowed = await self.quota_manager.check_group_quota(group_id, model_name, QuotaType.TPM, estimated_tokens)
            if not tpm_allowed:
                result["reason"] = "TPM quota exceeded"
                result["suggestions"].append("请减少输入文本长度或等待配额重置")
                return result
            
            # 检查RPD配额
            rpd_allowed = await self.quota_manager.check_group_quota(group_id, model_name, QuotaType.RPD, 1)
            if not rpd_allowed:
                result["reason"] = "RPD quota exceeded"
                result["suggestions"].append("今日配额已用完，请明天再试")
                return result
            
            # 所有检查通过
            result["allowed"] = True
            result["reason"] = "All quotas available"
            
            # 获取配额状态
            result["quota_status"] = await self.quota_manager.get_group_quota_status(group_id)
            
            return result
        except Exception as e:
            logger.error(f"Error checking request allowed: {str(e)}")
            return {
                "allowed": False,
                "reason": f"Internal error: {str(e)}",
                "suggestions": ["请稍后重试"],
                "quota_status": {},
                "alternative_model": None
            }
    
    async def track_request_completion(self, group_id: str, model_name: str, actual_tokens: int, success: bool = True):
        """
        跟踪请求完成情况
        
        Args:
            group_id: 用户组ID
            model_name: 模型名称
            actual_tokens: 实际使用的令牌数量
            success: 请求是否成功
        """
        try:
            if success:
                # 只有成功的请求才计入使用统计
                await self.quota_manager.track_group_usage(group_id, model_name, actual_tokens)
                logger.debug(f"Tracked successful request for group {group_id}: {model_name}, tokens: {actual_tokens}")
            else:
                # 失败的请求可能需要退还配额（取决于业务逻辑）
                logger.debug(f"Request failed for group {group_id}: {model_name}")
        except Exception as e:
            logger.error(f"Error tracking request completion: {str(e)}")
    
    async def get_quota_warnings(self, group_id: str) -> List[str]:
        """获取配额预警信息"""
        try:
            await self.initialize_group_quotas_from_database(group_id)
            return await self.quota_manager.predict_group_quota_exhaustion(group_id)
        except Exception as e:
            logger.error(f"Error getting quota warnings: {str(e)}")
            return []
    
    async def suggest_alternative_model(self, group_id: str, preferred_models: List[str], estimated_tokens: int = 1000) -> Optional[str]:
        """建议替代模型"""
        try:
            await self.initialize_group_quotas_from_database(group_id)
            return await self.quota_manager.get_best_available_model_for_group(group_id, preferred_models, estimated_tokens)
        except Exception as e:
            logger.error(f"Error suggesting alternative model: {str(e)}")
            return None
    
    async def get_group_usage_report(self, group_id: str, hours: int = 24) -> Dict[str, Any]:
        """获取用户组使用报告"""
        try:
            await self.initialize_group_quotas_from_database(group_id)
            
            # 获取使用摘要
            usage_summary = await self.quota_manager.get_group_usage_summary(group_id, hours)
            
            # 获取配额状态
            quota_status = await self.quota_manager.get_group_quota_status(group_id)
            
            # 获取预警信息
            warnings = await self.quota_manager.predict_group_quota_exhaustion(group_id)
            
            return {
                "usage_summary": usage_summary,
                "quota_status": [
                    {
                        "model": status.model_name,
                        "quota_type": status.quota_type.value,
                        "current_usage": status.current_usage,
                        "quota_limit": status.quota_limit,
                        "utilization_percent": status.utilization_percent,
                        "is_exceeded": status.is_exceeded,
                        "time_until_reset": status.time_until_reset
                    }
                    for status in quota_status
                ],
                "warnings": warnings,
                "report_time": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting group usage report: {str(e)}")
            return {"error": str(e)}
    
    async def reset_group_quotas(self, group_id: str, quota_type: Optional[QuotaType] = None) -> bool:
        """重置用户组配额（管理员功能）"""
        try:
            result = await self.quota_manager.reset_group_quota(group_id, quota_type)
            if result:
                logger.info(f"Admin reset quotas for group {group_id}, type: {quota_type}")
            return result
        except Exception as e:
            logger.error(f"Error resetting group quotas: {str(e)}")
            return False
    
    async def update_group_quota_config(self, group_id: str, quota_configs: List[UserGroupQuotaConfig]) -> bool:
        """更新用户组配额配置"""
        try:
            # 这里应该更新数据库中的配额配置
            # 然后重新初始化内存中的配额管理器
            
            for config in quota_configs:
                await self.quota_manager.add_group_quota(config)
            
            logger.info(f"Updated quota config for group {group_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating group quota config: {str(e)}")
            return False
    
    async def get_system_quota_overview(self) -> Dict[str, Any]:
        """获取系统配额概览"""
        try:
            overview = {
                "initialized_groups": len(self.initialized_groups),
                "groups": [],
                "system_status": "healthy"
            }
            
            for group_id in self.initialized_groups:
                group_status = await self.quota_manager.get_group_quota_status(group_id)
                warnings = await self.quota_manager.predict_group_quota_exhaustion(group_id)
                
                overview["groups"].append({
                    "group_id": group_id,
                    "quota_count": len(group_status),
                    "warnings_count": len(warnings),
                    "has_critical_warnings": any("CRITICAL" in warning for warning in warnings)
                })
            
            # 检查系统整体状态
            critical_groups = [g for g in overview["groups"] if g["has_critical_warnings"]]
            if critical_groups:
                overview["system_status"] = "warning"
            
            return overview
        except Exception as e:
            logger.error(f"Error getting system quota overview: {str(e)}")
            return {"error": str(e)}


# 全局实例
quota_integration_service = QuotaIntegrationService()
