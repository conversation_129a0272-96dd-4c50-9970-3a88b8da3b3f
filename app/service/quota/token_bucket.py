"""
令牌桶算法实现
用于平滑限流和配额控制
"""
import asyncio
import time
from typing import Optional
from dataclasses import dataclass
from app.log.logger import get_quota_logger

logger = get_quota_logger()


@dataclass
class TokenBucketConfig:
    """令牌桶配置"""
    capacity: int  # 桶容量
    refill_rate: float  # 令牌补充速率（每秒）
    initial_tokens: Optional[int] = None  # 初始令牌数


class TokenBucket:
    """
    令牌桶算法实现
    用于平滑限流，避免突发流量
    """
    
    def __init__(self, config: TokenBucketConfig):
        self.capacity = config.capacity
        self.refill_rate = config.refill_rate
        self.tokens = config.initial_tokens if config.initial_tokens is not None else config.capacity
        self.last_refill_time = time.time()
        self._lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """
        尝试消费指定数量的令牌
        
        Args:
            tokens: 需要消费的令牌数量
            
        Returns:
            bool: 是否成功消费令牌
        """
        async with self._lock:
            await self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                logger.debug(f"Consumed {tokens} tokens, remaining: {self.tokens}")
                return True
            else:
                logger.debug(f"Insufficient tokens: need {tokens}, have {self.tokens}")
                return False
    
    async def peek(self) -> int:
        """
        查看当前可用令牌数量（不消费）
        
        Returns:
            int: 当前可用令牌数量
        """
        async with self._lock:
            await self._refill()
            return int(self.tokens)
    
    async def wait_for_tokens(self, tokens: int = 1, timeout: Optional[float] = None) -> bool:
        """
        等待直到有足够的令牌可用
        
        Args:
            tokens: 需要的令牌数量
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            bool: 是否在超时前获得了足够的令牌
        """
        start_time = time.time()
        
        while True:
            if await self.consume(tokens):
                return True
            
            if timeout and (time.time() - start_time) >= timeout:
                logger.warning(f"Timeout waiting for {tokens} tokens")
                return False
            
            # 计算需要等待的时间
            async with self._lock:
                await self._refill()
                needed_tokens = tokens - self.tokens
                if needed_tokens > 0:
                    wait_time = needed_tokens / self.refill_rate
                    wait_time = min(wait_time, 1.0)  # 最多等待1秒
                else:
                    wait_time = 0.1  # 短暂等待后重试
            
            await asyncio.sleep(wait_time)
    
    async def _refill(self):
        """补充令牌"""
        current_time = time.time()
        time_passed = current_time - self.last_refill_time
        
        if time_passed > 0:
            tokens_to_add = time_passed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill_time = current_time
    
    async def get_status(self) -> dict:
        """获取令牌桶状态"""
        async with self._lock:
            await self._refill()
            return {
                "capacity": self.capacity,
                "current_tokens": int(self.tokens),
                "refill_rate": self.refill_rate,
                "utilization": (self.capacity - self.tokens) / self.capacity * 100,
                "last_refill_time": self.last_refill_time
            }
    
    async def reset(self):
        """重置令牌桶"""
        async with self._lock:
            self.tokens = self.capacity
            self.last_refill_time = time.time()
            logger.info("Token bucket reset")


class SlidingWindowCounter:
    """
    滑动窗口计数器
    用于时间窗口内的配额控制
    """
    
    def __init__(self, window_size: int, max_requests: int):
        """
        Args:
            window_size: 窗口大小（秒）
            max_requests: 窗口内最大请求数
        """
        self.window_size = window_size
        self.max_requests = max_requests
        self.requests = []  # 存储请求时间戳
        self._lock = asyncio.Lock()
    
    async def is_allowed(self) -> bool:
        """
        检查是否允许新请求
        
        Returns:
            bool: 是否允许请求
        """
        async with self._lock:
            current_time = time.time()
            
            # 清理过期的请求记录
            cutoff_time = current_time - self.window_size
            self.requests = [req_time for req_time in self.requests if req_time > cutoff_time]
            
            # 检查是否超过限制
            if len(self.requests) < self.max_requests:
                self.requests.append(current_time)
                logger.debug(f"Request allowed: {len(self.requests)}/{self.max_requests}")
                return True
            else:
                logger.debug(f"Request denied: {len(self.requests)}/{self.max_requests}")
                return False
    
    async def get_current_count(self) -> int:
        """获取当前窗口内的请求数量"""
        async with self._lock:
            current_time = time.time()
            cutoff_time = current_time - self.window_size
            self.requests = [req_time for req_time in self.requests if req_time > cutoff_time]
            return len(self.requests)
    
    async def get_status(self) -> dict:
        """获取滑动窗口状态"""
        current_count = await self.get_current_count()
        return {
            "window_size": self.window_size,
            "max_requests": self.max_requests,
            "current_count": current_count,
            "utilization": (current_count / self.max_requests) * 100,
            "remaining": self.max_requests - current_count
        }
    
    async def reset(self):
        """重置计数器"""
        async with self._lock:
            self.requests.clear()
            logger.info("Sliding window counter reset")


class HybridRateLimiter:
    """
    混合限流器
    结合令牌桶和滑动窗口的优势
    """
    
    def __init__(self, bucket_config: TokenBucketConfig, window_size: int, max_requests: int):
        self.token_bucket = TokenBucket(bucket_config)
        self.sliding_window = SlidingWindowCounter(window_size, max_requests)
    
    async def is_allowed(self, tokens: int = 1) -> bool:
        """
        检查请求是否被允许
        需要同时通过令牌桶和滑动窗口检查
        
        Args:
            tokens: 需要消费的令牌数量
            
        Returns:
            bool: 是否允许请求
        """
        # 先检查滑动窗口
        if not await self.sliding_window.is_allowed():
            return False
        
        # 再检查令牌桶
        if not await self.token_bucket.consume(tokens):
            return False
        
        return True
    
    async def get_status(self) -> dict:
        """获取混合限流器状态"""
        bucket_status = await self.token_bucket.get_status()
        window_status = await self.sliding_window.get_status()
        
        return {
            "token_bucket": bucket_status,
            "sliding_window": window_status,
            "overall_status": "healthy" if bucket_status["current_tokens"] > 0 and window_status["remaining"] > 0 else "limited"
        }
    
    async def reset(self):
        """重置所有限流器"""
        await self.token_bucket.reset()
        await self.sliding_window.reset()
        logger.info("Hybrid rate limiter reset")
