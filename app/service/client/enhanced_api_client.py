"""
增强API客户端
集成智能路由、连接池、缓存等优化功能
"""
import time
from typing import Dict, Any, AsyncGenerator, Optional
import httpx

from app.service.client.api_client import ApiClient
from app.core.connection_pool import get_connection_pool_manager
from app.service.routing.intelligent_router import get_intelligent_router
from app.service.cache.enhanced_cache_service import get_enhanced_cache_service
from app.config.config import settings
from app.log.logger import get_api_client_logger
from app.core.constants import DEFAULT_TIMEOUT

logger = get_api_client_logger()


class EnhancedApiClient(ApiClient):
    """增强API客户端，集成智能路由和缓存"""
    
    def __init__(self, provider: str, timeout: int = DEFAULT_TIMEOUT):
        self.provider = provider
        self.timeout = timeout
        self.cache_enabled = True
        self.routing_enabled = True
    
    async def generate_content(self, payload: Dict[str, Any], model: str, api_key: str) -> Dict[str, Any]:
        """
        生成内容，集成智能路由和缓存
        
        Args:
            payload: 请求负载
            model: 模型名称
            api_key: API密钥
            
        Returns:
            Dict[str, Any]: 生成的内容
        """
        start_time = time.time()
        selected_endpoint = None
        
        try:
            # 1. 检查缓存
            if self.cache_enabled:
                cache_key = self._generate_cache_key(payload, model)
                enhanced_cache = await get_enhanced_cache_service()
                cached_result = await enhanced_cache.get(cache_key, "default")
                
                if cached_result is not None:
                    logger.info(f"Cache hit for model {model}")
                    return cached_result
            
            # 2. 智能路由选择端点
            if self.routing_enabled:
                router = await get_intelligent_router()
                request_info = {
                    "model": model,
                    "estimated_tokens": self._estimate_tokens(payload),
                    "priority": "normal"
                }
                selected_endpoint = await router.route_request(self.provider, request_info)
                
                if not selected_endpoint:
                    raise Exception(f"No available endpoints for provider: {self.provider}")
                
                base_url = selected_endpoint.url
            else:
                # 使用默认URL
                base_url = self._get_default_base_url()
            
            # 3. 发起请求
            pool_manager = await get_connection_pool_manager()
            url = self._build_url(base_url, model, api_key)
            
            response = await pool_manager.make_request(
                'POST', url,
                pool_type=self.provider,
                json=payload,
                headers=self._prepare_headers(),
                api_key=api_key
            )
            
            if response.status_code != 200:
                error_content = response.text
                logger.error(f"API call failed - Status: {response.status_code}, Content: {error_content}")
                raise httpx.HTTPStatusError(
                    f"API call failed with status code {response.status_code}",
                    request=response.request,
                    response=response
                )
            
            response_data = response.json()
            
            # 4. 缓存结果
            if self.cache_enabled and response_data:
                cache_key = self._generate_cache_key(payload, model)
                enhanced_cache = await get_enhanced_cache_service()
                await enhanced_cache.set(cache_key, response_data, "default")
            
            # 5. 更新路由统计
            if self.routing_enabled and selected_endpoint:
                end_time = time.time()
                response_time = end_time - start_time
                router = await get_intelligent_router()
                await router.release_endpoint(selected_endpoint, success=True, response_time=response_time)
            
            return response_data
            
        except Exception as e:
            # 更新路由统计（失败）
            if self.routing_enabled and selected_endpoint:
                router = await get_intelligent_router()
                await router.release_endpoint(selected_endpoint, success=False)
            
            logger.error(f"Enhanced API client error: {str(e)}")
            raise
    
    async def stream_generate_content(self, payload: Dict[str, Any], model: str, api_key: str) -> AsyncGenerator[str, None]:
        """
        流式生成内容
        
        Args:
            payload: 请求负载
            model: 模型名称
            api_key: API密钥
            
        Yields:
            str: 流式响应数据
        """
        start_time = time.time()
        selected_endpoint = None
        
        try:
            # 1. 智能路由选择端点
            if self.routing_enabled:
                router = await get_intelligent_router()
                request_info = {
                    "model": model,
                    "estimated_tokens": self._estimate_tokens(payload),
                    "priority": "normal",
                    "streaming": True
                }
                selected_endpoint = await router.route_request(self.provider, request_info)
                
                if not selected_endpoint:
                    raise Exception(f"No available endpoints for provider: {self.provider}")
                
                base_url = selected_endpoint.url
            else:
                base_url = self._get_default_base_url()
            
            # 2. 发起流式请求
            pool_manager = await get_connection_pool_manager()
            client = await pool_manager.get_client(self.provider)
            
            url = self._build_stream_url(base_url, model, api_key)
            
            stream_kwargs = {
                'method': 'POST',
                'url': url,
                'json': payload,
                'headers': self._prepare_headers()
            }
            
            # 添加代理支持
            if settings.PROXIES:
                proxy = pool_manager._select_proxy(api_key)
                if proxy:
                    stream_kwargs['proxy'] = proxy
            
            async with client.stream(**stream_kwargs) as response:
                if response.status_code != 200:
                    error_content = await response.aread()
                    error_msg = error_content.decode("utf-8")
                    raise httpx.HTTPStatusError(
                        f"API call failed with status code {response.status_code}",
                        request=response.request,
                        response=response
                    )
                
                async for line in response.aiter_lines():
                    yield line
            
            # 更新路由统计（成功）
            if self.routing_enabled and selected_endpoint:
                end_time = time.time()
                response_time = end_time - start_time
                router = await get_intelligent_router()
                await router.release_endpoint(selected_endpoint, success=True, response_time=response_time)
                
        except Exception as e:
            # 更新路由统计（失败）
            if self.routing_enabled and selected_endpoint:
                router = await get_intelligent_router()
                await router.release_endpoint(selected_endpoint, success=False)
            
            logger.error(f"Enhanced streaming API client error: {str(e)}")
            raise
    
    async def get_models(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        获取模型列表，集成缓存和路由
        
        Args:
            api_key: API密钥
            
        Returns:
            Optional[Dict[str, Any]]: 模型列表
        """
        try:
            # 1. 检查缓存
            if self.cache_enabled:
                cache_key = f"models_list_{self.provider}"
                enhanced_cache = await get_enhanced_cache_service()
                cached_result = await enhanced_cache.get(cache_key, "default")
                
                if cached_result is not None:
                    logger.info(f"Cache hit for models list - {self.provider}")
                    return cached_result
            
            # 2. 智能路由选择端点
            selected_endpoint = None
            if self.routing_enabled:
                router = await get_intelligent_router()
                request_info = {"operation": "get_models", "priority": "low"}
                selected_endpoint = await router.route_request(self.provider, request_info)
                
                if not selected_endpoint:
                    logger.warning(f"No available endpoints for provider: {self.provider}")
                    return None
                
                base_url = selected_endpoint.url
            else:
                base_url = self._get_default_base_url()
            
            # 3. 发起请求
            pool_manager = await get_connection_pool_manager()
            url = self._build_models_url(base_url, api_key)
            
            response = await pool_manager.make_request(
                'GET', url,
                pool_type=self.provider,
                headers=self._prepare_headers(),
                api_key=api_key
            )
            
            response.raise_for_status()
            result = response.json()
            
            # 4. 缓存结果
            if self.cache_enabled and result:
                cache_key = f"models_list_{self.provider}"
                enhanced_cache = await get_enhanced_cache_service()
                await enhanced_cache.set(cache_key, result, "default", ttl=3600)  # 缓存1小时
            
            # 5. 更新路由统计
            if self.routing_enabled and selected_endpoint:
                router = await get_intelligent_router()
                await router.release_endpoint(selected_endpoint, success=True)
            
            return result
            
        except Exception as e:
            # 更新路由统计（失败）
            if self.routing_enabled and selected_endpoint:
                router = await get_intelligent_router()
                await router.release_endpoint(selected_endpoint, success=False)
            
            logger.error(f"Failed to get models for {self.provider}: {str(e)}")
            return None
    
    def _generate_cache_key(self, payload: Dict[str, Any], model: str) -> str:
        """生成缓存键"""
        import hashlib
        import json
        
        # 创建一个稳定的缓存键
        cache_data = {
            "model": model,
            "provider": self.provider,
            "payload": payload
        }
        
        cache_str = json.dumps(cache_data, sort_keys=True)
        return f"api_response_{hashlib.md5(cache_str.encode()).hexdigest()}"
    
    def _estimate_tokens(self, payload: Dict[str, Any]) -> int:
        """估算token数量"""
        # 简单的token估算逻辑
        content = str(payload)
        return len(content.split()) * 1.3  # 粗略估算
    
    def _prepare_headers(self) -> Dict[str, str]:
        """准备请求头"""
        headers = {}
        if settings.CUSTOM_HEADERS:
            headers.update(settings.CUSTOM_HEADERS)
        return headers
    
    def _get_default_base_url(self) -> str:
        """获取默认基础URL"""
        if self.provider == "gemini":
            return "https://generativelanguage.googleapis.com/v1beta"
        elif self.provider == "openai":
            return "https://api.openai.com/v1"
        elif self.provider == "vertex":
            return "https://us-central1-aiplatform.googleapis.com/v1"
        else:
            return "https://api.example.com/v1"
    
    def _build_url(self, base_url: str, model: str, api_key: str) -> str:
        """构建请求URL"""
        if self.provider == "gemini":
            return f"{base_url}/models/{model}:generateContent?key={api_key}"
        elif self.provider == "openai":
            return f"{base_url}/chat/completions"
        else:
            return f"{base_url}/generate"
    
    def _build_stream_url(self, base_url: str, model: str, api_key: str) -> str:
        """构建流式请求URL"""
        if self.provider == "gemini":
            return f"{base_url}/models/{model}:streamGenerateContent?alt=sse&key={api_key}"
        elif self.provider == "openai":
            return f"{base_url}/chat/completions"
        else:
            return f"{base_url}/stream"
    
    def _build_models_url(self, base_url: str, api_key: str) -> str:
        """构建模型列表URL"""
        if self.provider == "gemini":
            return f"{base_url}/models?key={api_key}&pageSize=1000"
        elif self.provider == "openai":
            return f"{base_url}/models"
        else:
            return f"{base_url}/models"


# 工厂函数
async def create_enhanced_api_client(provider: str, timeout: int = DEFAULT_TIMEOUT) -> EnhancedApiClient:
    """创建增强API客户端"""
    return EnhancedApiClient(provider, timeout)
