"""
缓存集成服务
将用户组缓存集成到聊天服务中
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from app.service.cache.user_group_cache import user_group_cache, CacheMode
from app.service.cache.intelligent_cache import intelligent_cache
from app.domain.openai_models import ChatRequest
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class CacheIntegrationService:
    """
    缓存集成服务
    负责将用户组缓存集成到聊天流程中
    """
    
    def __init__(self):
        self.user_group_cache = user_group_cache
        self.intelligent_cache = intelligent_cache
        
        # 缓存策略配置
        self.cache_strategies = {
            "enable_user_group_cache": True,
            "enable_fallback_cache": True,
            "cache_response_threshold": 100,  # 响应长度阈值
            "cache_token_threshold": 50,      # 令牌数阈值
        }
    
    async def get_cached_response(
        self, 
        request: ChatRequest, 
        group_id: Optional[str] = None,
        api_key: str = "",
        estimated_tokens: int = 0
    ) -> Optional[Any]:
        """
        获取缓存的响应
        
        Args:
            request: 聊天请求
            group_id: 用户组ID
            api_key: API密钥
            estimated_tokens: 预估令牌数
            
        Returns:
            Optional[Any]: 缓存的响应，如果没有则返回None
        """
        try:
            if group_id:
                group_config = await self.user_group_cache.get_group_config(group_id)
                # 如果用户组禁用了缓存，则不使用缓存
                if group_config.cache_mode == CacheMode.DISABLED:
                    return None
            
            if not self.cache_strategies["enable_user_group_cache"]:
                return None
            
            # 准备请求数据
            request_data = self._prepare_request_data(request)
            
            # 优先使用用户组缓存
            if group_id:
                cached_response = await self.user_group_cache.get(
                    group_id, request_data, request.model, api_key
                )
                
                if cached_response:
                    logger.info(f"Cache hit from user group cache for group {group_id}")
                    return cached_response
            
            # 回退到智能缓存
            if self.cache_strategies["enable_fallback_cache"]:
                cached_response = self.intelligent_cache.get(request_data, request.model, api_key)
                
                if cached_response:
                    logger.info(f"Cache hit from intelligent cache")
                    return cached_response
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached response: {str(e)}")
            return None
    
    async def cache_response(
        self, 
        request: ChatRequest, 
        response: Any,
        group_id: Optional[str] = None,
        api_key: str = "",
        token_count: int = 0
    ) -> bool:
        """
        缓存响应
        
        Args:
            request: 聊天请求
            response: 响应数据
            group_id: 用户组ID
            api_key: API密钥
            token_count: 令牌数
            
        Returns:
            bool: 缓存是否成功
        """
        try:
            if group_id:
                group_config = await self.user_group_cache.get_group_config(group_id)
                # 如果用户组禁用了缓存，则不缓存响应
                if group_config.cache_mode == CacheMode.DISABLED:
                    return False

            # 检查是否应该缓存
            if not self._should_cache_response(response, token_count):
                return False
            
            # 准备请求数据
            request_data = self._prepare_request_data(request)
            
            # 优先使用用户组缓存
            if group_id and self.cache_strategies["enable_user_group_cache"]:
                success = await self.user_group_cache.set(
                    group_id, request_data, request.model, api_key, response, token_count
                )
                
                if success:
                    logger.debug(f"Response cached in user group cache for group {group_id}")
                    return True
            
            # 回退到智能缓存
            if self.cache_strategies["enable_fallback_cache"]:
                success = self.intelligent_cache.set(
                    request_data, request.model, api_key, response, token_count
                )
                
                if success:
                    logger.debug(f"Response cached in intelligent cache")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error caching response: {str(e)}")
            return False
    
    def _prepare_request_data(self, request: ChatRequest) -> Dict[str, Any]:
        """准备请求数据用于缓存"""
        return {
            "messages": request.messages,
            "model": request.model,
            "temperature": getattr(request, 'temperature', 0.7),
            "max_tokens": getattr(request, 'max_tokens', None),
            "system_instruction": getattr(request, 'system_instruction', None),
            "top_p": getattr(request, 'top_p', None),
            "top_k": getattr(request, 'top_k', None),
        }
    
    def _should_cache_response(self, response: Any, token_count: int) -> bool:
        """判断是否应该缓存响应"""
        try:
            # 检查令牌数阈值
            if token_count < self.cache_strategies["cache_token_threshold"]:
                return False
            
            # 检查响应长度
            response_text = ""
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]
                if hasattr(choice, 'message') and choice.message:
                    response_text = choice.message.content or ""
            
            if len(response_text) < self.cache_strategies["cache_response_threshold"]:
                return False
            
            # 检查响应质量（简化检查）
            if not response_text.strip():
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking if should cache response: {str(e)}")
            return False
    
    async def get_cache_statistics(self, group_id: Optional[str] = None) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            stats = {}
            
            # 获取用户组缓存统计
            if group_id:
                stats["user_group_cache"] = await self.user_group_cache.get_group_cache_stats(group_id)
            else:
                stats["user_group_cache"] = await self.user_group_cache.get_global_cache_stats()
            
            # 获取智能缓存统计
            stats["intelligent_cache"] = self.intelligent_cache.get_stats()
            
            # 计算综合统计
            stats["combined_stats"] = self._calculate_combined_stats(stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting cache statistics: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_combined_stats(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合统计"""
        try:
            ug_stats = stats.get("user_group_cache", {}).get("statistics", {})
            ic_stats = stats.get("intelligent_cache", {})
            
            total_hits = ug_stats.get("hits", 0) + ic_stats.get("hits", 0)
            total_misses = ug_stats.get("misses", 0) + ic_stats.get("misses", 0)
            total_requests = total_hits + total_misses
            
            combined_hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "total_hits": total_hits,
                "total_misses": total_misses,
                "total_requests": total_requests,
                "combined_hit_rate": round(combined_hit_rate, 2),
                "cache_efficiency": "high" if combined_hit_rate > 70 else "medium" if combined_hit_rate > 40 else "low"
            }
            
        except Exception as e:
            logger.error(f"Error calculating combined stats: {str(e)}")
            return {}
    
    async def clear_group_cache(self, group_id: str) -> Dict[str, Any]:
        """清除用户组缓存"""
        try:
            # 清除用户组缓存
            ug_cleared = await self.user_group_cache.clear_group_cache(group_id)
            
            # 清除智能缓存中相关的条目（简化实现）
            ic_cleared = 0  # 智能缓存没有按用户组清除的功能
            
            return {
                "group_id": group_id,
                "user_group_cache_cleared": ug_cleared,
                "intelligent_cache_cleared": ic_cleared,
                "total_cleared": ug_cleared + ic_cleared,
                "cleared_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error clearing cache for group {group_id}: {str(e)}")
            return {"group_id": group_id, "error": str(e)}
    
    async def update_cache_config(
        self, 
        group_id: str, 
        config_updates: Dict[str, Any]
    ) -> bool:
        """更新缓存配置"""
        try:
            success = await self.user_group_cache.update_group_config(group_id, config_updates)
            
            if success:
                logger.info(f"Updated cache config for group {group_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating cache config for group {group_id}: {str(e)}")
            return False
    
    async def warm_up_cache(
        self, 
        group_id: str, 
        common_requests: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """预热缓存"""
        try:
            warmed_count = 0
            failed_count = 0
            
            for request_data in common_requests:
                try:
                    # 这里应该调用实际的API来获取响应并缓存
                    # 目前只是模拟预热过程
                    logger.debug(f"Warming up cache for group {group_id}: {request_data.get('model', 'unknown')}")
                    warmed_count += 1
                except Exception as e:
                    logger.warning(f"Failed to warm up cache entry: {str(e)}")
                    failed_count += 1
            
            return {
                "group_id": group_id,
                "warmed_count": warmed_count,
                "failed_count": failed_count,
                "total_requests": len(common_requests),
                "warmed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error warming up cache for group {group_id}: {str(e)}")
            return {"group_id": group_id, "error": str(e)}
    
    async def analyze_cache_performance(self, group_id: Optional[str] = None) -> Dict[str, Any]:
        """分析缓存性能"""
        try:
            stats = await self.get_cache_statistics(group_id)
            
            # 分析缓存效率
            analysis = {
                "cache_health": "unknown",
                "recommendations": [],
                "performance_metrics": {}
            }
            
            if "combined_stats" in stats:
                combined = stats["combined_stats"]
                hit_rate = combined.get("combined_hit_rate", 0)
                
                # 健康状态评估
                if hit_rate > 80:
                    analysis["cache_health"] = "excellent"
                elif hit_rate > 60:
                    analysis["cache_health"] = "good"
                elif hit_rate > 40:
                    analysis["cache_health"] = "fair"
                else:
                    analysis["cache_health"] = "poor"
                
                # 性能指标
                analysis["performance_metrics"] = {
                    "hit_rate": hit_rate,
                    "efficiency_rating": combined.get("cache_efficiency", "unknown"),
                    "total_requests": combined.get("total_requests", 0)
                }
                
                # 建议
                if hit_rate < 50:
                    analysis["recommendations"].append("Consider enabling semantic caching")
                    analysis["recommendations"].append("Review cache TTL settings")
                
                if hit_rate < 30:
                    analysis["recommendations"].append("Consider increasing cache size")
                    analysis["recommendations"].append("Enable cross-model caching if appropriate")
            
            analysis["analyzed_at"] = datetime.now().isoformat()
            analysis["group_id"] = group_id
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing cache performance: {str(e)}")
            return {"error": str(e)}

    async def get_global_cache_config(self) -> Dict[str, Any]:
        """获取全局缓存配置"""
        return {
            "cache_enabled": self.cache_strategies.get("enable_user_group_cache", True),
            "default_ttl": 3600,
            "max_cache_size": 1000,
            "enable_semantic_cache": True,
            "cache_response_threshold": self.cache_strategies.get("cache_response_threshold", 100),
            "cache_token_threshold": self.cache_strategies.get("cache_token_threshold", 50)
        }

    async def update_global_cache_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新全局缓存配置"""
        # 更新缓存策略
        if "cache_enabled" in config:
            self.cache_strategies["enable_user_group_cache"] = config["cache_enabled"]
        if "cache_response_threshold" in config:
            self.cache_strategies["cache_response_threshold"] = config["cache_response_threshold"]
        if "cache_token_threshold" in config:
            self.cache_strategies["cache_token_threshold"] = config["cache_token_threshold"]

        logger.info(f"Updated global cache config: {config}")

        return {
            "status": "success",
            "message": "全局缓存配置已更新",
            "updated_at": datetime.now().isoformat()
        }


# 全局实例
cache_integration_service = CacheIntegrationService()
