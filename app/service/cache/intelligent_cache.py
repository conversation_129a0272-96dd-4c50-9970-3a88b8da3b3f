import hashlib
import json
import time
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from app.log.logger import Logger

logger = Logger.setup_logger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    model: str
    api_key_hash: str
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: int
    content_hash: str
    token_count: int
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl_seconds)
    
    def is_stale(self, max_age_hours: int = 24) -> bool:
        """检查是否过时"""
        return datetime.now() > self.created_at + timedelta(hours=max_age_hours)


class IntelligentCache:
    """智能缓存系统"""
    
    def __init__(self, max_size: int = 10000, default_ttl: int = 3600):
        self.cache: Dict[str, CacheEntry] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        # 缓存策略配置
        self.cache_strategies = {
            "exact_match": True,      # 精确匹配缓存
            "semantic_similar": True, # 语义相似缓存
            "partial_match": True,    # 部分匹配缓存
            "model_agnostic": False,  # 跨模型缓存
        }
        
        # 统计信息
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "saves": 0,
            "api_calls_saved": 0,
            "tokens_saved": 0
        }
    
    def _generate_cache_key(self, request_data: Dict[str, Any], model: str, api_key: str) -> str:
        """生成缓存键"""
        # 提取关键信息
        cache_data = {
            "messages": request_data.get("messages", []),
            "model": model,
            "temperature": request_data.get("temperature", 0.7),
            "max_tokens": request_data.get("max_tokens"),
            "system_instruction": request_data.get("system_instruction"),
        }
        
        # 生成内容哈希
        content_str = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        content_hash = hashlib.md5(content_str.encode()).hexdigest()
        
        # API Key哈希（用于隔离不同用户的缓存）
        api_key_hash = hashlib.md5(api_key.encode()).hexdigest()[:8]
        
        return f"{api_key_hash}:{model}:{content_hash}"
    
    def _generate_content_hash(self, request_data: Dict[str, Any]) -> str:
        """生成内容哈希（用于语义相似性检查）"""
        messages = request_data.get("messages", [])
        if not messages:
            return ""
        
        # 提取最后几条消息的内容
        recent_messages = messages[-3:] if len(messages) > 3 else messages
        content = " ".join([msg.get("content", "") for msg in recent_messages])
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, request_data: Dict[str, Any], model: str, api_key: str) -> Optional[Any]:
        """获取缓存"""
        if not self.cache_strategies["exact_match"]:
            return None
        
        cache_key = self._generate_cache_key(request_data, model, api_key)
        
        # 精确匹配
        if cache_key in self.cache:
            entry = self.cache[cache_key]
            
            if entry.is_expired():
                del self.cache[cache_key]
                logger.debug(f"Cache entry expired and removed: {cache_key}")
                return None
            
            # 更新访问信息
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            self.stats["hits"] += 1
            self.stats["api_calls_saved"] += 1
            self.stats["tokens_saved"] += entry.token_count
            
            logger.info(f"Cache hit: {cache_key} (accessed {entry.access_count} times)")
            return entry.value
        
        # 尝试语义相似匹配
        if self.cache_strategies["semantic_similar"]:
            similar_entry = self._find_similar_entry(request_data, model, api_key)
            if similar_entry:
                self.stats["hits"] += 1
                self.stats["api_calls_saved"] += 1
                self.stats["tokens_saved"] += similar_entry.token_count
                logger.info(f"Cache hit (similar): {similar_entry.key}")
                return similar_entry.value
        
        self.stats["misses"] += 1
        return None
    
    def _find_similar_entry(self, request_data: Dict[str, Any], model: str, api_key: str) -> Optional[CacheEntry]:
        """查找语义相似的缓存条目"""
        content_hash = self._generate_content_hash(request_data)
        if not content_hash:
            return None
        
        api_key_hash = hashlib.md5(api_key.encode()).hexdigest()[:8]
        
        for entry in self.cache.values():
            if entry.is_expired():
                continue
            
            # 检查是否同一用户
            if not entry.key.startswith(api_key_hash):
                continue
            
            # 检查模型匹配（如果启用跨模型缓存则跳过）
            if not self.cache_strategies["model_agnostic"] and entry.model != model:
                continue
            
            # 检查内容相似性
            if entry.content_hash == content_hash:
                entry.last_accessed = datetime.now()
                entry.access_count += 1
                return entry
        
        return None
    
    def set(self, request_data: Dict[str, Any], model: str, api_key: str, response: Any, token_count: int = 0) -> None:
        """设置缓存"""
        cache_key = self._generate_cache_key(request_data, model, api_key)
        content_hash = self._generate_content_hash(request_data)
        api_key_hash = hashlib.md5(api_key.encode()).hexdigest()[:8]
        
        # 检查缓存大小，必要时清理
        if len(self.cache) >= self.max_size:
            self._evict_entries()
        
        # 创建缓存条目
        entry = CacheEntry(
            key=cache_key,
            value=response,
            model=model,
            api_key_hash=api_key_hash,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=0,
            ttl_seconds=self._calculate_ttl(request_data, token_count),
            content_hash=content_hash,
            token_count=token_count
        )
        
        self.cache[cache_key] = entry
        self.stats["saves"] += 1
        
        logger.info(f"Cache saved: {cache_key} (TTL: {entry.ttl_seconds}s, tokens: {token_count})")
    
    def _calculate_ttl(self, request_data: Dict[str, Any], token_count: int) -> int:
        """计算缓存TTL"""
        base_ttl = self.default_ttl
        
        # 根据内容类型调整TTL
        messages = request_data.get("messages", [])
        if messages:
            last_message = messages[-1].get("content", "").lower()
            
            # 时间敏感内容缩短TTL
            if any(keyword in last_message for keyword in ["今天", "现在", "最新", "实时"]):
                base_ttl = min(base_ttl, 300)  # 5分钟
            
            # 通用知识延长TTL
            elif any(keyword in last_message for keyword in ["什么是", "解释", "定义", "历史"]):
                base_ttl = max(base_ttl, 7200)  # 2小时
        
        # 根据token数量调整（长回复更有价值）
        if token_count > 1000:
            base_ttl = int(base_ttl * 1.5)
        
        return base_ttl
    
    def _evict_entries(self) -> None:
        """清理缓存条目"""
        # 移除过期条目
        expired_keys = [key for key, entry in self.cache.items() if entry.is_expired()]
        for key in expired_keys:
            del self.cache[key]
            self.stats["evictions"] += 1
        
        # 如果还是太多，移除最少使用的条目
        if len(self.cache) >= self.max_size:
            # 按访问次数和最后访问时间排序
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: (x[1].access_count, x[1].last_accessed)
            )
            
            # 移除最少使用的25%
            remove_count = len(sorted_entries) // 4
            for key, _ in sorted_entries[:remove_count]:
                del self.cache[key]
                self.stats["evictions"] += 1
        
        logger.info(f"Cache eviction completed. Current size: {len(self.cache)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "cache_size": len(self.cache),
            "max_size": self.max_size,
            "hit_rate": f"{hit_rate:.2f}%",
            "total_hits": self.stats["hits"],
            "total_misses": self.stats["misses"],
            "total_saves": self.stats["saves"],
            "total_evictions": self.stats["evictions"],
            "api_calls_saved": self.stats["api_calls_saved"],
            "tokens_saved": self.stats["tokens_saved"],
            "estimated_cost_saved": self.stats["tokens_saved"] * 0.00001,  # 估算节省成本
        }
    
    def clear_expired(self) -> int:
        """清理过期缓存"""
        expired_keys = [key for key, entry in self.cache.items() if entry.is_expired()]
        for key in expired_keys:
            del self.cache[key]
        
        logger.info(f"Cleared {len(expired_keys)} expired cache entries")
        return len(expired_keys)
    
    def clear_all(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
        self.stats = {key: 0 for key in self.stats}
        logger.info("All cache cleared")


# 全局缓存实例
intelligent_cache = IntelligentCache()


class IntelligentCacheService:
    """智能缓存服务 - 兼容性包装器"""

    def __init__(self):
        self.cache = IntelligentCache()

    async def get(self, key: str, user_group: str = "default") -> Optional[Any]:
        """获取缓存值"""
        return self.cache.get(key)

    async def set(self, key: str, value: Any, user_group: str = "default", ttl: int = None) -> bool:
        """设置缓存值"""
        # 模拟请求信息
        request_info = {
            "model": "default",
            "api_key": "default",
            "content": str(value)
        }
        return self.cache.set(key, value, request_info, ttl or self.cache.default_ttl)

    async def delete(self, key: str, user_group: str = "default") -> bool:
        """删除缓存"""
        return self.cache.delete(key)

    async def clear(self, user_group: str = "default") -> bool:
        """清空缓存"""
        self.cache.clear_all()
        return True

    def get_cache_analytics(self) -> Dict[str, Any]:
        """获取缓存分析"""
        return self.cache.get_analytics()

    async def optimize_cache_strategy(self, user_group: str) -> Dict[str, Any]:
        """优化缓存策略"""
        analytics = self.get_cache_analytics()

        recommendations = []
        hit_rate = analytics.get("hit_rate", 0)
        if hit_rate < 0.7:
            recommendations.append("考虑增加缓存TTL时间")
        if analytics.get("expired_entries", 0) > analytics.get("total_entries", 0) * 0.3:
            recommendations.append("优化缓存过期策略")
        if analytics.get("cache_size_mb", 0) > 100:
            recommendations.append("考虑增加缓存清理频率")

        return {
            "user_group": user_group,
            "current_performance": analytics,
            "recommendations": recommendations,
            "optimization_score": min(hit_rate * 100, 100)
        }
