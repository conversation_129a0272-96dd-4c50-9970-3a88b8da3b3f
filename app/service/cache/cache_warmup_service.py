"""
缓存预热服务
在系统启动或特定时间自动预热缓存，提升缓存命中率
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass

from app.service.cache.enhanced_cache_service import get_enhanced_cache_service
from app.service.client.api_client import GeminiApiClient, OpenAICompatibleApiClient
from app.database.services import get_user_groups, get_user_group_usage_stats
from app.log.logger import get_logger

logger = get_logger("cache_warmup")


@dataclass
class WarmupTask:
    """预热任务定义"""
    name: str
    user_group: str
    cache_key_pattern: str
    generator_func: Callable
    priority: int = 1  # 1=高优先级, 2=中优先级, 3=低优先级
    schedule: str = "startup"  # startup, hourly, daily
    enabled: bool = True


class CacheWarmupService:
    """缓存预热服务"""
    
    def __init__(self):
        self.warmup_tasks: List[WarmupTask] = []
        self.warmup_stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'last_warmup': None,
            'warmup_duration': 0
        }
        self.is_warming_up = False
        self._setup_default_tasks()
    
    def _setup_default_tasks(self):
        """设置默认预热任务"""
        # 模型列表预热
        self.warmup_tasks.append(WarmupTask(
            name="models_list_warmup",
            user_group="*",  # 所有用户组
            cache_key_pattern="models_list_{user_group}",
            generator_func=self._warmup_models_list,
            priority=1,
            schedule="startup"
        ))
        
        # 常用模型能力预热
        self.warmup_tasks.append(WarmupTask(
            name="model_capabilities_warmup",
            user_group="*",
            cache_key_pattern="model_capabilities_{model}_{user_group}",
            generator_func=self._warmup_model_capabilities,
            priority=2,
            schedule="startup"
        ))
        
        # 用户组配置预热
        self.warmup_tasks.append(WarmupTask(
            name="user_group_config_warmup",
            user_group="*",
            cache_key_pattern="user_group_config_{user_group}",
            generator_func=self._warmup_user_group_config,
            priority=1,
            schedule="startup"
        ))
        
        # 热门请求模板预热
        self.warmup_tasks.append(WarmupTask(
            name="popular_templates_warmup",
            user_group="*",
            cache_key_pattern="popular_templates_{user_group}",
            generator_func=self._warmup_popular_templates,
            priority=3,
            schedule="hourly"
        ))
    
    async def start_warmup(self, schedule_type: str = "startup") -> Dict[str, Any]:
        """开始缓存预热"""
        if self.is_warming_up:
            return {"status": "already_warming_up", "message": "Cache warmup is already in progress"}
        
        self.is_warming_up = True
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting cache warmup for schedule: {schedule_type}")
            
            # 获取符合条件的预热任务
            tasks_to_run = [task for task in self.warmup_tasks 
                           if task.enabled and task.schedule == schedule_type]
            
            # 按优先级排序
            tasks_to_run.sort(key=lambda x: x.priority)
            
            # 获取用户组列表
            user_groups = await self._get_active_user_groups()
            
            self.warmup_stats['total_tasks'] = len(tasks_to_run) * len(user_groups)
            self.warmup_stats['completed_tasks'] = 0
            self.warmup_stats['failed_tasks'] = 0
            
            # 执行预热任务
            for task in tasks_to_run:
                if task.user_group == "*":
                    # 为所有用户组执行
                    for user_group in user_groups:
                        await self._execute_warmup_task(task, user_group)
                else:
                    # 为特定用户组执行
                    await self._execute_warmup_task(task, task.user_group)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.warmup_stats['last_warmup'] = start_time.isoformat()
            self.warmup_stats['warmup_duration'] = duration
            
            logger.info(f"Cache warmup completed in {duration:.2f} seconds")
            
            return {
                "status": "completed",
                "duration_seconds": duration,
                "stats": self.warmup_stats.copy()
            }
        
        except Exception as e:
            logger.error(f"Cache warmup failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "stats": self.warmup_stats.copy()
            }
        
        finally:
            self.is_warming_up = False
    
    async def _execute_warmup_task(self, task: WarmupTask, user_group: str):
        """执行单个预热任务"""
        try:
            logger.debug(f"Executing warmup task: {task.name} for user group: {user_group}")
            
            # 生成缓存键
            cache_key = task.cache_key_pattern.format(user_group=user_group)
            
            # 执行生成器函数
            await task.generator_func(user_group, cache_key)
            
            self.warmup_stats['completed_tasks'] += 1
            
        except Exception as e:
            logger.error(f"Warmup task {task.name} failed for user group {user_group}: {str(e)}")
            self.warmup_stats['failed_tasks'] += 1
    
    async def _get_active_user_groups(self) -> List[str]:
        """获取活跃的用户组列表"""
        try:
            # 获取所有用户组
            user_groups_data = await get_user_groups()
            
            # 过滤活跃用户组（有API密钥且状态为活跃）
            active_groups = []
            for group in user_groups_data:
                if group.get('status') == 'active' and group.get('api_keys'):
                    active_groups.append(group['group_id'])
            
            # 如果没有活跃用户组，返回默认组
            if not active_groups:
                active_groups = ['default']
            
            return active_groups[:10]  # 限制最多10个用户组进行预热
        
        except Exception as e:
            logger.error(f"Failed to get active user groups: {str(e)}")
            return ['default']
    
    async def _warmup_models_list(self, user_group: str, cache_key: str):
        """预热模型列表"""
        try:
            enhanced_cache = await get_enhanced_cache_service()
            
            # 检查是否已缓存
            existing = await enhanced_cache.get(cache_key, user_group)
            if existing is not None:
                return
            
            # 模拟获取模型列表（实际应该调用真实API）
            models_data = {
                "models": [
                    {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "capabilities": ["text", "image"]},
                    {"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "capabilities": ["text"]},
                    {"id": "gemini-1.0-pro", "name": "Gemini 1.0 Pro", "capabilities": ["text"]},
                ],
                "cached_at": datetime.now().isoformat()
            }
            
            # 缓存模型列表
            await enhanced_cache.set(cache_key, models_data, user_group, ttl=3600)
            logger.debug(f"Warmed up models list for user group: {user_group}")
        
        except Exception as e:
            logger.error(f"Failed to warmup models list for {user_group}: {str(e)}")
    
    async def _warmup_model_capabilities(self, user_group: str, cache_key: str):
        """预热模型能力信息"""
        try:
            enhanced_cache = await get_enhanced_cache_service()
            
            # 常用模型列表
            popular_models = ["gemini-1.5-pro", "gemini-1.5-flash", "gpt-4", "gpt-3.5-turbo"]
            
            for model in popular_models:
                model_cache_key = f"model_capabilities_{model}_{user_group}"
                
                # 检查是否已缓存
                existing = await enhanced_cache.get(model_cache_key, user_group)
                if existing is not None:
                    continue
                
                # 模拟模型能力数据
                capabilities_data = {
                    "model": model,
                    "max_tokens": 8192 if "gemini" in model else 4096,
                    "supports_streaming": True,
                    "supports_images": "pro" in model.lower(),
                    "supports_functions": True,
                    "cached_at": datetime.now().isoformat()
                }
                
                await enhanced_cache.set(model_cache_key, capabilities_data, user_group, ttl=7200)
            
            logger.debug(f"Warmed up model capabilities for user group: {user_group}")
        
        except Exception as e:
            logger.error(f"Failed to warmup model capabilities for {user_group}: {str(e)}")
    
    async def _warmup_user_group_config(self, user_group: str, cache_key: str):
        """预热用户组配置"""
        try:
            enhanced_cache = await get_enhanced_cache_service()
            
            # 检查是否已缓存
            existing = await enhanced_cache.get(cache_key, user_group)
            if existing is not None:
                return
            
            # 模拟用户组配置数据
            config_data = {
                "group_id": user_group,
                "rate_limits": {
                    "requests_per_minute": 60,
                    "tokens_per_day": 100000
                },
                "allowed_models": ["gemini-1.5-pro", "gemini-1.5-flash"],
                "features": {
                    "streaming": True,
                    "image_analysis": True,
                    "function_calling": True
                },
                "cached_at": datetime.now().isoformat()
            }
            
            await enhanced_cache.set(cache_key, config_data, user_group, ttl=1800)
            logger.debug(f"Warmed up user group config for: {user_group}")
        
        except Exception as e:
            logger.error(f"Failed to warmup user group config for {user_group}: {str(e)}")
    
    async def _warmup_popular_templates(self, user_group: str, cache_key: str):
        """预热热门请求模板"""
        try:
            enhanced_cache = await get_enhanced_cache_service()
            
            # 检查是否已缓存
            existing = await enhanced_cache.get(cache_key, user_group)
            if existing is not None:
                return
            
            # 模拟热门模板数据
            templates_data = {
                "templates": [
                    {
                        "id": "summarize",
                        "name": "文本摘要",
                        "prompt": "请总结以下内容：",
                        "usage_count": 150
                    },
                    {
                        "id": "translate",
                        "name": "翻译",
                        "prompt": "请将以下内容翻译成中文：",
                        "usage_count": 120
                    },
                    {
                        "id": "code_review",
                        "name": "代码审查",
                        "prompt": "请审查以下代码并提供改进建议：",
                        "usage_count": 80
                    }
                ],
                "cached_at": datetime.now().isoformat()
            }
            
            await enhanced_cache.set(cache_key, templates_data, user_group, ttl=3600)
            logger.debug(f"Warmed up popular templates for user group: {user_group}")
        
        except Exception as e:
            logger.error(f"Failed to warmup popular templates for {user_group}: {str(e)}")
    
    def add_custom_warmup_task(self, task: WarmupTask):
        """添加自定义预热任务"""
        self.warmup_tasks.append(task)
        logger.info(f"Added custom warmup task: {task.name}")
    
    def remove_warmup_task(self, task_name: str) -> bool:
        """移除预热任务"""
        for i, task in enumerate(self.warmup_tasks):
            if task.name == task_name:
                del self.warmup_tasks[i]
                logger.info(f"Removed warmup task: {task_name}")
                return True
        return False
    
    def get_warmup_status(self) -> Dict[str, Any]:
        """获取预热状态"""
        return {
            "is_warming_up": self.is_warming_up,
            "total_tasks_configured": len(self.warmup_tasks),
            "enabled_tasks": len([t for t in self.warmup_tasks if t.enabled]),
            "stats": self.warmup_stats.copy(),
            "tasks": [
                {
                    "name": task.name,
                    "user_group": task.user_group,
                    "priority": task.priority,
                    "schedule": task.schedule,
                    "enabled": task.enabled
                }
                for task in self.warmup_tasks
            ]
        }
    
    async def schedule_periodic_warmup(self):
        """调度周期性预热"""
        try:
            # 每小时预热
            await self.start_warmup("hourly")
            
            # 每天预热
            current_hour = datetime.now().hour
            if current_hour == 6:  # 每天早上6点
                await self.start_warmup("daily")
        
        except Exception as e:
            logger.error(f"Scheduled warmup failed: {str(e)}")


# 全局缓存预热服务实例
_cache_warmup_service = None

def get_cache_warmup_service() -> CacheWarmupService:
    """获取缓存预热服务实例"""
    global _cache_warmup_service
    if _cache_warmup_service is None:
        _cache_warmup_service = CacheWarmupService()
    return _cache_warmup_service
