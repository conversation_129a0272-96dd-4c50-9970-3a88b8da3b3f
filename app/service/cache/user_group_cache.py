"""
用户组缓存系统
基于IntelligentCache扩展，实现用户组级别的缓存隔离和共享
"""
import hashlib
import json
import time
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict

from app.service.cache.intelligent_cache import IntelligentCache, CacheEntry
from app.database.services import get_user_group
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class CacheMode(Enum):
    """缓存模式"""
    ISOLATED = "isolated"  # 完全隔离
    SHARED = "shared"  # 完全共享
    HYBRID = "hybrid"  # 混合模式（降级时共享）
    DISABLED = "disabled" # 禁用缓存


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    TTL = "ttl"  # 基于时间
    ADAPTIVE = "adaptive"  # 自适应


@dataclass
class GroupCacheEntry(CacheEntry):
    """用户组缓存条目"""
    group_id: str
    cache_mode: CacheMode
    shared_count: int = 0  # 跨组共享次数
    
    def can_share_with_group(self, target_group_id: str, mode: CacheMode) -> bool:
        """判断是否可以与目标用户组共享"""
        if self.group_id == target_group_id:
            return True
        
        if mode == CacheMode.SHARED or self.cache_mode == CacheMode.SHARED:
            return True
        
        if mode == CacheMode.HYBRID and self.cache_mode == CacheMode.HYBRID:
            # 混合模式下，允许有限的共享
            return self.shared_count < 3  # 最多共享3次
        
        return False


@dataclass
class GroupCacheConfig:
    """用户组缓存配置"""
    group_id: str
    cache_mode: CacheMode
    strategy: CacheStrategy
    max_size: int
    default_ttl: int
    enable_semantic_cache: bool
    enable_cross_model_cache: bool
    cache_priority: int  # 缓存优先级 1-10


class UserGroupCache:
    """
    用户组缓存系统
    支持用户组级别的缓存隔离和共享
    """
    
    def __init__(self, global_max_size: int = 50000):
        # 全局缓存存储
        self.cache: Dict[str, GroupCacheEntry] = {}
        self.global_max_size = global_max_size
        
        # 用户组配置缓存
        self.group_configs: Dict[str, GroupCacheConfig] = {}
        
        # 用户组缓存统计
        self.group_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: {
            "hits": 0, "misses": 0, "evictions": 0, "saves": 0,
            "shared_hits": 0, "cross_group_access": 0
        })
        
        # 缓存命名空间映射
        self.namespace_mapping: Dict[str, str] = {}
        
        # LRU访问顺序
        self.access_order: List[str] = []
        
        # 默认配置
        self.default_config = GroupCacheConfig(
            group_id="default",
            cache_mode=CacheMode.ISOLATED,
            strategy=CacheStrategy.LRU,
            max_size=1000,
            default_ttl=3600,
            enable_semantic_cache=True,
            enable_cross_model_cache=False,
            cache_priority=5
        )
    
    async def get_group_config(self, group_id: str) -> GroupCacheConfig:
        """获取用户组缓存配置"""
        if group_id not in self.group_configs:
            # 从数据库获取用户组配置
            user_group = await get_user_group(group_id)
            
            if user_group:
                cache_config = user_group.get("cache_config", {})
                self.group_configs[group_id] = GroupCacheConfig(
                    group_id=group_id,
                    cache_mode=CacheMode(cache_config.get("cache_mode", "isolated")),
                    strategy=CacheStrategy(cache_config.get("strategy", "lru")),
                    max_size=cache_config.get("max_size", 1000),
                    default_ttl=cache_config.get("default_ttl", 3600),
                    enable_semantic_cache=cache_config.get("enable_semantic_cache", True),
                    enable_cross_model_cache=cache_config.get("enable_cross_model_cache", False),
                    cache_priority=cache_config.get("cache_priority", 5)
                )
            else:
                # 使用默认配置
                config = self.default_config
                config.group_id = group_id
                self.group_configs[group_id] = config
        
        return self.group_configs[group_id]
    
    def _generate_cache_key(
        self, 
        group_id: str, 
        request_data: Dict[str, Any], 
        model: str, 
        api_key: str
    ) -> str:
        """生成用户组缓存键"""
        # 生成基础内容哈希
        cache_data = {
            "messages": request_data.get("messages", []),
            "model": model,
            "temperature": request_data.get("temperature", 0.7),
            "max_tokens": request_data.get("max_tokens"),
            "system_instruction": request_data.get("system_instruction"),
        }
        
        content_str = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        content_hash = hashlib.md5(content_str.encode()).hexdigest()
        
        # API Key哈希
        api_key_hash = hashlib.md5(api_key.encode()).hexdigest()[:8]
        
        # 用户组命名空间
        return f"group:{group_id}:{api_key_hash}:{model}:{content_hash}"
    
    async def get(
        self, 
        group_id: str, 
        request_data: Dict[str, Any], 
        model: str, 
        api_key: str
    ) -> Optional[Any]:
        """获取缓存"""
        try:
            config = await self.get_group_config(group_id)
            cache_key = self._generate_cache_key(group_id, request_data, model, api_key)
            
            # 1. 尝试精确匹配
            if cache_key in self.cache:
                entry = self.cache[cache_key]
                
                if entry.is_expired():
                    await self._remove_cache_entry(cache_key)
                    return None
                
                # 更新访问信息
                entry.last_accessed = datetime.now()
                entry.access_count += 1
                self._update_access_order(cache_key)
                
                self.group_stats[group_id]["hits"] += 1
                logger.debug(f"Cache hit for group {group_id}: {cache_key}")
                return entry.value
            
            # 2. 尝试跨组共享缓存
            shared_entry = await self._find_shared_cache(group_id, request_data, model, api_key, config)
            if shared_entry:
                shared_entry.shared_count += 1
                shared_entry.last_accessed = datetime.now()
                self._update_access_order(shared_entry.key)
                
                self.group_stats[group_id]["shared_hits"] += 1
                self.group_stats[group_id]["cross_group_access"] += 1
                logger.info(f"Shared cache hit for group {group_id}: {shared_entry.key}")
                return shared_entry.value
            
            # 3. 尝试语义相似匹配
            if config.enable_semantic_cache:
                similar_entry = await self._find_similar_cache(group_id, request_data, model, api_key, config)
                if similar_entry:
                    similar_entry.last_accessed = datetime.now()
                    self._update_access_order(similar_entry.key)
                    
                    self.group_stats[group_id]["hits"] += 1
                    logger.info(f"Semantic cache hit for group {group_id}: {similar_entry.key}")
                    return similar_entry.value
            
            self.group_stats[group_id]["misses"] += 1
            return None
            
        except Exception as e:
            logger.error(f"Error getting cache for group {group_id}: {str(e)}")
            return None
    
    async def set(
        self, 
        group_id: str, 
        request_data: Dict[str, Any], 
        model: str, 
        api_key: str, 
        value: Any,
        token_count: int = 0
    ) -> bool:
        """设置缓存"""
        try:
            config = await self.get_group_config(group_id)
            cache_key = self._generate_cache_key(group_id, request_data, model, api_key)
            
            # 检查是否需要清理空间
            await self._ensure_cache_space(group_id, config)
            
            # 创建缓存条目
            content_hash = self._generate_content_hash(request_data)
            api_key_hash = hashlib.md5(api_key.encode()).hexdigest()[:8]
            
            entry = GroupCacheEntry(
                key=cache_key,
                value=value,
                model=model,
                api_key_hash=api_key_hash,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                ttl_seconds=config.default_ttl,
                content_hash=content_hash,
                token_count=token_count,
                group_id=group_id,
                cache_mode=config.cache_mode
            )
            
            self.cache[cache_key] = entry
            self._update_access_order(cache_key)
            
            self.group_stats[group_id]["saves"] += 1
            logger.debug(f"Cache saved for group {group_id}: {cache_key}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache for group {group_id}: {str(e)}")
            return False

    async def _find_shared_cache(
        self,
        group_id: str,
        request_data: Dict[str, Any],
        model: str,
        api_key: str,
        config: GroupCacheConfig
    ) -> Optional[GroupCacheEntry]:
        """查找可共享的缓存"""
        if config.cache_mode == CacheMode.ISOLATED:
            return None

        content_hash = self._generate_content_hash(request_data)
        if not content_hash:
            return None

        for entry in self.cache.values():
            if entry.is_expired() or entry.group_id == group_id:
                continue

            # 检查是否可以共享
            if not entry.can_share_with_group(group_id, config.cache_mode):
                continue

            # 检查内容相似性
            if entry.content_hash == content_hash and entry.model == model:
                return entry

        return None

    async def _find_similar_cache(
        self,
        group_id: str,
        request_data: Dict[str, Any],
        model: str,
        api_key: str,
        config: GroupCacheConfig
    ) -> Optional[GroupCacheEntry]:
        """查找语义相似的缓存"""
        content_hash = self._generate_content_hash(request_data)
        if not content_hash:
            return None

        api_key_hash = hashlib.md5(api_key.encode()).hexdigest()[:8]

        for entry in self.cache.values():
            if entry.is_expired():
                continue

            # 检查用户组权限
            if entry.group_id != group_id and not entry.can_share_with_group(group_id, config.cache_mode):
                continue

            # 检查API密钥匹配（同组内）
            if entry.group_id == group_id and entry.api_key_hash != api_key_hash:
                continue

            # 检查模型匹配
            if not config.enable_cross_model_cache and entry.model != model:
                continue

            # 简单的内容相似性检查
            if self._calculate_content_similarity(content_hash, entry.content_hash) > 0.8:
                return entry

        return None

    def _generate_content_hash(self, request_data: Dict[str, Any]) -> str:
        """生成内容哈希"""
        messages = request_data.get("messages", [])
        if not messages:
            return ""

        # 提取最后几条消息的内容
        recent_messages = messages[-3:] if len(messages) > 3 else messages
        content = " ".join([msg.get("content", "") for msg in recent_messages])
        return hashlib.md5(content.encode()).hexdigest()

    def _calculate_content_similarity(self, hash1: str, hash2: str) -> float:
        """计算内容相似性（简化实现）"""
        if hash1 == hash2:
            return 1.0

        # 简单的字符串相似性计算
        common_chars = sum(1 for a, b in zip(hash1, hash2) if a == b)
        return common_chars / max(len(hash1), len(hash2))

    async def _ensure_cache_space(self, group_id: str, config: GroupCacheConfig):
        """确保缓存空间充足"""
        # 检查全局缓存大小
        if len(self.cache) >= self.global_max_size:
            await self._evict_global_cache()

        # 检查用户组缓存大小
        group_cache_count = sum(1 for entry in self.cache.values() if entry.group_id == group_id)
        if group_cache_count >= config.max_size:
            await self._evict_group_cache(group_id, config)

    async def _evict_global_cache(self):
        """全局缓存清理"""
        # 按优先级和访问时间清理
        entries_to_remove = []

        for key, entry in self.cache.items():
            if entry.is_expired() or entry.is_stale(max_age_hours=48):
                entries_to_remove.append(key)

        # 如果过期清理不够，使用LRU策略
        if len(entries_to_remove) < self.global_max_size * 0.1:
            # 按最后访问时间排序，移除最旧的
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: x[1].last_accessed
            )

            additional_removals = int(self.global_max_size * 0.2) - len(entries_to_remove)
            entries_to_remove.extend([key for key, _ in sorted_entries[:additional_removals]])

        for key in entries_to_remove:
            await self._remove_cache_entry(key)

    async def _evict_group_cache(self, group_id: str, config: GroupCacheConfig):
        """用户组缓存清理"""
        group_entries = [(k, v) for k, v in self.cache.items() if v.group_id == group_id]

        if config.strategy == CacheStrategy.LRU:
            # 按最后访问时间排序
            group_entries.sort(key=lambda x: x[1].last_accessed)
        elif config.strategy == CacheStrategy.LFU:
            # 按访问次数排序
            group_entries.sort(key=lambda x: x[1].access_count)
        elif config.strategy == CacheStrategy.TTL:
            # 按创建时间排序
            group_entries.sort(key=lambda x: x[1].created_at)

        # 移除最旧的条目
        removal_count = len(group_entries) - config.max_size + 1
        for i in range(min(removal_count, len(group_entries))):
            key = group_entries[i][0]
            await self._remove_cache_entry(key)
            self.group_stats[group_id]["evictions"] += 1

    async def _remove_cache_entry(self, cache_key: str):
        """移除缓存条目"""
        if cache_key in self.cache:
            del self.cache[cache_key]

        if cache_key in self.access_order:
            self.access_order.remove(cache_key)

    def _update_access_order(self, cache_key: str):
        """更新访问顺序"""
        if cache_key in self.access_order:
            self.access_order.remove(cache_key)
        self.access_order.append(cache_key)

        # 保持访问顺序列表在合理大小
        if len(self.access_order) > self.global_max_size:
            self.access_order = self.access_order[-self.global_max_size:]

    async def clear_group_cache(self, group_id: str) -> int:
        """清除用户组缓存"""
        try:
            keys_to_remove = [key for key, entry in self.cache.items() if entry.group_id == group_id]

            for key in keys_to_remove:
                await self._remove_cache_entry(key)

            # 重置统计
            self.group_stats[group_id] = {
                "hits": 0, "misses": 0, "evictions": 0, "saves": 0,
                "shared_hits": 0, "cross_group_access": 0
            }

            logger.info(f"Cleared {len(keys_to_remove)} cache entries for group {group_id}")
            return len(keys_to_remove)

        except Exception as e:
            logger.error(f"Error clearing cache for group {group_id}: {str(e)}")
            return 0

    async def get_group_cache_stats(self, group_id: str) -> Dict[str, Any]:
        """获取用户组缓存统计"""
        try:
            config = await self.get_group_config(group_id)
            stats = self.group_stats[group_id].copy()

            # 计算缓存条目数
            cache_count = sum(1 for entry in self.cache.values() if entry.group_id == group_id)

            # 计算命中率
            total_requests = stats["hits"] + stats["misses"]
            hit_rate = (stats["hits"] / total_requests * 100) if total_requests > 0 else 0

            # 计算内存使用
            memory_usage = sum(
                len(str(entry.value)) for entry in self.cache.values()
                if entry.group_id == group_id
            )

            return {
                "group_id": group_id,
                "cache_mode": config.cache_mode.value,
                "strategy": config.strategy.value,
                "cache_count": cache_count,
                "max_size": config.max_size,
                "hit_rate": round(hit_rate, 2),
                "memory_usage_bytes": memory_usage,
                "statistics": stats,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting cache stats for group {group_id}: {str(e)}")
            return {"group_id": group_id, "error": str(e)}

    async def get_global_cache_stats(self) -> Dict[str, Any]:
        """获取全局缓存统计"""
        try:
            total_entries = len(self.cache)
            total_memory = sum(len(str(entry.value)) for entry in self.cache.values())

            # 按用户组分组统计
            group_breakdown = defaultdict(int)
            for entry in self.cache.values():
                group_breakdown[entry.group_id] += 1

            # 计算全局命中率
            total_hits = sum(stats["hits"] for stats in self.group_stats.values())
            total_misses = sum(stats["misses"] for stats in self.group_stats.values())
            total_requests = total_hits + total_misses
            global_hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0

            return {
                "total_entries": total_entries,
                "max_size": self.global_max_size,
                "utilization": round(total_entries / self.global_max_size * 100, 2),
                "total_memory_bytes": total_memory,
                "global_hit_rate": round(global_hit_rate, 2),
                "group_breakdown": dict(group_breakdown),
                "active_groups": len(self.group_stats),
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting global cache stats: {str(e)}")
            return {"error": str(e)}

    async def update_group_config(
        self,
        group_id: str,
        config_updates: Dict[str, Any]
    ) -> bool:
        """更新用户组缓存配置"""
        try:
            # 移除旧的配置，以便下次重新加载
            if group_id in self.group_configs:
                del self.group_configs[group_id]

            config = await self.get_group_config(group_id)

            # 更新配置
            if "cache_mode" in config_updates:
                config.cache_mode = CacheMode(config_updates["cache_mode"])
            if "strategy" in config_updates:
                config.strategy = CacheStrategy(config_updates["strategy"])
            if "max_size" in config_updates:
                config.max_size = int(config_updates["max_size"])
            if "default_ttl" in config_updates:
                config.default_ttl = int(config_updates["default_ttl"])
            if "enable_semantic_cache" in config_updates:
                config.enable_semantic_cache = bool(config_updates["enable_semantic_cache"])
            if "enable_cross_model_cache" in config_updates:
                config.enable_cross_model_cache = bool(config_updates["enable_cross_model_cache"])
            if "cache_priority" in config_updates:
                config.cache_priority = int(config_updates["cache_priority"])

            self.group_configs[group_id] = config
            logger.info(f"Updated cache config for group {group_id}")

            return True

        except Exception as e:
            logger.error(f"Error updating cache config for group {group_id}: {str(e)}")
            return False


class UserGroupCacheService:
    """用户组缓存服务 - 提供统一的缓存服务接口"""

    def __init__(self):
        self.cache = UserGroupCache()

    async def get(self, key: str, user_group: str = "default") -> Optional[Any]:
        """获取缓存值"""
        # 模拟请求信息
        request_data = {"content": key}
        return await self.cache.get(user_group, request_data, "default", "default")

    async def set(self, key: str, value: Any, user_group: str = "default", ttl: int = None) -> bool:
        """设置缓存值"""
        # 模拟请求信息
        request_data = {"content": key}
        return await self.cache.set(user_group, request_data, "default", "default", value, ttl)

    async def delete(self, key: str, user_group: str = "default") -> bool:
        """删除缓存"""
        return await self.cache.delete(key, user_group)

    async def clear(self, user_group: str = "default") -> bool:
        """清空用户组缓存"""
        return await self.cache.clear_group_cache(user_group)

    async def get_stats(self, user_group: str = "default") -> Dict[str, Any]:
        """获取用户组缓存统计"""
        return await self.cache.get_group_stats(user_group)

    async def get_global_stats(self) -> Dict[str, Any]:
        """获取全局缓存统计"""
        return await self.cache.get_global_stats()

    async def update_config(self, user_group: str, config_updates: Dict[str, Any]) -> bool:
        """更新用户组缓存配置"""
        return await self.cache.update_group_config(user_group, config_updates)

    async def get_config(self, user_group: str) -> Dict[str, Any]:
        """获取用户组缓存配置"""
        config = await self.cache.get_group_config(user_group)
        return {
            "cache_mode": config.cache_mode.value,
            "strategy": config.strategy.value,
            "max_size": config.max_size,
            "default_ttl": config.default_ttl,
            "enable_semantic_cache": config.enable_semantic_cache,
            "enable_cross_model_cache": config.enable_cross_model_cache,
            "cache_priority": config.cache_priority
        }


# 全局实例
user_group_cache = UserGroupCache()
user_group_cache_service = UserGroupCacheService()
