"""
增强缓存服务
实现预测性缓存、智能TTL调整、缓存预热等高级功能
"""
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
import hashlib
import pickle

from app.service.cache.intelligent_cache import IntelligentCacheService
from app.service.cache.user_group_cache import UserGroupCacheService
from app.log.logger import get_logger

logger = get_logger("enhanced_cache")


class CacheUsagePattern:
    """缓存使用模式分析"""
    
    def __init__(self):
        self.access_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.frequency_stats: Dict[str, int] = defaultdict(int)
        self.time_patterns: Dict[str, List[float]] = defaultdict(list)
        self.user_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
    
    def record_access(self, cache_key: str, user_group: str, timestamp: float = None):
        """记录缓存访问"""
        if timestamp is None:
            timestamp = time.time()
        
        self.access_history[cache_key].append({
            'timestamp': timestamp,
            'user_group': user_group
        })
        self.frequency_stats[cache_key] += 1
        self.time_patterns[cache_key].append(timestamp)
        
        # 更新用户组模式
        if user_group not in self.user_patterns:
            self.user_patterns[user_group] = {
                'keys': set(),
                'access_times': [],
                'frequency': 0
            }
        
        self.user_patterns[user_group]['keys'].add(cache_key)
        self.user_patterns[user_group]['access_times'].append(timestamp)
        self.user_patterns[user_group]['frequency'] += 1
    
    def get_hot_keys(self, limit: int = 50) -> List[Tuple[str, int]]:
        """获取热点缓存键"""
        return sorted(self.frequency_stats.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    def predict_next_access(self, user_group: str, current_time: float = None) -> List[str]:
        """预测用户组的下一次访问"""
        if current_time is None:
            current_time = time.time()
        
        if user_group not in self.user_patterns:
            return []
        
        pattern = self.user_patterns[user_group]
        recent_keys = list(pattern['keys'])
        
        # 基于访问频率和时间模式预测
        predictions = []
        for key in recent_keys:
            if key in self.time_patterns:
                times = self.time_patterns[key]
                if len(times) >= 2:
                    # 计算平均访问间隔
                    intervals = [times[i] - times[i-1] for i in range(1, len(times))]
                    avg_interval = sum(intervals) / len(intervals)
                    
                    # 预测下次访问时间
                    last_access = times[-1]
                    predicted_next = last_access + avg_interval
                    
                    # 如果预测时间接近当前时间，则认为可能很快被访问
                    if abs(predicted_next - current_time) < avg_interval * 0.5:
                        predictions.append(key)
        
        return predictions[:10]  # 返回前10个预测


class PredictiveCacheService:
    """预测性缓存服务"""
    
    def __init__(self, base_cache: IntelligentCacheService):
        self.base_cache = base_cache
        self.usage_pattern = CacheUsagePattern()
        self.preload_queue = asyncio.Queue()
        self.preload_task = None
        self.preload_stats = {
            'preloaded_count': 0,
            'hit_from_preload': 0,
            'preload_accuracy': 0.0
        }
        self._start_preload_worker()
    
    def _start_preload_worker(self):
        """启动预加载工作器"""
        try:
            self.preload_task = asyncio.create_task(self._preload_worker())
        except RuntimeError:
            # 如果没有运行的事件循环，稍后再启动
            pass
    
    async def _preload_worker(self):
        """预加载工作器"""
        while True:
            try:
                preload_item = await self.preload_queue.get()
                await self._execute_preload(preload_item)
                self.preload_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Preload worker error: {str(e)}")
                await asyncio.sleep(1)
    
    async def _execute_preload(self, preload_item: Dict[str, Any]):
        """执行预加载"""
        try:
            cache_key = preload_item['cache_key']
            user_group = preload_item['user_group']
            generator_func = preload_item['generator_func']
            generator_args = preload_item.get('generator_args', ())
            generator_kwargs = preload_item.get('generator_kwargs', {})
            
            # 检查是否已经缓存
            existing = await self.base_cache.get(cache_key, user_group)
            if existing is not None:
                return
            
            # 生成内容并缓存
            content = await generator_func(*generator_args, **generator_kwargs)
            if content is not None:
                await self.base_cache.set(cache_key, content, user_group)
                self.preload_stats['preloaded_count'] += 1
                logger.info(f"Preloaded cache for key: {cache_key}")
        
        except Exception as e:
            logger.error(f"Failed to execute preload for {preload_item.get('cache_key')}: {str(e)}")
    
    async def get(self, cache_key: str, user_group: str) -> Optional[Any]:
        """获取缓存内容"""
        # 记录访问模式
        self.usage_pattern.record_access(cache_key, user_group)
        
        # 从基础缓存获取
        result = await self.base_cache.get(cache_key, user_group)
        
        if result is not None:
            # 检查是否来自预加载
            if hasattr(result, '_preloaded'):
                self.preload_stats['hit_from_preload'] += 1
        else:
            # 缓存未命中，触发预测性预加载
            await self._trigger_predictive_preload(user_group)
        
        return result
    
    async def set(self, cache_key: str, content: Any, user_group: str, ttl: Optional[int] = None) -> bool:
        """设置缓存内容"""
        # 记录访问模式
        self.usage_pattern.record_access(cache_key, user_group)
        
        # 智能TTL调整
        if ttl is None:
            ttl = self._calculate_smart_ttl(cache_key, user_group)
        
        return await self.base_cache.set(cache_key, content, user_group, ttl)
    
    def _calculate_smart_ttl(self, cache_key: str, user_group: str) -> int:
        """计算智能TTL"""
        # 基于访问频率调整TTL
        frequency = self.usage_pattern.frequency_stats.get(cache_key, 0)
        
        if frequency >= 10:  # 高频访问
            return 3600  # 1小时
        elif frequency >= 5:  # 中频访问
            return 1800  # 30分钟
        else:  # 低频访问
            return 600   # 10分钟
    
    async def _trigger_predictive_preload(self, user_group: str):
        """触发预测性预加载"""
        try:
            # 获取预测的缓存键
            predicted_keys = self.usage_pattern.predict_next_access(user_group)
            
            for cache_key in predicted_keys:
                # 检查是否已经在预加载队列中
                if not self.preload_queue.full():
                    # 这里需要根据实际业务逻辑定义生成器函数
                    # 暂时跳过，等待具体实现
                    pass
        
        except Exception as e:
            logger.error(f"Failed to trigger predictive preload: {str(e)}")
    
    async def schedule_preload(self, cache_key: str, user_group: str, 
                              generator_func, *args, **kwargs):
        """调度预加载任务"""
        preload_item = {
            'cache_key': cache_key,
            'user_group': user_group,
            'generator_func': generator_func,
            'generator_args': args,
            'generator_kwargs': kwargs
        }
        
        try:
            await self.preload_queue.put(preload_item)
        except asyncio.QueueFull:
            logger.warning(f"Preload queue full, skipping preload for {cache_key}")
    
    async def warm_up_cache(self, user_group: str, warm_up_data: List[Dict[str, Any]]):
        """缓存预热"""
        logger.info(f"Starting cache warm-up for user group: {user_group}")
        
        for item in warm_up_data:
            cache_key = item['cache_key']
            content = item['content']
            ttl = item.get('ttl')
            
            await self.set(cache_key, content, user_group, ttl)
        
        logger.info(f"Cache warm-up completed for user group: {user_group}, {len(warm_up_data)} items")
    
    def get_cache_analytics(self) -> Dict[str, Any]:
        """获取缓存分析数据"""
        hot_keys = self.usage_pattern.get_hot_keys()
        
        # 计算预加载准确率
        total_preloaded = self.preload_stats['preloaded_count']
        hit_from_preload = self.preload_stats['hit_from_preload']
        preload_accuracy = (hit_from_preload / max(total_preloaded, 1)) * 100
        
        return {
            'hot_keys': hot_keys,
            'user_patterns_count': len(self.usage_pattern.user_patterns),
            'total_access_records': sum(len(history) for history in self.usage_pattern.access_history.values()),
            'preload_stats': {
                **self.preload_stats,
                'preload_accuracy': preload_accuracy
            },
            'queue_size': self.preload_queue.qsize()
        }
    
    async def optimize_cache_strategy(self, user_group: str) -> Dict[str, Any]:
        """优化缓存策略"""
        if user_group not in self.usage_pattern.user_patterns:
            return {'message': 'No usage pattern found for user group'}
        
        pattern = self.usage_pattern.user_patterns[user_group]
        recommendations = []
        
        # 分析访问频率
        if pattern['frequency'] > 100:
            recommendations.append("高频用户组，建议增加缓存容量")
        
        # 分析访问时间模式
        access_times = pattern['access_times']
        if len(access_times) >= 10:
            # 计算访问时间的标准差，判断是否有规律
            import statistics
            time_intervals = [access_times[i] - access_times[i-1] for i in range(1, len(access_times))]
            if len(time_intervals) > 1:
                std_dev = statistics.stdev(time_intervals)
                avg_interval = statistics.mean(time_intervals)
                
                if std_dev / avg_interval < 0.3:  # 访问模式比较规律
                    recommendations.append("访问模式规律，建议启用预测性缓存")
        
        return {
            'user_group': user_group,
            'pattern_analysis': {
                'total_keys': len(pattern['keys']),
                'total_accesses': pattern['frequency'],
                'avg_access_interval': sum(access_times[i] - access_times[i-1] 
                                         for i in range(1, len(access_times))) / max(len(access_times) - 1, 1)
                                       if len(access_times) > 1 else 0
            },
            'recommendations': recommendations
        }
    
    async def cleanup(self):
        """清理资源"""
        if self.preload_task and not self.preload_task.done():
            self.preload_task.cancel()
            try:
                await self.preload_task
            except asyncio.CancelledError:
                pass


# 全局增强缓存服务实例
_enhanced_cache_service = None

async def get_enhanced_cache_service() -> PredictiveCacheService:
    """获取增强缓存服务实例"""
    global _enhanced_cache_service
    if _enhanced_cache_service is None:
        from app.service.cache.intelligent_cache import get_intelligent_cache_service
        base_cache = await get_intelligent_cache_service()
        _enhanced_cache_service = PredictiveCacheService(base_cache)
    return _enhanced_cache_service
