from typing import Dict, Optional, List, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from app.log.logger import Logger
from app.config.config import settings

logger = Logger.setup_logger(__name__)


@dataclass
class QuotaUsage:
    """配额使用情况"""
    model: str
    api_key: str
    requests_per_minute: int
    tokens_per_minute: int
    requests_per_day: int
    last_reset_time: datetime
    current_minute_requests: int
    current_minute_tokens: int
    current_day_requests: int


class QuotaMonitor:
    """智能配额监控器"""
    
    def __init__(self):
        self.usage_tracker: Dict[str, QuotaUsage] = {}
        
        # 免费层级配额限制
        self.QUOTA_LIMITS = {
            "gemini-2.5-pro": {"rpm": 5, "tpm": 250000, "rpd": 100},
            "gemini-2.5-flash": {"rpm": 10, "tpm": 250000, "rpd": 250},
            "gemini-2.5-flash-lite": {"rpm": 15, "tpm": 250000, "rpd": 1000},
            "gemini-2.0-flash": {"rpm": 15, "tpm": 1000000, "rpd": 200},
            "gemini-2.0-flash-lite": {"rpm": 30, "tpm": 1000000, "rpd": 200},
            "gemini-1.5-flash": {"rpm": 15, "tpm": 250000, "rpd": 50},
            "gemini-1.5-flash-8b": {"rpm": 15, "tpm": 250000, "rpd": 50},
            "gemini-1.5-pro": {"rpm": 5, "tpm": 250000, "rpd": 50},
        }
    
    def track_request(self, model: str, api_key: str, tokens_used: int = 0):
        """跟踪请求使用情况"""
        key = f"{model}:{api_key}"
        now = datetime.now()
        
        if key not in self.usage_tracker:
            self.usage_tracker[key] = QuotaUsage(
                model=model,
                api_key=api_key,
                requests_per_minute=0,
                tokens_per_minute=0,
                requests_per_day=0,
                last_reset_time=now,
                current_minute_requests=0,
                current_minute_tokens=0,
                current_day_requests=0
            )
        
        usage = self.usage_tracker[key]
        
        # 检查是否需要重置计数器
        self._reset_counters_if_needed(usage, now)
        
        # 更新使用情况
        usage.current_minute_requests += 1
        usage.current_minute_tokens += tokens_used
        usage.current_day_requests += 1
        
        logger.debug(f"Tracked request for {model}: {usage.current_minute_requests} RPM, {usage.current_minute_tokens} TPM")
    
    def _reset_counters_if_needed(self, usage: QuotaUsage, now: datetime):
        """根据时间重置计数器"""
        # 重置分钟计数器
        if now.minute != usage.last_reset_time.minute:
            usage.current_minute_requests = 0
            usage.current_minute_tokens = 0
        
        # 重置日计数器
        if now.date() != usage.last_reset_time.date():
            usage.current_day_requests = 0
        
        usage.last_reset_time = now
    
    def predict_quota_exhaustion(self, model: str, api_key: str) -> Optional[str]:
        """预测配额耗尽情况"""
        key = f"{model}:{api_key}"
        if key not in self.usage_tracker:
            return None
        
        usage = self.usage_tracker[key]
        limits = self.QUOTA_LIMITS.get(model, {})
        
        if not limits:
            return None
        
        warnings = []
        
        # 检查RPM
        if usage.current_minute_requests >= limits["rpm"] * 0.8:
            warnings.append(f"RPM接近限制: {usage.current_minute_requests}/{limits['rpm']}")
        
        # 检查TPM
        if usage.current_minute_tokens >= limits["tpm"] * 0.8:
            warnings.append(f"TPM接近限制: {usage.current_minute_tokens}/{limits['tpm']}")
        
        # 检查RPD
        if usage.current_day_requests >= limits["rpd"] * 0.8:
            warnings.append(f"RPD接近限制: {usage.current_day_requests}/{limits['rpd']}")
        
        return "; ".join(warnings) if warnings else None
    
    def is_quota_available(self, model: str, api_key: str, estimated_tokens: int = 1000) -> bool:
        """检查配额是否可用"""
        key = f"{model}:{api_key}"
        limits = self.QUOTA_LIMITS.get(model, {})
        
        if not limits:
            return True  # 未知模型，假设可用
        
        if key not in self.usage_tracker:
            return True  # 未使用过，肯定可用
        
        usage = self.usage_tracker[key]
        now = datetime.now()
        self._reset_counters_if_needed(usage, now)
        
        # 检查各项限制
        if usage.current_minute_requests >= limits["rpm"]:
            return False
        
        if usage.current_minute_tokens + estimated_tokens > limits["tpm"]:
            return False
        
        if usage.current_day_requests >= limits["rpd"]:
            return False
        
        return True
    
    def get_best_available_model(self, preferred_models: List[str], api_key: str) -> Optional[str]:
        """获取最佳可用模型"""
        for model in preferred_models:
            if self.is_quota_available(model, api_key):
                return model
        return None
    
    def get_usage_stats(self, model: str, api_key: str) -> Dict[str, Any]:
        """获取使用统计"""
        key = f"{model}:{api_key}"
        if key not in self.usage_tracker:
            return {"status": "no_data"}
        
        usage = self.usage_tracker[key]
        limits = self.QUOTA_LIMITS.get(model, {})
        
        return {
            "model": model,
            "api_key": api_key[-4:],  # 只显示后4位
            "current_minute_requests": usage.current_minute_requests,
            "current_minute_tokens": usage.current_minute_tokens,
            "current_day_requests": usage.current_day_requests,
            "limits": limits,
            "rpm_usage_percent": (usage.current_minute_requests / limits.get("rpm", 1)) * 100 if limits else 0,
            "tpm_usage_percent": (usage.current_minute_tokens / limits.get("tpm", 1)) * 100 if limits else 0,
            "rpd_usage_percent": (usage.current_day_requests / limits.get("rpd", 1)) * 100 if limits else 0,
        }


# 全局实例
quota_monitor = QuotaMonitor()
