from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from app.log.logger import Logger
from app.config.config import settings
from app.service.model.quota_monitor import quota_monitor

logger = Logger.setup_logger(__name__)


class ModelCapability(Enum):
    """模型能力枚举"""
    THINKING = "thinking"
    MULTIMODAL = "multimodal"
    LONG_CONTEXT = "long_context"
    CODE_EXECUTION = "code_execution"
    FUNCTION_CALLING = "function_calling"
    SEARCH_GROUNDING = "search_grounding"


@dataclass
class ModelSpec:
    """模型规格"""
    name: str
    performance_level: int
    rpm_limit: int
    tpm_limit: int
    rpd_limit: int
    capabilities: List[ModelCapability]
    context_window: int
    cost_efficiency: float  # 性价比评分 (1-10)
    
    
class SmartModelSelector:
    """智能模型选择器"""
    
    def __init__(self):
        self.model_specs = {
            "gemini-2.5-pro": ModelSpec(
                name="gemini-2.5-pro",
                performance_level=1,
                rpm_limit=5,
                tpm_limit=250000,
                rpd_limit=100,
                capabilities=[
                    ModelCapability.THINKING,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.LONG_CONTEXT,
                    ModelCapability.CODE_EXECUTION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.SEARCH_GROUNDING
                ],
                context_window=1048576,
                cost_efficiency=7.0
            ),
            "gemini-2.5-flash": ModelSpec(
                name="gemini-2.5-flash",
                performance_level=2,
                rpm_limit=10,
                tpm_limit=250000,
                rpd_limit=250,
                capabilities=[
                    ModelCapability.THINKING,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.LONG_CONTEXT,
                    ModelCapability.CODE_EXECUTION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.SEARCH_GROUNDING
                ],
                context_window=1048576,
                cost_efficiency=9.0
            ),
            "gemini-2.0-flash": ModelSpec(
                name="gemini-2.0-flash",
                performance_level=3,
                rpm_limit=15,
                tpm_limit=1000000,
                rpd_limit=200,
                capabilities=[
                    ModelCapability.MULTIMODAL,
                    ModelCapability.LONG_CONTEXT,
                    ModelCapability.CODE_EXECUTION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.SEARCH_GROUNDING
                ],
                context_window=1048576,
                cost_efficiency=8.5
            ),
            "gemini-2.5-flash-lite": ModelSpec(
                name="gemini-2.5-flash-lite",
                performance_level=4,
                rpm_limit=15,
                tpm_limit=250000,
                rpd_limit=1000,
                capabilities=[
                    ModelCapability.THINKING,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.SEARCH_GROUNDING
                ],
                context_window=1000000,
                cost_efficiency=9.5
            ),
            "gemini-2.0-flash-lite": ModelSpec(
                name="gemini-2.0-flash-lite",
                performance_level=5,
                rpm_limit=30,
                tpm_limit=1000000,
                rpd_limit=200,
                capabilities=[
                    ModelCapability.MULTIMODAL,
                    ModelCapability.FUNCTION_CALLING
                ],
                context_window=1048576,
                cost_efficiency=10.0
            ),
        }
    
    def select_best_model(
        self,
        required_capabilities: List[ModelCapability] = None,
        preferred_models: List[str] = None,
        api_key: str = None,
        estimated_tokens: int = 1000,
        prioritize_performance: bool = True
    ) -> Optional[str]:
        """
        选择最佳模型
        
        Args:
            required_capabilities: 必需的能力
            preferred_models: 首选模型列表
            api_key: API密钥
            estimated_tokens: 预估token数
            prioritize_performance: 是否优先考虑性能
            
        Returns:
            最佳模型名称
        """
        candidates = []
        
        # 筛选候选模型
        for model_name, spec in self.model_specs.items():
            # 检查能力要求
            if required_capabilities:
                if not all(cap in spec.capabilities for cap in required_capabilities):
                    continue
            
            # 检查配额可用性
            if api_key and not quota_monitor.is_quota_available(model_name, api_key, estimated_tokens):
                continue
            
            candidates.append(spec)
        
        if not candidates:
            logger.warning("No suitable models found with required capabilities and available quota")
            return None
        
        # 如果有首选模型，优先选择
        if preferred_models:
            for preferred in preferred_models:
                for candidate in candidates:
                    if candidate.name == preferred:
                        logger.info(f"Selected preferred model: {preferred}")
                        return preferred
        
        # 根据策略排序
        if prioritize_performance:
            # 优先性能：性能等级 > 性价比
            candidates.sort(key=lambda x: (x.performance_level, -x.cost_efficiency))
        else:
            # 优先性价比：性价比 > 性能等级
            candidates.sort(key=lambda x: (-x.cost_efficiency, x.performance_level))
        
        selected = candidates[0]
        logger.info(f"Selected best model: {selected.name} (performance: {selected.performance_level}, efficiency: {selected.cost_efficiency})")
        return selected.name
    
    def get_fallback_chain(
        self,
        original_model: str,
        required_capabilities: List[ModelCapability] = None,
        api_key: str = None
    ) -> List[str]:
        """
        获取降级链
        
        Args:
            original_model: 原始模型
            required_capabilities: 必需的能力
            api_key: API密钥
            
        Returns:
            降级模型链
        """
        if original_model not in self.model_specs:
            return []
        
        original_spec = self.model_specs[original_model]
        candidates = []
        
        # 筛选候选模型（性能等级更低的）
        for model_name, spec in self.model_specs.items():
            if spec.performance_level <= original_spec.performance_level:
                continue
            
            # 检查能力要求
            if required_capabilities:
                if not all(cap in spec.capabilities for cap in required_capabilities):
                    continue
            
            # 检查配额可用性
            if api_key and not quota_monitor.is_quota_available(model_name, api_key):
                continue
            
            candidates.append(spec)
        
        # 按性能等级排序（从高到低）
        candidates.sort(key=lambda x: x.performance_level)
        
        return [spec.name for spec in candidates]
    
    def analyze_model_suitability(
        self,
        model: str,
        request_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        分析模型适用性
        
        Args:
            model: 模型名称
            request_context: 请求上下文
            
        Returns:
            适用性分析结果
        """
        if model not in self.model_specs:
            return {"suitable": False, "reason": "Unknown model"}
        
        spec = self.model_specs[model]
        analysis = {
            "suitable": True,
            "model": model,
            "performance_level": spec.performance_level,
            "capabilities": [cap.value for cap in spec.capabilities],
            "warnings": [],
            "recommendations": []
        }
        
        # 检查上下文长度
        estimated_tokens = request_context.get("estimated_tokens", 0)
        if estimated_tokens > spec.context_window:
            analysis["suitable"] = False
            analysis["warnings"].append(f"Context too long: {estimated_tokens} > {spec.context_window}")
        
        # 检查能力需求
        required_caps = request_context.get("required_capabilities", [])
        missing_caps = [cap for cap in required_caps if cap not in spec.capabilities]
        if missing_caps:
            analysis["suitable"] = False
            analysis["warnings"].append(f"Missing capabilities: {missing_caps}")
        
        # 性能建议
        if spec.performance_level > 3:
            analysis["recommendations"].append("Consider using a higher performance model for better results")
        
        return analysis
    
    def get_model_comparison(self, models: List[str]) -> Dict[str, Any]:
        """获取模型对比"""
        comparison = {
            "models": [],
            "best_performance": None,
            "best_efficiency": None,
            "highest_quota": None
        }
        
        best_perf_level = float('inf')
        best_efficiency = 0
        highest_rpm = 0
        
        for model in models:
            if model not in self.model_specs:
                continue
            
            spec = self.model_specs[model]
            model_info = {
                "name": model,
                "performance_level": spec.performance_level,
                "cost_efficiency": spec.cost_efficiency,
                "rpm_limit": spec.rpm_limit,
                "tpm_limit": spec.tpm_limit,
                "rpd_limit": spec.rpd_limit,
                "capabilities": [cap.value for cap in spec.capabilities]
            }
            comparison["models"].append(model_info)
            
            # 找最佳性能
            if spec.performance_level < best_perf_level:
                best_perf_level = spec.performance_level
                comparison["best_performance"] = model
            
            # 找最佳性价比
            if spec.cost_efficiency > best_efficiency:
                best_efficiency = spec.cost_efficiency
                comparison["best_efficiency"] = model
            
            # 找最高配额
            if spec.rpm_limit > highest_rpm:
                highest_rpm = spec.rpm_limit
                comparison["highest_quota"] = model
        
        return comparison


# 全局实例
smart_model_selector = SmartModelSelector()
