from typing import Optional, List, Dict, Any
from app.log.logger import Logger
from app.config.config import settings

logger = Logger.setup_logger(__name__)


class ModelSwitchService:
    """模型切换服务，用于在模型失败时按性能顺序自动切换到下一个备用模型"""
    
    def __init__(self):
        # 模型性能等级（用于记录降级信息），数值越小，性能越高
        # 更新日期: 2024-07-23 - 根据用户指定的顺序进行排列
        self.MODEL_PERFORMANCE_LEVEL = {
            # --- 用户指定顺序 ---
            "gemini-1.5-pro-latest": 1,
            "gemini-1.5-pro": 2,
            "gemini-2.5-pro": 3,
            "gemini-1.5-flash-latest": 4,
            "gemini-1.5-flash": 5,
            "gemini-2.0-flash": 6,
            "gemini-2.5-flash": 7,

            # --- 补充的其他模型 (按性能) ---
            "gemini-pro": 8,
            "gemini-2.5-flash-lite": 9,
            "gemini-2.0-flash-lite": 10,
            "gemma-7b-it": 11,
            "gemma-2b-it": 12,
            "gemini-1.5-flash-8b": 13,
        }

        # 根据性能等级生成统一的、从高到低的降级模型列表
        self.PERFORMANCE_ORDERED_MODELS = sorted(self.MODEL_PERFORMANCE_LEVEL, key=self.MODEL_PERFORMANCE_LEVEL.get)

    def get_fallback_model(self, current_model: str, attempt: int = 0) -> Optional[str]:
        """
        获取下一个备用模型。
        逻辑：在统一的性能排序列表中找到当前模型，并返回列表中的下一个模型。
        
        Args:
            current_model: 当前失败的模型
            attempt: 尝试次数 (此参数在此逻辑中未使用，但保留以兼容接口)
            
        Returns:
            Optional[str]: 下一个备用模型名称，如果没有可用的备用模型则返回None
        """
        if not current_model:
            return None
            
        # 标准化模型名称
        normalized_model = self._normalize_model_name(current_model)
        
        try:
            # 找到当前模型在性能排序列表中的索引
            current_index = self.PERFORMANCE_ORDERED_MODELS.index(normalized_model)
        except ValueError:
            logger.warning(f"Model '{normalized_model}' not found in the performance-ordered list. Cannot determine fallback.")
            return None
            
        # 确定下一个模型的索引
        next_index = current_index + 1
        
        if next_index < len(self.PERFORMANCE_ORDERED_MODELS):
            fallback_model = self.PERFORMANCE_ORDERED_MODELS[next_index]
            
            # 记录模型切换信息
            current_level = self.MODEL_PERFORMANCE_LEVEL.get(normalized_model, 999)
            fallback_level = self.MODEL_PERFORMANCE_LEVEL.get(fallback_model, 999)
            
            logger.info(
                f"Model fallback triggered: {current_model} -> {fallback_model} "
                f"(performance level: {current_level} -> {fallback_level})"
            )
            
            return fallback_model
        else:
            logger.warning(
                f"Model '{current_model}' is the last in the performance list. No further fallback available."
            )
            return None
    
    def _normalize_model_name(self, model_name: str) -> str:
        """
        标准化模型名称，移除可能的前缀和后缀
        
        Args:
            model_name: 原始模型名称
            
        Returns:
            str: 标准化后的模型名称
        """
        if not model_name:
            return model_name
            
        # 移除 "models/" 前缀
        if model_name.startswith("models/"):
            model_name = model_name[7:]
        
        # 移除版本后缀，例如 gemini-1.5-pro-001 -> gemini-1.5-pro
        # 但保留 -latest
        if not model_name.endswith("-latest"):
            parts = model_name.split('-')
            if parts[-1].isdigit():
                model_name = '-'.join(parts[:-1])

        return model_name
    
    def is_model_switch_enabled(self) -> bool:
        """
        检查是否启用了模型切换功能
        
        Returns:
            bool: 是否启用模型切换
        """
        return getattr(settings, 'MODEL_SWITCH_ENABLED', True)
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict: 模型信息
        """
        normalized_model = self._normalize_model_name(model_name)
        
        try:
            current_index = self.PERFORMANCE_ORDERED_MODELS.index(normalized_model)
            fallback_models = self.PERFORMANCE_ORDERED_MODELS[current_index+1:]
        except ValueError:
            fallback_models = []

        return {
            "model_name": model_name,
            "normalized_name": normalized_model,
            "performance_level": self.MODEL_PERFORMANCE_LEVEL.get(normalized_model, 999),
            "has_fallbacks": bool(fallback_models),
            "fallback_count": len(fallback_models),
            "fallback_models": fallback_models
        }
    
    def should_attempt_fallback(self, current_model: str, attempt: int, max_attempts: int = 3) -> bool:
        """
        判断是否应该尝试模型降级
        
        Args:
            current_model: 当前模型
            attempt: 当前尝试次数
            max_attempts: 最大尝试次数
            
        Returns:
            bool: 是否应该尝试降级
        """
        if not self.is_model_switch_enabled():
            return False
            
        if attempt >= max_attempts:
            return False
            
        normalized_model = self._normalize_model_name(current_model)
        
        try:
            current_index = self.PERFORMANCE_ORDERED_MODELS.index(normalized_model)
            return current_index < len(self.PERFORMANCE_ORDERED_MODELS) - 1
        except ValueError:
            return False


# 全局实例
model_switch_service = ModelSwitchService()
