"""
增强安全服务
提供用户组级别的安全功能，包括访问控制、审计日志、异常检测等
"""
import asyncio
import hashlib
import time
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import ipaddress

from app.core.security import SecurityService
from app.database.services import get_user_group
from app.log.logger import get_security_logger

logger = get_security_logger()


class SecurityLevel(Enum):
    """安全级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatType(Enum):
    """威胁类型"""
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_IP = "suspicious_ip"
    UNUSUAL_PATTERN = "unusual_pattern"
    INVALID_ACCESS = "invalid_access"
    API_KEY_ABUSE = "api_key_abuse"


@dataclass
class SecurityEvent:
    """安全事件"""
    event_id: str
    group_id: str
    event_type: ThreatType
    severity: SecurityLevel
    source_ip: str
    user_agent: str
    timestamp: datetime
    details: Dict[str, Any]
    resolved: bool = False


@dataclass
class AccessRule:
    """访问规则"""
    rule_id: str
    group_id: str
    rule_type: str  # ip_whitelist, rate_limit, time_window
    rule_config: Dict[str, Any]
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class RateLimitConfig:
    """速率限制配置"""
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int


class EnhancedSecurityService:
    """
    增强安全服务
    提供用户组级别的安全保护
    """
    
    def __init__(self):
        self.base_security = SecurityService()
        
        # 用户组安全配置
        self.group_security_configs: Dict[str, Dict[str, Any]] = {}
        
        # 访问规则
        self.access_rules: Dict[str, List[AccessRule]] = defaultdict(list)
        
        # 速率限制跟踪
        self.rate_limit_tracker: Dict[str, Dict[str, deque]] = defaultdict(lambda: defaultdict(deque))
        
        # IP白名单/黑名单
        self.ip_whitelists: Dict[str, Set[str]] = defaultdict(set)
        self.ip_blacklists: Dict[str, Set[str]] = defaultdict(set)
        
        # 安全事件存储
        self.security_events: List[SecurityEvent] = []
        
        # 异常检测数据
        self.usage_patterns: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "hourly_requests": defaultdict(int),
            "model_usage": defaultdict(int),
            "ip_addresses": defaultdict(int),
            "user_agents": defaultdict(int)
        })
        
        # API密钥轮换跟踪
        self.key_rotation_tracker: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # 默认安全配置
        self.default_security_config = {
            "enable_ip_whitelist": False,
            "enable_rate_limiting": True,
            "enable_anomaly_detection": True,
            "enable_audit_logging": True,
            "rate_limit": RateLimitConfig(
                requests_per_minute=100,
                requests_per_hour=1000,
                requests_per_day=10000,
                burst_limit=20
            ),
            "security_level": SecurityLevel.MEDIUM,
            "auto_block_suspicious_ips": True,
            "key_rotation_interval_days": 30
        }
    
    async def get_group_security_config(self, group_id: str) -> Dict[str, Any]:
        """获取用户组安全配置"""
        if group_id not in self.group_security_configs:
            # 从数据库获取用户组配置
            user_group = await get_user_group(group_id)
            
            if user_group and "security_config" in user_group:
                self.group_security_configs[group_id] = user_group["security_config"]
            else:
                self.group_security_configs[group_id] = self.default_security_config.copy()
        
        return self.group_security_configs[group_id]
    
    async def verify_group_access(
        self, 
        group_id: str, 
        source_ip: str, 
        user_agent: str,
        api_key: str,
        request_path: str
    ) -> Dict[str, Any]:
        """验证用户组访问权限"""
        try:
            config = await self.get_group_security_config(group_id)
            
            # 基础认证
            await self.base_security.verify_key(api_key)
            
            # IP白名单检查
            if config.get("enable_ip_whitelist", False):
                if not await self._check_ip_whitelist(group_id, source_ip):
                    await self._log_security_event(
                        group_id, ThreatType.INVALID_ACCESS, SecurityLevel.HIGH,
                        source_ip, user_agent, {"reason": "IP not in whitelist"}
                    )
                    return {"allowed": False, "reason": "IP not in whitelist"}
            
            # IP黑名单检查
            if await self._check_ip_blacklist(group_id, source_ip):
                await self._log_security_event(
                    group_id, ThreatType.SUSPICIOUS_IP, SecurityLevel.CRITICAL,
                    source_ip, user_agent, {"reason": "IP in blacklist"}
                )
                return {"allowed": False, "reason": "IP blocked"}
            
            # 速率限制检查
            if config.get("enable_rate_limiting", True):
                rate_check = await self._check_rate_limits(group_id, source_ip, config["rate_limit"])
                if not rate_check["allowed"]:
                    await self._log_security_event(
                        group_id, ThreatType.RATE_LIMIT_EXCEEDED, SecurityLevel.MEDIUM,
                        source_ip, user_agent, rate_check
                    )
                    return rate_check
            
            # 异常检测
            if config.get("enable_anomaly_detection", True):
                anomaly_check = await self._detect_anomalies(group_id, source_ip, user_agent, request_path)
                if anomaly_check["suspicious"]:
                    await self._log_security_event(
                        group_id, ThreatType.UNUSUAL_PATTERN, SecurityLevel.MEDIUM,
                        source_ip, user_agent, anomaly_check
                    )
                    # 异常检测不阻止请求，只记录
            
            # 更新使用模式
            await self._update_usage_patterns(group_id, source_ip, user_agent, request_path)
            
            return {"allowed": True, "security_level": config["security_level"].value}
            
        except Exception as e:
            logger.error(f"Error verifying group access for {group_id}: {str(e)}")
            return {"allowed": False, "reason": "Security check failed", "error": str(e)}
    
    async def _check_ip_whitelist(self, group_id: str, source_ip: str) -> bool:
        """检查IP白名单"""
        try:
            whitelist = self.ip_whitelists.get(group_id, set())
            if not whitelist:
                return True  # 没有白名单则允许所有IP
            
            # 检查精确匹配
            if source_ip in whitelist:
                return True
            
            # 检查CIDR匹配
            source_ip_obj = ipaddress.ip_address(source_ip)
            for ip_range in whitelist:
                try:
                    if "/" in ip_range:
                        network = ipaddress.ip_network(ip_range, strict=False)
                        if source_ip_obj in network:
                            return True
                except ValueError:
                    continue
            
            return False
        except Exception as e:
            logger.error(f"Error checking IP whitelist: {str(e)}")
            return True  # 出错时允许访问
    
    async def _check_ip_blacklist(self, group_id: str, source_ip: str) -> bool:
        """检查IP黑名单"""
        try:
            blacklist = self.ip_blacklists.get(group_id, set())
            if not blacklist:
                return False  # 没有黑名单
            
            # 检查精确匹配
            if source_ip in blacklist:
                return True
            
            # 检查CIDR匹配
            source_ip_obj = ipaddress.ip_address(source_ip)
            for ip_range in blacklist:
                try:
                    if "/" in ip_range:
                        network = ipaddress.ip_network(ip_range, strict=False)
                        if source_ip_obj in network:
                            return True
                except ValueError:
                    continue
            
            return False
        except Exception as e:
            logger.error(f"Error checking IP blacklist: {str(e)}")
            return False  # 出错时不阻止
    
    async def _check_rate_limits(
        self, 
        group_id: str, 
        source_ip: str, 
        rate_config: RateLimitConfig
    ) -> Dict[str, Any]:
        """检查速率限制"""
        try:
            current_time = time.time()
            key = f"{group_id}:{source_ip}"
            
            # 获取时间窗口队列
            minute_queue = self.rate_limit_tracker[key]["minute"]
            hour_queue = self.rate_limit_tracker[key]["hour"]
            day_queue = self.rate_limit_tracker[key]["day"]
            
            # 清理过期记录
            minute_cutoff = current_time - 60
            hour_cutoff = current_time - 3600
            day_cutoff = current_time - 86400
            
            while minute_queue and minute_queue[0] < minute_cutoff:
                minute_queue.popleft()
            while hour_queue and hour_queue[0] < hour_cutoff:
                hour_queue.popleft()
            while day_queue and day_queue[0] < day_cutoff:
                day_queue.popleft()
            
            # 检查限制
            if len(minute_queue) >= rate_config.requests_per_minute:
                return {
                    "allowed": False,
                    "reason": "Rate limit exceeded (per minute)",
                    "limit": rate_config.requests_per_minute,
                    "current": len(minute_queue)
                }
            
            if len(hour_queue) >= rate_config.requests_per_hour:
                return {
                    "allowed": False,
                    "reason": "Rate limit exceeded (per hour)",
                    "limit": rate_config.requests_per_hour,
                    "current": len(hour_queue)
                }
            
            if len(day_queue) >= rate_config.requests_per_day:
                return {
                    "allowed": False,
                    "reason": "Rate limit exceeded (per day)",
                    "limit": rate_config.requests_per_day,
                    "current": len(day_queue)
                }
            
            # 记录当前请求
            minute_queue.append(current_time)
            hour_queue.append(current_time)
            day_queue.append(current_time)
            
            return {"allowed": True}
            
        except Exception as e:
            logger.error(f"Error checking rate limits: {str(e)}")
            return {"allowed": True}  # 出错时允许访问

    async def _detect_anomalies(
        self,
        group_id: str,
        source_ip: str,
        user_agent: str,
        request_path: str
    ) -> Dict[str, Any]:
        """检测异常行为"""
        try:
            patterns = self.usage_patterns[group_id]
            current_hour = datetime.now().hour

            # 检查异常指标
            anomalies = []

            # 1. 检查请求频率异常
            hourly_avg = sum(patterns["hourly_requests"].values()) / max(len(patterns["hourly_requests"]), 1)
            current_requests = patterns["hourly_requests"][current_hour]

            if current_requests > hourly_avg * 3:  # 超过平均值3倍
                anomalies.append("High request frequency")

            # 2. 检查新IP地址
            if source_ip not in patterns["ip_addresses"]:
                if len(patterns["ip_addresses"]) > 10:  # 已有足够历史数据
                    anomalies.append("New IP address")

            # 3. 检查User-Agent异常
            if user_agent not in patterns["user_agents"]:
                if len(patterns["user_agents"]) > 5:
                    anomalies.append("New user agent")

            # 4. 检查访问路径异常
            if "/admin" in request_path or "/debug" in request_path:
                anomalies.append("Suspicious path access")

            return {
                "suspicious": len(anomalies) > 0,
                "anomalies": anomalies,
                "confidence": min(len(anomalies) * 0.3, 1.0)
            }

        except Exception as e:
            logger.error(f"Error detecting anomalies: {str(e)}")
            return {"suspicious": False, "anomalies": []}

    async def _update_usage_patterns(
        self,
        group_id: str,
        source_ip: str,
        user_agent: str,
        request_path: str
    ):
        """更新使用模式"""
        try:
            patterns = self.usage_patterns[group_id]
            current_hour = datetime.now().hour

            patterns["hourly_requests"][current_hour] += 1
            patterns["ip_addresses"][source_ip] += 1
            patterns["user_agents"][user_agent] += 1

            # 提取模型名称
            if "/chat/completions" in request_path:
                patterns["model_usage"]["chat"] += 1
            elif "/embeddings" in request_path:
                patterns["model_usage"]["embeddings"] += 1
            elif "/images" in request_path:
                patterns["model_usage"]["images"] += 1

        except Exception as e:
            logger.error(f"Error updating usage patterns: {str(e)}")

    async def _log_security_event(
        self,
        group_id: str,
        event_type: ThreatType,
        severity: SecurityLevel,
        source_ip: str,
        user_agent: str,
        details: Dict[str, Any]
    ):
        """记录安全事件"""
        try:
            event = SecurityEvent(
                event_id=f"sec_{int(time.time())}_{group_id}",
                group_id=group_id,
                event_type=event_type,
                severity=severity,
                source_ip=source_ip,
                user_agent=user_agent,
                timestamp=datetime.now(),
                details=details
            )

            self.security_events.append(event)

            # 保持事件列表在合理大小
            if len(self.security_events) > 10000:
                self.security_events = self.security_events[-5000:]

            # 记录到日志
            logger.warning(
                f"Security event: {event_type.value} for group {group_id} "
                f"from {source_ip}, severity: {severity.value}"
            )

            # 自动响应
            await self._auto_respond_to_threat(event)

        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")

    async def _auto_respond_to_threat(self, event: SecurityEvent):
        """自动响应威胁"""
        try:
            config = await self.get_group_security_config(event.group_id)

            if not config.get("auto_block_suspicious_ips", True):
                return

            # 高危事件自动加入黑名单
            if event.severity in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]:
                if event.event_type in [ThreatType.SUSPICIOUS_IP, ThreatType.INVALID_ACCESS]:
                    await self.add_ip_to_blacklist(event.group_id, event.source_ip, "Auto-blocked due to security event")

            # 连续违规自动阻止
            recent_events = [
                e for e in self.security_events
                if e.group_id == event.group_id
                and e.source_ip == event.source_ip
                and e.timestamp > datetime.now() - timedelta(minutes=10)
            ]

            if len(recent_events) >= 5:
                await self.add_ip_to_blacklist(event.group_id, event.source_ip, "Auto-blocked due to repeated violations")

        except Exception as e:
            logger.error(f"Error auto-responding to threat: {str(e)}")

    async def add_ip_to_whitelist(self, group_id: str, ip_address: str, reason: str = "") -> bool:
        """添加IP到白名单"""
        try:
            self.ip_whitelists[group_id].add(ip_address)
            logger.info(f"Added IP {ip_address} to whitelist for group {group_id}: {reason}")
            return True
        except Exception as e:
            logger.error(f"Error adding IP to whitelist: {str(e)}")
            return False

    async def add_ip_to_blacklist(self, group_id: str, ip_address: str, reason: str = "") -> bool:
        """添加IP到黑名单"""
        try:
            self.ip_blacklists[group_id].add(ip_address)
            logger.warning(f"Added IP {ip_address} to blacklist for group {group_id}: {reason}")
            return True
        except Exception as e:
            logger.error(f"Error adding IP to blacklist: {str(e)}")
            return False

    async def remove_ip_from_blacklist(self, group_id: str, ip_address: str) -> bool:
        """从黑名单移除IP"""
        try:
            if ip_address in self.ip_blacklists[group_id]:
                self.ip_blacklists[group_id].remove(ip_address)
                logger.info(f"Removed IP {ip_address} from blacklist for group {group_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error removing IP from blacklist: {str(e)}")
            return False

    async def get_security_events(
        self,
        group_id: Optional[str] = None,
        severity: Optional[SecurityLevel] = None,
        limit: int = 100
    ) -> List[SecurityEvent]:
        """获取安全事件"""
        try:
            events = self.security_events

            # 过滤条件
            if group_id:
                events = [e for e in events if e.group_id == group_id]

            if severity:
                events = [e for e in events if e.severity == severity]

            # 按时间倒序排列
            events.sort(key=lambda x: x.timestamp, reverse=True)

            return events[:limit]

        except Exception as e:
            logger.error(f"Error getting security events: {str(e)}")
            return []

    async def get_security_statistics(self, group_id: str) -> Dict[str, Any]:
        """获取安全统计"""
        try:
            # 获取最近24小时的事件
            recent_events = [
                e for e in self.security_events
                if e.group_id == group_id
                and e.timestamp > datetime.now() - timedelta(hours=24)
            ]

            # 按类型统计
            event_counts = defaultdict(int)
            for event in recent_events:
                event_counts[event.event_type.value] += 1

            # 按严重级别统计
            severity_counts = defaultdict(int)
            for event in recent_events:
                severity_counts[event.severity.value] += 1

            # 获取配置信息
            config = await self.get_group_security_config(group_id)

            return {
                "group_id": group_id,
                "security_level": config["security_level"].value,
                "recent_events_24h": len(recent_events),
                "event_types": dict(event_counts),
                "severity_distribution": dict(severity_counts),
                "ip_whitelist_size": len(self.ip_whitelists.get(group_id, set())),
                "ip_blacklist_size": len(self.ip_blacklists.get(group_id, set())),
                "rate_limiting_enabled": config.get("enable_rate_limiting", True),
                "anomaly_detection_enabled": config.get("enable_anomaly_detection", True),
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting security statistics: {str(e)}")
            return {"group_id": group_id, "error": str(e)}

    async def update_security_config(
        self,
        group_id: str,
        config_updates: Dict[str, Any]
    ) -> bool:
        """更新安全配置"""
        try:
            config = await self.get_group_security_config(group_id)

            # 更新配置
            for key, value in config_updates.items():
                if key in config:
                    if key == "security_level":
                        config[key] = SecurityLevel(value)
                    elif key == "rate_limit":
                        config[key] = RateLimitConfig(**value)
                    else:
                        config[key] = value

            self.group_security_configs[group_id] = config
            logger.info(f"Updated security config for group {group_id}")

            return True

        except Exception as e:
            logger.error(f"Error updating security config: {str(e)}")
            return False

    async def generate_api_key_rotation_plan(self, group_id: str) -> Dict[str, Any]:
        """生成API密钥轮换计划"""
        try:
            config = await self.get_group_security_config(group_id)
            rotation_interval = config.get("key_rotation_interval_days", 30)

            # 获取当前密钥信息（模拟）
            current_keys = ["key1", "key2", "key3"]  # 实际应该从数据库获取

            rotation_plan = []
            for i, key in enumerate(current_keys):
                rotation_date = datetime.now() + timedelta(days=rotation_interval + i * 7)
                rotation_plan.append({
                    "key_id": key,
                    "current_key": key[:8] + "...",
                    "rotation_date": rotation_date.isoformat(),
                    "status": "scheduled"
                })

            return {
                "group_id": group_id,
                "rotation_interval_days": rotation_interval,
                "total_keys": len(current_keys),
                "rotation_plan": rotation_plan,
                "next_rotation": rotation_plan[0]["rotation_date"] if rotation_plan else None
            }

        except Exception as e:
            logger.error(f"Error generating key rotation plan: {str(e)}")
            return {"group_id": group_id, "error": str(e)}


# 全局实例
enhanced_security_service = EnhancedSecurityService()
