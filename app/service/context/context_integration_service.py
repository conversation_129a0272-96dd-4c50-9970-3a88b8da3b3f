"""
上下文集成服务
将上下文管理集成到聊天服务中
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from app.service.context.context_manager import context_manager, ContextMode, ContextInfo
from app.domain.openai_models import ChatRequest
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class ContextIntegrationService:
    """
    上下文集成服务
    负责将上下文管理集成到聊天流程中
    """
    
    def __init__(self):
        self.context_manager = context_manager
    
    async def prepare_chat_context(
        self, 
        request: ChatRequest, 
        group_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        为聊天请求准备上下文
        
        Args:
            request: 聊天请求
            group_id: 用户组ID
            conversation_id: 对话ID
            
        Returns:
            Tuple[messages, context_info]: 处理后的消息列表和上下文信息
        """
        try:
            # 如果没有用户组或对话ID，直接返回原始消息
            if not group_id or not conversation_id:
                return request.messages, {"context_enabled": False}
            
            # 获取历史上下文
            historical_messages = await self.context_manager.get_context(
                group_id, conversation_id, request.model
            )
            
            # 合并历史消息和当前消息
            merged_messages = await self._merge_messages(historical_messages, request.messages)
            
            # 获取上下文信息
            context_info = await self.context_manager.get_context_info(group_id, conversation_id)
            
            context_metadata = {
                "context_enabled": True,
                "group_id": group_id,
                "conversation_id": conversation_id,
                "historical_message_count": len(historical_messages),
                "current_message_count": len(request.messages),
                "merged_message_count": len(merged_messages),
                "context_info": context_info.__dict__ if context_info else None
            }
            
            logger.debug(f"Prepared context for {group_id}:{conversation_id}, "
                        f"historical: {len(historical_messages)}, current: {len(request.messages)}, "
                        f"merged: {len(merged_messages)}")
            
            return merged_messages, context_metadata
            
        except Exception as e:
            logger.error(f"Error preparing chat context: {str(e)}")
            return request.messages, {"context_enabled": False, "error": str(e)}
    
    async def save_chat_context(
        self,
        group_id: str,
        conversation_id: str,
        messages: List[Dict[str, Any]],
        model_name: str,
        tokens_used: int = 0,
        response_message: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        保存聊天上下文
        
        Args:
            group_id: 用户组ID
            conversation_id: 对话ID
            messages: 消息列表
            model_name: 模型名称
            tokens_used: 使用的令牌数
            response_message: 响应消息
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 如果有响应消息，添加到消息列表
            if response_message:
                messages_to_save = messages + [response_message]
            else:
                messages_to_save = messages
            
            # 保存上下文
            success = await self.context_manager.save_context(
                group_id, conversation_id, messages_to_save, model_name, tokens_used
            )
            
            if success:
                logger.debug(f"Saved chat context for {group_id}:{conversation_id}, "
                           f"messages: {len(messages_to_save)}, tokens: {tokens_used}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error saving chat context: {str(e)}")
            return False
    
    async def _merge_messages(
        self, 
        historical_messages: List[Dict[str, Any]], 
        current_messages: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """合并历史消息和当前消息"""
        try:
            # 分离系统消息和其他消息
            historical_system = [msg for msg in historical_messages if msg.get("role") == "system"]
            historical_other = [msg for msg in historical_messages if msg.get("role") != "system"]
            
            current_system = [msg for msg in current_messages if msg.get("role") == "system"]
            current_other = [msg for msg in current_messages if msg.get("role") != "system"]
            
            # 合并策略：
            # 1. 优先使用当前请求的系统消息
            # 2. 如果当前请求没有系统消息，使用历史系统消息
            # 3. 历史对话消息 + 当前对话消息
            
            merged_messages = []
            
            # 添加系统消息
            if current_system:
                merged_messages.extend(current_system)
            elif historical_system:
                merged_messages.extend(historical_system)
            
            # 添加历史对话消息
            merged_messages.extend(historical_other)
            
            # 添加当前对话消息
            merged_messages.extend(current_other)
            
            return merged_messages
            
        except Exception as e:
            logger.error(f"Error merging messages: {str(e)}")
            return current_messages  # 出错时返回当前消息
    
    async def clear_conversation_context(self, group_id: str, conversation_id: str) -> bool:
        """清除对话上下文"""
        try:
            success = await self.context_manager.clear_context(group_id, conversation_id)
            
            if success:
                logger.info(f"Cleared conversation context for {group_id}:{conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error clearing conversation context: {str(e)}")
            return False
    
    async def get_conversation_summary(self, group_id: str, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取对话摘要"""
        try:
            context_info = await self.context_manager.get_context_info(group_id, conversation_id)
            
            if not context_info:
                return None
            
            # 获取完整上下文
            messages = await self.context_manager.get_context(group_id, conversation_id)
            
            # 分析消息类型
            message_types = {"system": 0, "user": 0, "assistant": 0, "tool": 0}
            for msg in messages:
                role = msg.get("role", "unknown")
                if role in message_types:
                    message_types[role] += 1
                else:
                    message_types["unknown"] = message_types.get("unknown", 0) + 1
            
            return {
                "group_id": context_info.group_id,
                "conversation_id": context_info.conversation_id,
                "message_count": context_info.message_count,
                "total_tokens": context_info.total_tokens,
                "last_updated": context_info.last_updated.isoformat(),
                "is_shared": context_info.is_shared,
                "model_name": context_info.model_name,
                "message_types": message_types,
                "estimated_size_mb": len(str(messages)) / (1024 * 1024)
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation summary: {str(e)}")
            return None
    
    async def list_group_conversations(self, group_id: str) -> List[Dict[str, Any]]:
        """列出用户组的所有对话"""
        try:
            contexts = await self.context_manager.list_group_contexts(group_id)
            
            conversations = []
            for context in contexts:
                summary = {
                    "conversation_id": context.conversation_id,
                    "message_count": context.message_count,
                    "total_tokens": context.total_tokens,
                    "last_updated": context.last_updated.isoformat(),
                    "is_shared": context.is_shared,
                    "model_name": context.model_name
                }
                conversations.append(summary)
            
            return conversations
            
        except Exception as e:
            logger.error(f"Error listing group conversations: {str(e)}")
            return []
    
    async def switch_conversation_mode(
        self, 
        group_id: str, 
        conversation_id: str, 
        new_mode: str
    ) -> bool:
        """切换对话模式"""
        try:
            # 转换模式字符串
            mode_map = {
                "isolated": ContextMode.ISOLATED,
                "shared": ContextMode.SHARED,
                "hybrid": ContextMode.HYBRID
            }
            
            if new_mode not in mode_map:
                logger.error(f"Invalid context mode: {new_mode}")
                return False
            
            context_mode = mode_map[new_mode]
            success = await self.context_manager.switch_context_mode(
                group_id, conversation_id, context_mode
            )
            
            if success:
                logger.info(f"Switched conversation mode for {group_id}:{conversation_id} to {new_mode}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error switching conversation mode: {str(e)}")
            return False
    
    async def get_context_statistics(self, group_id: Optional[str] = None) -> Dict[str, Any]:
        """获取上下文统计信息"""
        try:
            # 获取内存使用情况
            memory_usage = await self.context_manager.get_memory_usage()
            
            # 如果指定了用户组，获取该用户组的统计
            if group_id:
                conversations = await self.list_group_conversations(group_id)
                
                group_stats = {
                    "group_id": group_id,
                    "conversation_count": len(conversations),
                    "total_messages": sum(conv["message_count"] for conv in conversations),
                    "total_tokens": sum(conv["total_tokens"] for conv in conversations),
                    "conversations": conversations
                }
                
                return {
                    "memory_usage": memory_usage,
                    "group_statistics": group_stats
                }
            else:
                return {
                    "memory_usage": memory_usage,
                    "system_statistics": {
                        "total_contexts": memory_usage.get("total_contexts", 0),
                        "total_messages": memory_usage.get("total_messages", 0),
                        "total_tokens": memory_usage.get("total_tokens", 0)
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting context statistics: {str(e)}")
            return {"error": str(e)}
    
    async def cleanup_expired_contexts(self, ttl_hours: int = 24) -> Dict[str, Any]:
        """清理过期的上下文"""
        try:
            cleaned_count = await self.context_manager.cleanup_expired_contexts()
            
            return {
                "cleaned_contexts": cleaned_count,
                "ttl_hours": ttl_hours,
                "cleanup_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up expired contexts: {str(e)}")
            return {"error": str(e)}

    async def get_global_context_config(self) -> Dict[str, Any]:
        """获取全局上下文隔离配置"""
        try:
            # 返回默认的全局配置
            return {
                "enable_isolation": True,
                "max_context_length": 4096,
                "context_overlap": 200,
                "isolation_strategy": "sliding_window",
                "memory_threshold": 0.8
            }
        except Exception as e:
            logger.error(f"Error getting global context config: {e}")
            return {}

    async def get_group_context_config(self, group_id: str) -> Dict[str, Any]:
        """获取用户组的上下文隔离配置"""
        try:
            # 从数据库获取用户组配置，如果不存在则返回默认配置
            from app.database.services import get_user_group
            user_group = await get_user_group(group_id)

            if user_group and "context_config" in user_group:
                return user_group["context_config"]
            else:
                # 返回默认配置
                return {
                    "enable_isolation": True,
                    "max_context_length": 4096,
                    "context_overlap": 200,
                    "isolation_strategy": "sliding_window",
                    "memory_threshold": 0.8
                }
        except Exception as e:
            logger.error(f"Error getting group context config for {group_id}: {e}")
            return {}

    async def update_global_context_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新全局上下文隔离配置"""
        try:
            # 这里应该保存到全局配置中，暂时返回成功
            logger.info("Global context config updated")
            return {"success": True, "message": "Global context config updated"}
        except Exception as e:
            logger.error(f"Error updating global context config: {e}")
            return {"success": False, "message": str(e)}

    async def update_group_context_config(self, group_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户组的上下文隔离配置"""
        try:
            # 这里应该保存到用户组配置中，暂时返回成功
            logger.info(f"Group context config updated for {group_id}")
            return {"success": True, "message": f"Group context config updated for {group_id}"}
        except Exception as e:
            logger.error(f"Error updating group context config for {group_id}: {e}")
            return {"success": False, "message": str(e)}


# 全局实例
context_integration_service = ContextIntegrationService()
