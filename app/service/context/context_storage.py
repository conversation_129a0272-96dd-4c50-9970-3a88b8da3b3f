"""
上下文存储接口
支持数据库和内存存储的上下文持久化
"""
import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from abc import ABC, abstractmethod

from app.database.services import (
    create_user_group_context, 
    get_user_group_context, 
    update_user_group_context,
    delete_user_group_context
)
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class ContextStorage(ABC):
    """上下文存储抽象基类"""
    
    @abstractmethod
    async def get_context(self, group_id: str, conversation_id: str, is_shared: bool = False) -> Optional[Dict[str, Any]]:
        """获取上下文数据"""
        pass
    
    @abstractmethod
    async def save_context(self, group_id: str, conversation_id: str, context_data: Dict[str, Any], is_shared: bool = False) -> bool:
        """保存上下文数据"""
        pass
    
    @abstractmethod
    async def delete_context(self, group_id: str, conversation_id: str) -> bool:
        """删除上下文数据"""
        pass
    
    @abstractmethod
    async def list_group_contexts(self, group_id: str) -> List[Dict[str, Any]]:
        """列出用户组的所有上下文"""
        pass
    
    @abstractmethod
    async def cleanup_expired_contexts(self, ttl_hours: int = 24) -> int:
        """清理过期的上下文"""
        pass


class DatabaseContextStorage(ContextStorage):
    """数据库上下文存储实现"""
    
    async def get_context(self, group_id: str, conversation_id: str, is_shared: bool = False) -> Optional[Dict[str, Any]]:
        """从数据库获取上下文数据"""
        try:
            # 如果是共享模式，使用特殊的group_id
            storage_group_id = "shared" if is_shared else group_id
            
            context_record = await get_user_group_context(storage_group_id, conversation_id)
            if context_record:
                return {
                    "messages": context_record.get("context_data", {}).get("messages", []),
                    "metadata": {
                        "group_id": group_id,  # 返回原始group_id
                        "conversation_id": conversation_id,
                        "created_at": context_record.get("created_at", datetime.now()).isoformat(),
                        "last_updated": context_record.get("updated_at", datetime.now()).isoformat(),
                        "message_count": context_record.get("message_count", 0),
                        "total_tokens": context_record.get("total_tokens", 0),
                        "model_name": context_record.get("model_name", "unknown"),
                        "is_shared": is_shared
                    }
                }
            return None
        except Exception as e:
            logger.error(f"Error getting context from database: {str(e)}")
            return None
    
    async def save_context(self, group_id: str, conversation_id: str, context_data: Dict[str, Any], is_shared: bool = False) -> bool:
        """保存上下文数据到数据库"""
        try:
            # 如果是共享模式，使用特殊的group_id
            storage_group_id = "shared" if is_shared else group_id
            
            metadata = context_data.get("metadata", {})
            messages = context_data.get("messages", [])
            
            # 检查是否已存在
            existing_context = await get_user_group_context(storage_group_id, conversation_id)
            
            context_record = {
                "group_id": storage_group_id,
                "conversation_id": conversation_id,
                "context_data": {"messages": messages},
                "message_count": len(messages),
                "total_tokens": metadata.get("total_tokens", 0),
                "model_name": metadata.get("model_name", "unknown")
            }
            
            if existing_context:
                # 更新现有记录
                success = await update_user_group_context(
                    storage_group_id, 
                    conversation_id, 
                    context_record["context_data"],
                    context_record["message_count"],
                    context_record["total_tokens"],
                    context_record["model_name"]
                )
            else:
                # 创建新记录
                success = await create_user_group_context(context_record)
            
            if success:
                logger.debug(f"Saved context to database: {storage_group_id}:{conversation_id}")
            
            return success
        except Exception as e:
            logger.error(f"Error saving context to database: {str(e)}")
            return False
    
    async def delete_context(self, group_id: str, conversation_id: str) -> bool:
        """从数据库删除上下文数据"""
        try:
            # 尝试删除隔离模式的上下文
            success1 = await delete_user_group_context(group_id, conversation_id)
            
            # 尝试删除共享模式的上下文
            success2 = await delete_user_group_context("shared", conversation_id)
            
            return success1 or success2
        except Exception as e:
            logger.error(f"Error deleting context from database: {str(e)}")
            return False
    
    async def list_group_contexts(self, group_id: str) -> List[Dict[str, Any]]:
        """列出用户组的所有上下文"""
        try:
            # 这里需要实现数据库查询方法
            # 由于当前数据库服务中没有相应方法，返回空列表
            logger.warning("list_group_contexts not implemented in database services")
            return []
        except Exception as e:
            logger.error(f"Error listing group contexts from database: {str(e)}")
            return []
    
    async def cleanup_expired_contexts(self, ttl_hours: int = 24) -> int:
        """清理过期的上下文"""
        try:
            # 这里需要实现数据库清理方法
            # 由于当前数据库服务中没有相应方法，返回0
            logger.warning("cleanup_expired_contexts not implemented in database services")
            return 0
        except Exception as e:
            logger.error(f"Error cleaning up expired contexts from database: {str(e)}")
            return 0


class MemoryContextStorage(ContextStorage):
    """内存上下文存储实现（用于测试和开发）"""
    
    def __init__(self):
        self.storage: Dict[str, Dict[str, Any]] = {}
        self.lock = asyncio.Lock()
    
    def _get_storage_key(self, group_id: str, conversation_id: str, is_shared: bool = False) -> str:
        """生成存储键"""
        if is_shared:
            return f"shared:{conversation_id}"
        return f"{group_id}:{conversation_id}"
    
    async def get_context(self, group_id: str, conversation_id: str, is_shared: bool = False) -> Optional[Dict[str, Any]]:
        """从内存获取上下文数据"""
        async with self.lock:
            storage_key = self._get_storage_key(group_id, conversation_id, is_shared)
            return self.storage.get(storage_key)
    
    async def save_context(self, group_id: str, conversation_id: str, context_data: Dict[str, Any], is_shared: bool = False) -> bool:
        """保存上下文数据到内存"""
        async with self.lock:
            storage_key = self._get_storage_key(group_id, conversation_id, is_shared)
            self.storage[storage_key] = context_data
            return True
    
    async def delete_context(self, group_id: str, conversation_id: str) -> bool:
        """从内存删除上下文数据"""
        async with self.lock:
            # 尝试删除隔离模式的上下文
            isolated_key = self._get_storage_key(group_id, conversation_id, False)
            shared_key = self._get_storage_key(group_id, conversation_id, True)
            
            deleted = False
            if isolated_key in self.storage:
                del self.storage[isolated_key]
                deleted = True
            
            if shared_key in self.storage:
                del self.storage[shared_key]
                deleted = True
            
            return deleted
    
    async def list_group_contexts(self, group_id: str) -> List[Dict[str, Any]]:
        """列出用户组的所有上下文"""
        async with self.lock:
            contexts = []
            for key, context_data in self.storage.items():
                metadata = context_data.get("metadata", {})
                if metadata.get("group_id") == group_id:
                    contexts.append(context_data)
            return contexts
    
    async def cleanup_expired_contexts(self, ttl_hours: int = 24) -> int:
        """清理过期的上下文"""
        async with self.lock:
            current_time = datetime.now()
            expired_keys = []
            
            for key, context_data in self.storage.items():
                metadata = context_data.get("metadata", {})
                last_updated_str = metadata.get("last_updated")
                
                if last_updated_str:
                    try:
                        last_updated = datetime.fromisoformat(last_updated_str)
                        if current_time - last_updated > timedelta(hours=ttl_hours):
                            expired_keys.append(key)
                    except ValueError:
                        # 无效的时间格式，标记为过期
                        expired_keys.append(key)
            
            for key in expired_keys:
                del self.storage[key]
            
            return len(expired_keys)
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        async with self.lock:
            total_contexts = len(self.storage)
            total_messages = 0
            total_tokens = 0
            
            for context_data in self.storage.values():
                messages = context_data.get("messages", [])
                metadata = context_data.get("metadata", {})
                
                total_messages += len(messages)
                total_tokens += metadata.get("total_tokens", 0)
            
            return {
                "total_contexts": total_contexts,
                "total_messages": total_messages,
                "total_tokens": total_tokens,
                "storage_keys": list(self.storage.keys())
            }


class HybridContextStorage(ContextStorage):
    """混合上下文存储（内存+数据库）"""
    
    def __init__(self):
        self.memory_storage = MemoryContextStorage()
        self.database_storage = DatabaseContextStorage()
    
    async def get_context(self, group_id: str, conversation_id: str, is_shared: bool = False) -> Optional[Dict[str, Any]]:
        """先从内存获取，再从数据库获取"""
        # 先尝试内存
        context = await self.memory_storage.get_context(group_id, conversation_id, is_shared)
        if context:
            return context
        
        # 再尝试数据库
        context = await self.database_storage.get_context(group_id, conversation_id, is_shared)
        if context:
            # 缓存到内存
            await self.memory_storage.save_context(group_id, conversation_id, context, is_shared)
        
        return context
    
    async def save_context(self, group_id: str, conversation_id: str, context_data: Dict[str, Any], is_shared: bool = False) -> bool:
        """同时保存到内存和数据库"""
        # 保存到内存
        memory_success = await self.memory_storage.save_context(group_id, conversation_id, context_data, is_shared)
        
        # 异步保存到数据库
        asyncio.create_task(
            self.database_storage.save_context(group_id, conversation_id, context_data, is_shared)
        )
        
        return memory_success
    
    async def delete_context(self, group_id: str, conversation_id: str) -> bool:
        """从内存和数据库删除"""
        memory_success = await self.memory_storage.delete_context(group_id, conversation_id)
        database_success = await self.database_storage.delete_context(group_id, conversation_id)
        
        return memory_success or database_success
    
    async def list_group_contexts(self, group_id: str) -> List[Dict[str, Any]]:
        """列出用户组的所有上下文"""
        # 合并内存和数据库的结果
        memory_contexts = await self.memory_storage.list_group_contexts(group_id)
        database_contexts = await self.database_storage.list_group_contexts(group_id)
        
        # 去重（基于conversation_id）
        seen_conversations = set()
        merged_contexts = []
        
        for context in memory_contexts + database_contexts:
            metadata = context.get("metadata", {})
            conversation_id = metadata.get("conversation_id")
            
            if conversation_id not in seen_conversations:
                seen_conversations.add(conversation_id)
                merged_contexts.append(context)
        
        return merged_contexts
    
    async def cleanup_expired_contexts(self, ttl_hours: int = 24) -> int:
        """清理过期的上下文"""
        memory_cleaned = await self.memory_storage.cleanup_expired_contexts(ttl_hours)
        database_cleaned = await self.database_storage.cleanup_expired_contexts(ttl_hours)
        
        return memory_cleaned + database_cleaned


# 全局实例 - 默认使用混合存储
context_storage = HybridContextStorage()
