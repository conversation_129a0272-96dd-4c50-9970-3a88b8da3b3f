"""
上下文管理器
支持用户组级别的上下文隔离和共享机制
"""
import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from app.service.context.context_storage import ContextStorage, context_storage
from app.database.services import get_user_group
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


class ContextMode(Enum):
    """上下文模式"""
    ISOLATED = "isolated"  # 完全隔离
    SHARED = "shared"  # 完全共享
    HYBRID = "hybrid"  # 混合模式（降级时共享）


@dataclass
class ContextConfig:
    """上下文配置"""
    group_id: str
    conversation_id: str
    mode: ContextMode
    max_messages: int = 50
    max_tokens: int = 100000
    ttl_hours: int = 24
    enable_compression: bool = True


@dataclass
class ContextInfo:
    """上下文信息"""
    group_id: str
    conversation_id: str
    message_count: int
    total_tokens: int
    last_updated: datetime
    is_shared: bool
    model_name: str


class ContextManager:
    """
    上下文管理器
    负责用户组级别的上下文隔离和共享
    """
    
    def __init__(self, storage: ContextStorage = None):
        self.storage = storage or context_storage
        
        # 内存缓存
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        
        # 用户组配置缓存
        self.group_configs: Dict[str, ContextConfig] = {}
        
        # 上下文锁
        self.context_locks: Dict[str, asyncio.Lock] = {}
        
        # 清理任务
        self._cleanup_task = None
        self._initialized = False

    def _ensure_initialized(self):
        """确保管理器已初始化"""
        if not self._initialized:
            self._start_cleanup_task()
            self._initialized = True

    def _start_cleanup_task(self):
        """启动清理任务"""
        try:
            if self._cleanup_task is None or self._cleanup_task.done():
                self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
        except RuntimeError:
            # 如果没有运行的事件循环，稍后再启动
            pass
    
    async def _periodic_cleanup(self):
        """定期清理过期上下文"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时清理一次
                await self.cleanup_expired_contexts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {str(e)}")
    
    async def get_context_config(self, group_id: str, conversation_id: str) -> ContextConfig:
        """获取上下文配置"""
        config_key = f"{group_id}:{conversation_id}"
        
        if config_key not in self.group_configs:
            # 从数据库获取用户组配置
            user_group = await get_user_group(group_id)
            
            if user_group:
                context_isolation = user_group.get("context_isolation", True)
                mode = ContextMode.ISOLATED if context_isolation else ContextMode.SHARED
            else:
                mode = ContextMode.ISOLATED  # 默认隔离
            
            self.group_configs[config_key] = ContextConfig(
                group_id=group_id,
                conversation_id=conversation_id,
                mode=mode,
                max_messages=50,
                max_tokens=100000,
                ttl_hours=24,
                enable_compression=True
            )
        
        return self.group_configs[config_key]
    
    async def get_context(
        self, 
        group_id: str, 
        conversation_id: str, 
        model_name: str = None
    ) -> List[Dict[str, Any]]:
        """
        获取上下文消息
        
        Args:
            group_id: 用户组ID
            conversation_id: 对话ID
            model_name: 模型名称
            
        Returns:
            List[Dict[str, Any]]: 上下文消息列表
        """
        try:
            config = await self.get_context_config(group_id, conversation_id)
            context_key = self._get_context_key(group_id, conversation_id, config.mode)
            
            # 获取锁
            if context_key not in self.context_locks:
                self.context_locks[context_key] = asyncio.Lock()
            
            async with self.context_locks[context_key]:
                # 先从内存缓存获取
                if context_key in self.memory_cache:
                    context_data = self.memory_cache[context_key]
                    logger.debug(f"Retrieved context from memory cache for {context_key}")
                else:
                    # 从存储获取
                    context_data = await self.storage.get_context(
                        group_id, conversation_id, config.mode == ContextMode.SHARED
                    )
                    
                    if context_data:
                        # 缓存到内存
                        self.memory_cache[context_key] = context_data
                        logger.debug(f"Retrieved context from storage for {context_key}")
                    else:
                        # 创建新的上下文
                        context_data = {
                            "messages": [],
                            "metadata": {
                                "group_id": group_id,
                                "conversation_id": conversation_id,
                                "created_at": datetime.now().isoformat(),
                                "last_updated": datetime.now().isoformat(),
                                "message_count": 0,
                                "total_tokens": 0,
                                "model_name": model_name or "unknown",
                                "is_shared": config.mode == ContextMode.SHARED
                            }
                        }
                        self.memory_cache[context_key] = context_data
                        logger.info(f"Created new context for {context_key}")
                
                return context_data.get("messages", [])
        
        except Exception as e:
            logger.error(f"Error getting context for {group_id}:{conversation_id}: {str(e)}")
            return []
    
    async def save_context(
        self, 
        group_id: str, 
        conversation_id: str, 
        messages: List[Dict[str, Any]], 
        model_name: str = None,
        tokens_used: int = 0
    ) -> bool:
        """
        保存上下文消息
        
        Args:
            group_id: 用户组ID
            conversation_id: 对话ID
            messages: 消息列表
            model_name: 模型名称
            tokens_used: 使用的令牌数
            
        Returns:
            bool: 保存是否成功
        """
        try:
            config = await self.get_context_config(group_id, conversation_id)
            context_key = self._get_context_key(group_id, conversation_id, config.mode)
            
            # 获取锁
            if context_key not in self.context_locks:
                self.context_locks[context_key] = asyncio.Lock()
            
            async with self.context_locks[context_key]:
                # 限制消息数量
                if len(messages) > config.max_messages:
                    messages = messages[-config.max_messages:]
                
                # 计算总令牌数
                total_tokens = sum(
                    len(str(msg.get("content", ""))) for msg in messages
                ) + tokens_used
                
                # 如果超过令牌限制，压缩上下文
                if total_tokens > config.max_tokens and config.enable_compression:
                    messages = await self._compress_context(messages, config.max_tokens)
                    total_tokens = sum(len(str(msg.get("content", ""))) for msg in messages)
                
                # 更新上下文数据
                context_data = {
                    "messages": messages,
                    "metadata": {
                        "group_id": group_id,
                        "conversation_id": conversation_id,
                        "created_at": self.memory_cache.get(context_key, {}).get("metadata", {}).get("created_at", datetime.now().isoformat()),
                        "last_updated": datetime.now().isoformat(),
                        "message_count": len(messages),
                        "total_tokens": total_tokens,
                        "model_name": model_name or "unknown",
                        "is_shared": config.mode == ContextMode.SHARED
                    }
                }
                
                # 更新内存缓存
                self.memory_cache[context_key] = context_data
                
                # 异步保存到存储
                asyncio.create_task(self.storage.save_context(
                    group_id, 
                    conversation_id, 
                    context_data,
                    config.mode == ContextMode.SHARED
                ))
                
                logger.debug(f"Saved context for {context_key}, messages: {len(messages)}, tokens: {total_tokens}")
                return True
        
        except Exception as e:
            logger.error(f"Error saving context for {group_id}:{conversation_id}: {str(e)}")
            return False
    
    async def _compress_context(self, messages: List[Dict[str, Any]], max_tokens: int) -> List[Dict[str, Any]]:
        """压缩上下文（简化实现）"""
        if not messages:
            return messages
        
        # 保留系统消息和最近的消息
        system_messages = [msg for msg in messages if msg.get("role") == "system"]
        other_messages = [msg for msg in messages if msg.get("role") != "system"]
        
        # 从最新消息开始保留
        compressed_messages = system_messages.copy()
        current_tokens = sum(len(str(msg.get("content", ""))) for msg in system_messages)
        
        for msg in reversed(other_messages):
            msg_tokens = len(str(msg.get("content", "")))
            if current_tokens + msg_tokens <= max_tokens:
                compressed_messages.insert(-len(system_messages) if system_messages else 0, msg)
                current_tokens += msg_tokens
            else:
                break
        
        logger.info(f"Compressed context from {len(messages)} to {len(compressed_messages)} messages")
        return compressed_messages
    
    def _get_context_key(self, group_id: str, conversation_id: str, mode: ContextMode) -> str:
        """生成上下文键"""
        if mode == ContextMode.SHARED:
            return f"shared:{conversation_id}"
        else:
            return f"isolated:{group_id}:{conversation_id}"

    async def clear_context(self, group_id: str, conversation_id: str) -> bool:
        """清除指定的上下文"""
        try:
            config = await self.get_context_config(group_id, conversation_id)
            context_key = self._get_context_key(group_id, conversation_id, config.mode)

            # 从内存缓存删除
            if context_key in self.memory_cache:
                del self.memory_cache[context_key]

            # 从存储删除
            await self.storage.delete_context(group_id, conversation_id)

            logger.info(f"Cleared context for {context_key}")
            return True
        except Exception as e:
            logger.error(f"Error clearing context for {group_id}:{conversation_id}: {str(e)}")
            return False

    async def get_context_info(self, group_id: str, conversation_id: str) -> Optional[ContextInfo]:
        """获取上下文信息"""
        try:
            config = await self.get_context_config(group_id, conversation_id)
            context_key = self._get_context_key(group_id, conversation_id, config.mode)

            context_data = self.memory_cache.get(context_key)
            if not context_data:
                context_data = await self.storage.get_context(
                    group_id, conversation_id, config.mode == ContextMode.SHARED
                )

            if context_data and "metadata" in context_data:
                metadata = context_data["metadata"]
                return ContextInfo(
                    group_id=metadata.get("group_id", group_id),
                    conversation_id=metadata.get("conversation_id", conversation_id),
                    message_count=metadata.get("message_count", 0),
                    total_tokens=metadata.get("total_tokens", 0),
                    last_updated=datetime.fromisoformat(metadata.get("last_updated", datetime.now().isoformat())),
                    is_shared=metadata.get("is_shared", False),
                    model_name=metadata.get("model_name", "unknown")
                )

            return None
        except Exception as e:
            logger.error(f"Error getting context info for {group_id}:{conversation_id}: {str(e)}")
            return None

    async def list_group_contexts(self, group_id: str) -> List[ContextInfo]:
        """列出用户组的所有上下文"""
        try:
            contexts = []

            # 从内存缓存查找
            for context_key, context_data in self.memory_cache.items():
                if context_key.startswith(f"isolated:{group_id}:") or context_key.startswith("shared:"):
                    metadata = context_data.get("metadata", {})
                    if metadata.get("group_id") == group_id:
                        contexts.append(ContextInfo(
                            group_id=metadata.get("group_id", group_id),
                            conversation_id=metadata.get("conversation_id", ""),
                            message_count=metadata.get("message_count", 0),
                            total_tokens=metadata.get("total_tokens", 0),
                            last_updated=datetime.fromisoformat(metadata.get("last_updated", datetime.now().isoformat())),
                            is_shared=metadata.get("is_shared", False),
                            model_name=metadata.get("model_name", "unknown")
                        ))

            # 从存储查找
            storage_contexts = await self.storage.list_group_contexts(group_id)
            for context_data in storage_contexts:
                metadata = context_data.get("metadata", {})
                context_info = ContextInfo(
                    group_id=metadata.get("group_id", group_id),
                    conversation_id=metadata.get("conversation_id", ""),
                    message_count=metadata.get("message_count", 0),
                    total_tokens=metadata.get("total_tokens", 0),
                    last_updated=datetime.fromisoformat(metadata.get("last_updated", datetime.now().isoformat())),
                    is_shared=metadata.get("is_shared", False),
                    model_name=metadata.get("model_name", "unknown")
                )

                # 避免重复
                if not any(c.conversation_id == context_info.conversation_id for c in contexts):
                    contexts.append(context_info)

            return sorted(contexts, key=lambda x: x.last_updated, reverse=True)
        except Exception as e:
            logger.error(f"Error listing contexts for group {group_id}: {str(e)}")
            return []

    async def cleanup_expired_contexts(self) -> int:
        """清理过期的上下文"""
        try:
            cleaned_count = 0
            current_time = datetime.now()

            # 清理内存缓存中的过期上下文
            expired_keys = []
            for context_key, context_data in self.memory_cache.items():
                metadata = context_data.get("metadata", {})
                last_updated = datetime.fromisoformat(metadata.get("last_updated", current_time.isoformat()))

                # 检查是否过期（默认24小时）
                if current_time - last_updated > timedelta(hours=24):
                    expired_keys.append(context_key)

            for key in expired_keys:
                del self.memory_cache[key]
                cleaned_count += 1

            # 清理存储中的过期上下文
            storage_cleaned = await self.storage.cleanup_expired_contexts(24)
            cleaned_count += storage_cleaned

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired contexts")

            return cleaned_count
        except Exception as e:
            logger.error(f"Error cleaning up expired contexts: {str(e)}")
            return 0

    async def switch_context_mode(self, group_id: str, conversation_id: str, new_mode: ContextMode) -> bool:
        """切换上下文模式"""
        try:
            config = await self.get_context_config(group_id, conversation_id)
            old_mode = config.mode

            if old_mode == new_mode:
                return True  # 无需切换

            # 获取当前上下文
            messages = await self.get_context(group_id, conversation_id)

            # 更新配置
            config.mode = new_mode
            config_key = f"{group_id}:{conversation_id}"
            self.group_configs[config_key] = config

            # 清除旧的上下文键
            old_context_key = self._get_context_key(group_id, conversation_id, old_mode)
            if old_context_key in self.memory_cache:
                del self.memory_cache[old_context_key]

            # 保存到新的上下文键
            await self.save_context(group_id, conversation_id, messages)

            logger.info(f"Switched context mode for {group_id}:{conversation_id} from {old_mode.value} to {new_mode.value}")
            return True
        except Exception as e:
            logger.error(f"Error switching context mode for {group_id}:{conversation_id}: {str(e)}")
            return False

    async def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            total_contexts = len(self.memory_cache)
            total_messages = 0
            total_tokens = 0

            for context_data in self.memory_cache.values():
                messages = context_data.get("messages", [])
                metadata = context_data.get("metadata", {})

                total_messages += len(messages)
                total_tokens += metadata.get("total_tokens", 0)

            return {
                "total_contexts": total_contexts,
                "total_messages": total_messages,
                "total_tokens": total_tokens,
                "memory_size_mb": len(str(self.memory_cache)) / (1024 * 1024),
                "active_locks": len(self.context_locks)
            }
        except Exception as e:
            logger.error(f"Error getting memory usage: {str(e)}")
            return {"error": str(e)}


# 全局实例
context_manager = ContextManager()
