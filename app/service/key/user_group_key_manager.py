"""
用户组密钥管理器
扩展现有KeyManager以支持用户组功能
"""
import asyncio
from itertools import cycle
from typing import Dict, List, Optional
from collections import defaultdict

from app.service.key.key_manager import KeyManager, get_key_manager_instance
from app.database.services import get_user_group
from app.log.logger import get_key_manager_logger
from app.config.config import settings

logger = get_key_manager_logger()


class UserGroupKeyManager(KeyManager):
    """
    用户组密钥管理器
    扩展KeyManager以支持用户组专属密钥管理
    """
    
    def __init__(self, api_keys: list, vertex_api_keys: list):
        # 调用父类初始化
        super().__init__(api_keys, vertex_api_keys)
        
        # 用户组密钥池
        self.group_key_pools: Dict[str, List[str]] = {}
        self.group_vertex_key_pools: Dict[str, List[str]] = {}
        
        # 用户组密钥循环器
        self.group_key_cycles: Dict[str, cycle] = {}
        self.group_vertex_key_cycles: Dict[str, cycle] = {}
        
        # 用户组密钥锁
        self.group_key_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        self.group_vertex_key_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        
        # 用户组密钥失败计数
        self.group_key_failure_counts: Dict[str, Dict[str, int]] = defaultdict(dict)
        self.group_vertex_key_failure_counts: Dict[str, Dict[str, int]] = defaultdict(dict)
        
        # 用户组失败计数锁
        self.group_failure_count_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        self.group_vertex_failure_count_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
    
    async def add_group_keys(self, group_id: str, api_keys: List[str], vertex_api_keys: Optional[List[str]] = None):
        """为用户组添加专属密钥"""
        try:
            user_group = await get_user_group(group_id)
            if not user_group:
                logger.warning(f"User group {group_id} not found")
                return False
            
            if api_keys:
                self.group_key_pools[group_id] = api_keys
                self.group_key_cycles[group_id] = cycle(api_keys)
                async with self.group_failure_count_locks[group_id]:
                    self.group_key_failure_counts[group_id] = dict.fromkeys(api_keys, 0)
                logger.info(f"Added {len(api_keys)} API keys for group {group_id}")
            
            if vertex_api_keys:
                self.group_vertex_key_pools[group_id] = vertex_api_keys
                self.group_vertex_key_cycles[group_id] = cycle(vertex_api_keys)
                async with self.group_vertex_failure_count_locks[group_id]:
                    self.group_vertex_key_failure_counts[group_id] = dict.fromkeys(vertex_api_keys, 0)
                logger.info(f"Added {len(vertex_api_keys)} Vertex keys for group {group_id}")
            
            return True
        except Exception as e:
            logger.error(f"Failed to add keys for group {group_id}: {str(e)}")
            return False
    
    async def get_next_working_key(self, group_id: Optional[str] = None) -> str:
        """获取下一个可用的API密钥，支持用户组"""
        if group_id and group_id in self.group_key_pools:
            return await self.get_next_working_group_key(group_id)
        else:
            return await super().get_next_working_key()
    
    async def get_next_working_vertex_key(self, group_id: Optional[str] = None) -> str:
        """获取下一个可用的Vertex API密钥，支持用户组"""
        if group_id and group_id in self.group_vertex_key_pools:
            return await self.get_next_working_group_vertex_key(group_id)
        else:
            return await super().get_next_working_vertex_key()
    
    async def get_next_working_group_key(self, group_id: str) -> str:
        """获取用户组的下一个可用API密钥"""
        if group_id not in self.group_key_pools:
            logger.warning(f"No keys for group {group_id}, using global keys")
            return await super().get_next_working_key()
        
        initial_key = await self.get_next_group_key(group_id)
        if not initial_key:
            return await super().get_next_working_key()
        
        current_key = initial_key
        while True:
            if await self.is_group_key_valid(group_id, current_key):
                return current_key
            
            current_key = await self.get_next_group_key(group_id)
            if current_key == initial_key:
                return current_key
    
    async def get_next_working_group_vertex_key(self, group_id: str) -> str:
        """获取用户组的下一个可用Vertex API密钥"""
        if group_id not in self.group_vertex_key_pools:
            logger.warning(f"No Vertex keys for group {group_id}, using global keys")
            return await super().get_next_working_vertex_key()
        
        initial_key = await self.get_next_group_vertex_key(group_id)
        if not initial_key:
            return await super().get_next_working_vertex_key()
        
        current_key = initial_key
        while True:
            if await self.is_group_vertex_key_valid(group_id, current_key):
                return current_key
            
            current_key = await self.get_next_group_vertex_key(group_id)
            if current_key == initial_key:
                return current_key
    
    async def get_next_group_key(self, group_id: str) -> Optional[str]:
        """获取用户组的下一个API密钥"""
        if group_id not in self.group_key_pools:
            return None
        
        async with self.group_key_locks[group_id]:
            try:
                return next(self.group_key_cycles[group_id])
            except StopIteration:
                return None
    
    async def get_next_group_vertex_key(self, group_id: str) -> Optional[str]:
        """获取用户组的下一个Vertex API密钥"""
        if group_id not in self.group_vertex_key_pools:
            return None
        
        async with self.group_vertex_key_locks[group_id]:
            try:
                return next(self.group_vertex_key_cycles[group_id])
            except StopIteration:
                return None
    
    async def is_group_key_valid(self, group_id: str, key: str) -> bool:
        """检查用户组密钥是否有效"""
        if group_id not in self.group_key_failure_counts:
            return True
        
        async with self.group_failure_count_locks[group_id]:
            failure_count = self.group_key_failure_counts[group_id].get(key, 0)
            return failure_count < self.MAX_FAILURES
    
    async def is_group_vertex_key_valid(self, group_id: str, key: str) -> bool:
        """检查用户组Vertex密钥是否有效"""
        if group_id not in self.group_vertex_key_failure_counts:
            return True
        
        async with self.group_vertex_failure_count_locks[group_id]:
            failure_count = self.group_vertex_key_failure_counts[group_id].get(key, 0)
            return failure_count < self.MAX_FAILURES
    
    async def handle_group_api_failure(self, group_id: str, api_key: str, retries: int) -> str:
        """处理用户组API调用失败"""
        if group_id in self.group_key_failure_counts:
            async with self.group_failure_count_locks[group_id]:
                if api_key in self.group_key_failure_counts[group_id]:
                    self.group_key_failure_counts[group_id][api_key] += 1
                    if self.group_key_failure_counts[group_id][api_key] >= self.MAX_FAILURES:
                        logger.warning(f"Group {group_id} API key {api_key} failed {self.MAX_FAILURES} times")
        
        if retries < settings.MAX_RETRIES:
            return await self.get_next_working_group_key(group_id)
        else:
            return ""
    
    async def get_group_keys_status(self, group_id: str) -> dict:
        """获取用户组密钥状态"""
        if group_id not in self.group_key_pools:
            return {"error": f"No keys configured for group {group_id}"}
        
        valid_keys = {}
        invalid_keys = {}
        
        async with self.group_failure_count_locks[group_id]:
            for key in self.group_key_pools[group_id]:
                fail_count = self.group_key_failure_counts[group_id].get(key, 0)
                if fail_count < self.MAX_FAILURES:
                    valid_keys[key] = fail_count
                else:
                    invalid_keys[key] = fail_count
        
        return {
            "group_id": group_id,
            "valid_keys": valid_keys,
            "invalid_keys": invalid_keys,
            "total_keys": len(self.group_key_pools[group_id])
        }


# 全局用户组密钥管理器实例
_user_group_key_manager_instance = None
_user_group_manager_lock = asyncio.Lock()


async def get_user_group_key_manager_instance(
    api_keys: list = None, vertex_api_keys: list = None
) -> UserGroupKeyManager:
    """获取用户组密钥管理器单例实例"""
    global _user_group_key_manager_instance
    
    async with _user_group_manager_lock:
        if _user_group_key_manager_instance is None:
            if api_keys is None or vertex_api_keys is None:
                # 使用全局密钥管理器的密钥
                global_manager = await get_key_manager_instance()
                api_keys = global_manager.api_keys
                vertex_api_keys = global_manager.vertex_api_keys
            
            _user_group_key_manager_instance = UserGroupKeyManager(api_keys, vertex_api_keys)
            logger.info("UserGroupKeyManager instance created")
        
        return _user_group_key_manager_instance


async def reset_user_group_key_manager_instance():
    """重置用户组密钥管理器实例"""
    global _user_group_key_manager_instance
    async with _user_group_manager_lock:
        _user_group_key_manager_instance = None
        logger.info("UserGroupKeyManager instance reset")


# 创建一个简单的全局实例用于导入
user_group_key_manager = UserGroupKeyManager([], [])
