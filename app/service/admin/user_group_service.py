"""
用户组管理服务
提供用户组的完整生命周期管理功能
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

from app.database.sqlite_operations import (
    create_user_group, get_user_group, update_user_group, delete_user_group,
    list_user_groups, get_user_group_quotas, get_user_group_api_keys
)
from app.database.services import (
    create_user_group_api_key, delete_user_group_api_key
)
from app.database.models import UserGroupStatus, QuotaType
from app.service.quota.user_group_quota_manager import user_group_quota_manager
from app.service.key.user_group_key_manager import user_group_key_manager
from app.service.context.context_integration_service import context_integration_service
from app.log.logger import get_user_group_logger

logger = get_user_group_logger()


@dataclass
class UserGroupCreateRequest:
    """用户组创建请求"""
    group_id: str
    group_name: str
    description: Optional[str] = None
    status: UserGroupStatus = UserGroupStatus.ACTIVE
    priority: int = 5
    auto_fallback: bool = True
    context_isolation: bool = True
    no_fallback_models: List[str] = None
    token_limit: int = 0  # 0表示无限制
    expiry_date: Optional[str] = None  # 到期日期，格式：YYYY-MM-DD
    token_rates: Optional[Dict[str, float]] = None  # 模型Token消耗倍率


@dataclass
class UserGroupUpdateRequest:
    """用户组更新请求"""
    group_name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[UserGroupStatus] = None
    priority: Optional[int] = None
    auto_fallback: Optional[bool] = None
    context_isolation: Optional[bool] = None
    no_fallback_models: Optional[List[str]] = None


@dataclass
class QuotaCreateRequest:
    """配额创建请求"""
    quota_type: QuotaType
    model_name: Optional[str] = None
    quota_limit: int = 1000
    window_duration_minutes: int = 1


@dataclass
class ApiKeyCreateRequest:
    """API密钥创建请求"""
    api_key: str
    key_name: Optional[str] = None
    is_active: bool = True
    priority: int = 1


class UserGroupService:
    """
    用户组管理服务
    提供用户组的完整生命周期管理
    """
    
    def __init__(self):
        self.quota_manager = user_group_quota_manager
        self.key_manager = user_group_key_manager
        self.context_service = context_integration_service
    
    async def create_user_group(self, request: UserGroupCreateRequest) -> Dict[str, Any]:
        """创建用户组"""
        try:
            # 检查用户组是否已存在
            existing_group = await get_user_group(request.group_id)
            if existing_group:
                return {
                    "error": "GROUP_EXISTS",
                    "message": f"用户组 '{request.group_id}' 已存在",
                    "group_id": request.group_id,
                    "status": "exists"
                }
            
            # 创建用户组到数据库
            success = await create_user_group(
                group_id=request.group_id,
                group_name=request.group_name,
                description=request.description,
                auto_fallback=request.auto_fallback,
                context_isolation=request.context_isolation,
                priority=request.priority,
                no_fallback_models=request.no_fallback_models or []
            )

            if not success:
                raise RuntimeError("Failed to create user group in database")

            # 自动生成API密钥
            import secrets
            api_key = f"sk-{secrets.token_urlsafe(32)}"

            try:
                # 创建API密钥请求
                api_key_request = ApiKeyCreateRequest(
                    api_key=api_key,
                    key_name=f"default-{request.group_id}",
                    is_active=True,
                    priority=1
                )

                # 创建API密钥
                await self.create_group_api_key(request.group_id, api_key_request)
                logger.info(f"Auto-generated API key for group: {request.group_id}")

            except Exception as e:
                logger.warning(f"Failed to auto-generate API key for group {request.group_id}: {str(e)}")
                # 不影响用户组创建，只记录警告

            logger.info(f"Created user group: {request.group_id}")

            return {
                "group_id": request.group_id,
                "status": "created",
                "created_at": datetime.now().isoformat(),
                "api_key": api_key  # 返回生成的API密钥
            }
            
        except Exception as e:
            logger.error(f"Error creating user group {request.group_id}: {str(e)}")
            raise
    
    async def get_user_group(self, group_id: str) -> Optional[Dict[str, Any]]:
        """获取用户组详情"""
        try:
            group = await get_user_group(group_id)
            if not group:
                return None
            
            # 获取配额信息
            quotas = await self.get_group_quotas(group_id)
            
            # 获取API密钥信息
            api_keys = await self.get_group_api_keys(group_id)
            
            # 获取统计信息
            stats = await self.get_group_statistics(group_id)
            
            return {
                **group,
                "quotas": quotas,
                "api_keys": api_keys,
                "statistics": stats
            }
            
        except Exception as e:
            logger.error(f"Error getting user group {group_id}: {str(e)}")
            raise
    
    async def list_user_groups(
        self,
        status: Optional[UserGroupStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """列出用户组"""
        try:
            # 从数据库获取用户组
            all_groups = await list_user_groups()

            # 根据状态过滤
            if status:
                all_groups = [g for g in all_groups if g["status"] == status.value]

            # 为每个用户组添加API密钥数量信息
            for group in all_groups:
                try:
                    api_keys = await self.get_group_api_keys(group["group_id"])
                    group["api_keys_count"] = len(api_keys)

                    # 获取今日请求数
                    stats = await self.get_group_statistics(group["group_id"])
                    group["today_requests"] = stats.get("total_requests", 0)  # Using total_requests for now
                    group["quota_usage"] = 0  # Placeholder

                except Exception as e:
                    logger.warning(f"Failed to get API keys count for group {group['group_id']}: {str(e)}")
                    group["api_keys_count"] = 0
                    group["today_requests"] = 0
                    group["quota_usage"] = 0

            # 分页处理
            total = len(all_groups)
            groups = all_groups[offset:offset + limit]

            return {
                "user_groups": groups,
                "total": total,
                "limit": limit,
                "offset": offset
            }

        except Exception as e:
            logger.error(f"Error listing user groups: {str(e)}")
            raise
    
    async def update_user_group(self, group_id: str, request: UserGroupUpdateRequest) -> Dict[str, Any]:
        """更新用户组"""
        try:
            # 检查用户组是否存在
            existing_group = await get_user_group(group_id)
            if not existing_group:
                raise ValueError(f"User group '{group_id}' not found")
            
            # 准备更新数据
            update_data = {}
            if request.group_name is not None:
                update_data["group_name"] = request.group_name
            if request.description is not None:
                update_data["description"] = request.description
            if request.status is not None:
                update_data["status"] = request.status
            if request.priority is not None:
                update_data["priority"] = request.priority
            if request.auto_fallback is not None:
                update_data["auto_fallback"] = request.auto_fallback
            if request.context_isolation is not None:
                update_data["context_isolation"] = request.context_isolation
            if request.no_fallback_models is not None:
                update_data["no_fallback_models"] = request.no_fallback_models
            
            if not update_data:
                raise ValueError("No update data provided")
            
            # 更新用户组
            success = await update_user_group(group_id, update_data)
            if not success:
                raise RuntimeError("Failed to update user group in database")
            
            logger.info(f"Updated user group: {group_id}")
            
            return {
                "group_id": group_id,
                "status": "updated",
                "updated_at": datetime.now().isoformat(),
                "changes": update_data
            }
            
        except Exception as e:
            logger.error(f"Error updating user group {group_id}: {str(e)}")
            raise
    
    async def delete_user_group(self, group_id: str, force: bool = False) -> Dict[str, Any]:
        """删除用户组"""
        try:
            # 检查用户组是否存在
            existing_group = await get_user_group(group_id)
            if not existing_group:
                raise ValueError(f"User group '{group_id}' not found")
            
            # 检查是否有活跃的使用
            if not force:
                stats = await self.get_group_statistics(group_id)
                if stats.get("active_sessions", 0) > 0:
                    raise ValueError("Cannot delete user group with active sessions. Use force=true to override.")
            
            # 清理相关数据
            await self._cleanup_group_data(group_id)
            
            # 删除用户组
            success = await delete_user_group(group_id)
            if not success:
                raise RuntimeError("Failed to delete user group from database")
            
            logger.info(f"Deleted user group: {group_id}")
            
            return {
                "group_id": group_id,
                "status": "deleted",
                "deleted_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error deleting user group {group_id}: {str(e)}")
            raise
    
    async def _cleanup_group_data(self, group_id: str):
        """清理用户组相关数据"""
        try:
            # 清理上下文数据
            conversations = await self.context_service.list_group_conversations(group_id)
            for conv in conversations:
                await self.context_service.clear_conversation_context(
                    group_id, conv.get("conversation_id", "")
                )
            
            # 清理配额数据
            quotas = await get_user_group_quotas(group_id)
            for quota in quotas:
                await delete_user_group_quota(group_id, quota.get("id"))
            
            # 清理API密钥数据
            api_keys = await get_user_group_api_keys(group_id)
            for key in api_keys:
                await delete_user_group_api_key(key.get("id"))
            
            logger.info(f"Cleaned up data for user group: {group_id}")
            
        except Exception as e:
            logger.warning(f"Error cleaning up data for group {group_id}: {str(e)}")
    
    async def create_group_quota(self, group_id: str, request: QuotaCreateRequest) -> Dict[str, Any]:
        """创建用户组配额"""
        try:
            # 检查用户组是否存在
            group = await get_user_group(group_id)
            if not group:
                raise ValueError(f"User group '{group_id}' not found")
            
            # 创建配额
            quota_data = {
                "group_id": group_id,
                "quota_type": request.quota_type,
                "model_name": request.model_name,
                "quota_limit": request.quota_limit,
                "window_duration_minutes": request.window_duration_minutes
            }
            
            success = await create_user_group_quota(quota_data)
            if not success:
                raise RuntimeError("Failed to create quota in database")
            
            # 更新配额管理器
            await self.quota_manager.add_group_quota_config(
                group_id, request.quota_type, request.model_name, 
                request.quota_limit, request.window_duration_minutes
            )
            
            logger.info(f"Created quota for group {group_id}: {request.quota_type.value}")
            
            return {
                "group_id": group_id,
                "quota_type": request.quota_type.value,
                "model_name": request.model_name,
                "quota_limit": request.quota_limit,
                "status": "created"
            }
            
        except Exception as e:
            logger.error(f"Error creating quota for group {group_id}: {str(e)}")
            raise

    async def get_group_quotas(self, group_id: str) -> List[Dict[str, Any]]:
        """获取用户组配额列表"""
        try:
            quotas = await get_user_group_quotas(group_id)

            # 获取实时使用情况
            quota_status = await self.quota_manager.get_group_quota_status(group_id)

            # 合并配额配置和使用情况
            result = []
            for quota in quotas:
                quota_info = {
                    **quota,
                    "current_usage": 0,
                    "utilization_percent": 0.0,
                    "is_exceeded": False
                }

                # 查找对应的使用情况
                for status in quota_status:
                    if (status.get("quota_type") == quota.get("quota_type") and
                        status.get("model_name") == quota.get("model_name")):
                        quota_info.update({
                            "current_usage": status.get("current_usage", 0),
                            "utilization_percent": status.get("utilization_percent", 0.0),
                            "is_exceeded": status.get("is_exceeded", False)
                        })
                        break

                result.append(quota_info)

            return result

        except Exception as e:
            logger.error(f"Error getting quotas for group {group_id}: {str(e)}")
            return []

    async def update_group_quota(
        self,
        group_id: str,
        quota_id: int,
        quota_limit: int
    ) -> Dict[str, Any]:
        """更新用户组配额"""
        try:
            # 更新数据库
            success = await update_user_group_quota(group_id, quota_id, {"quota_limit": quota_limit})
            if not success:
                raise RuntimeError("Failed to update quota in database")

            logger.info(f"Updated quota {quota_id} for group {group_id}")

            return {
                "group_id": group_id,
                "quota_id": quota_id,
                "quota_limit": quota_limit,
                "status": "updated"
            }

        except Exception as e:
            logger.error(f"Error updating quota for group {group_id}: {str(e)}")
            raise

    async def delete_group_quota(self, group_id: str, quota_id: int) -> Dict[str, Any]:
        """删除用户组配额"""
        try:
            success = await delete_user_group_quota(group_id, quota_id)
            if not success:
                raise RuntimeError("Failed to delete quota from database")

            logger.info(f"Deleted quota {quota_id} for group {group_id}")

            return {
                "group_id": group_id,
                "quota_id": quota_id,
                "status": "deleted"
            }

        except Exception as e:
            logger.error(f"Error deleting quota for group {group_id}: {str(e)}")
            raise

    async def create_group_api_key(self, group_id: str, request: ApiKeyCreateRequest) -> Dict[str, Any]:
        """创建用户组API密钥"""
        try:
            # 检查用户组是否存在
            group = await get_user_group(group_id)
            if not group:
                raise ValueError(f"User group '{group_id}' not found")

            # 创建API密钥
            key_data = {
                "group_id": group_id,
                "api_key": request.api_key,
                "key_name": request.key_name or f"key-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
                "is_active": request.is_active
            }

            success = await create_user_group_api_key(key_data)
            if not success:
                raise RuntimeError("Failed to create API key in database")

            # 更新密钥管理器
            await self.key_manager.add_group_keys(group_id, [request.api_key])

            logger.info(f"Created API key for group {group_id}")

            return {
                "group_id": group_id,
                "key_name": key_data["key_name"],
                "api_key": request.api_key[:8] + "...",  # 隐藏完整密钥
                "status": "created"
            }

        except Exception as e:
            logger.error(f"Error creating API key for group {group_id}: {str(e)}")
            raise

    async def get_group_api_keys(self, group_id: str) -> List[Dict[str, Any]]:
        """获取用户组API密钥列表"""
        try:
            # api_keys = await get_user_group_api_keys(group_id)

            # # 隐藏完整的API密钥
            # result = []
            # for key in api_keys:
            #     key_info = {
            #         **key,
            #         "api_key": key.get("api_key", "")[:8] + "..." if key.get("api_key") else "***"
            #     }
            #     result.append(key_info)

            # return result
            return await get_user_group_api_keys(group_id)

        except Exception as e:
            logger.error(f"Error getting API keys for group {group_id}: {str(e)}")
            return []

    async def delete_group_api_key(self, group_id: str, key_id: int) -> Dict[str, Any]:
        """删除用户组API密钥"""
        try:
            success = await delete_user_group_api_key(group_id, key_id)
            if not success:
                raise RuntimeError("Failed to delete API key from database")

            logger.info(f"Deleted API key {key_id} for group {group_id}")

            return {
                "group_id": group_id,
                "key_id": key_id,
                "status": "deleted"
            }

        except Exception as e:
            logger.error(f"Error deleting API key for group {group_id}: {str(e)}")
            raise

    async def get_group_statistics(self, group_id: str) -> Dict[str, Any]:
        """获取用户组统计信息"""
        try:
            # 获取配额统计
            quota_stats = await self.quota_manager.get_group_quota_status(group_id)

            # 获取上下文统计
            context_stats = await self.context_service.get_context_statistics(group_id)

            # 计算总体统计
            total_requests = sum(q.get("current_usage", 0) for q in quota_stats)
            active_quotas = len([q for q in quota_stats if q.get("current_usage", 0) > 0])

            return {
                "group_id": group_id,
                "total_requests": total_requests,
                "active_quotas": active_quotas,
                "quota_utilization": quota_stats,
                "context_statistics": context_stats.get("group_statistics", {}),
                "active_sessions": 0,  # 需要实现会话跟踪
                "last_activity": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting statistics for group {group_id}: {str(e)}")
            return {
                "group_id": group_id,
                "error": str(e)
            }

    async def reset_group_quota(self, group_id: str, quota_type: Optional[str] = None) -> Dict[str, Any]:
        """重置用户组配额"""
        try:
            # 重置配额使用量
            reset_count = await self.quota_manager.reset_group_quota(group_id, quota_type)

            logger.info(f"Reset quota for group {group_id}, type: {quota_type or 'all'}")

            return {
                "group_id": group_id,
                "quota_type": quota_type or "all",
                "reset_count": reset_count,
                "status": "reset",
                "reset_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error resetting quota for group {group_id}: {str(e)}")
            raise

    async def batch_create_groups(self, requests: List[UserGroupCreateRequest]) -> Dict[str, Any]:
        """批量创建用户组"""
        try:
            results = []
            success_count = 0

            for request in requests:
                try:
                    result = await self.create_user_group(request)
                    results.append({
                        "group_id": request.group_id,
                        "status": "success",
                        "result": result
                    })
                    success_count += 1
                except Exception as e:
                    results.append({
                        "group_id": request.group_id,
                        "status": "error",
                        "error": str(e)
                    })

            return {
                "total": len(requests),
                "success": success_count,
                "failed": len(requests) - success_count,
                "results": results
            }

        except Exception as e:
            logger.error(f"Error in batch create groups: {str(e)}")
            raise


# 全局实例
user_group_service = UserGroupService() 