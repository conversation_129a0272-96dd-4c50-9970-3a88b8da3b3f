apiVersion: v1
kind: Secret
metadata:
  name: gemini-balance-secrets
  namespace: gemini-balance
type: Opaque
stringData:
  # 数据库密码
  MYSQL_USER: "gemini"
  MYSQL_PASSWORD: "your-secure-password-here"
  
  # API密钥 (需要base64编码或直接在stringData中)
  API_KEYS: '["AIzaSyxxxxxxxxxxxxxxxxxxx","AIzaSyxxxxxxxxxxxxxxxxxxx"]'
  VERTEX_API_KEYS: '["AQ.Abxxxxxxxxxxxxxxxxxxx"]'
  
  # 认证令牌
  ALLOWED_TOKENS: '["sk-123456"]'
  AUTH_TOKEN: "sk-123456"
  
  # 付费API密钥
  PAID_KEY: "AIzaSyxxxxxxxxxxxxxxxxxxx"
  
  # 图床配置
  SMMS_SECRET_TOKEN: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  PICGO_API_KEY: "xxxx"
  CLOUDFLARE_IMGBED_AUTH_CODE: "xxxxxxxxx"
  
  # 代理配置 (如果需要)
  PROXIES: '[]'

---
# 数据库密码单独管理
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secret
  namespace: gemini-balance
type: Opaque
stringData:
  mysql-root-password: "your-root-password-here"
  mysql-password: "your-secure-password-here"
