apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gemini-balance-ingress
  namespace: gemini-balance
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"  # 如果使用cert-manager
spec:
  tls:
  - hosts:
    - your-domain.com  # 替换为您的域名
    secretName: gemini-balance-tls
  rules:
  - host: your-domain.com  # 替换为您的域名
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gemini-balance-service
            port:
              number: 80

---
# 如果使用Oracle Cloud Infrastructure的原生Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gemini-balance-oci-ingress
  namespace: gemini-balance
  annotations:
    kubernetes.io/ingress.class: "oci-native-ingress"
    oci-native-ingress.oraclecloud.com/backend-policy: |
      {
        "healthChecker": {
          "protocol": "HTTP",
          "urlPath": "/health",
          "port": 8001,
          "intervalInMillis": 30000,
          "timeoutInMillis": 3000,
          "retries": 3
        }
      }
spec:
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gemini-balance-service
            port:
              number: 80
