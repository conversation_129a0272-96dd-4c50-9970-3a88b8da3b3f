apiVersion: apps/v1
kind: Deployment
metadata:
  name: gemini-balance
  namespace: gemini-balance
  labels:
    app: gemini-balance
spec:
  replicas: 3  # 高可用部署
  selector:
    matchLabels:
      app: gemini-balance
  template:
    metadata:
      labels:
        app: gemini-balance
    spec:
      containers:
      - name: gemini-balance
        image: your-registry/gemini-balance:latest  # 替换为您的镜像地址
        ports:
        - containerPort: 8001
        env:
        # 从ConfigMap加载配置
        - name: DATABASE_TYPE
          valueFrom:
            configMapKeyRef:
              name: gemini-balance-config
              key: DATABASE_TYPE
        - name: MYSQL_HOST
          valueFrom:
            configMapKeyRef:
              name: gemini-balance-config
              key: MYSQL_HOST
        - name: MYSQL_PORT
          valueFrom:
            configMapKeyRef:
              name: gemini-balance-config
              key: MYSQL_PORT
        - name: MYSQL_DATABASE
          valueFrom:
            configMapKeyRef:
              name: gemini-balance-config
              key: MYSQL_DATABASE
        
        # 从Secret加载敏感信息
        - name: MYSQL_USER
          valueFrom:
            secretKeyRef:
              name: gemini-balance-secrets
              key: MYSQL_USER
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: gemini-balance-secrets
              key: MYSQL_PASSWORD
        - name: API_KEYS
          valueFrom:
            secretKeyRef:
              name: gemini-balance-secrets
              key: API_KEYS
        - name: VERTEX_API_KEYS
          valueFrom:
            secretKeyRef:
              name: gemini-balance-secrets
              key: VERTEX_API_KEYS
        - name: AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: gemini-balance-secrets
              key: AUTH_TOKEN
        - name: ALLOWED_TOKENS
          valueFrom:
            secretKeyRef:
              name: gemini-balance-secrets
              key: ALLOWED_TOKENS
        
        # 其他配置
        envFrom:
        - configMapRef:
            name: gemini-balance-config
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        
        # 健康检查
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        
        # 挂载卷
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
      
      volumes:
      - name: logs
        emptyDir: {}
      - name: uploads
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: gemini-balance-service
  namespace: gemini-balance
spec:
  selector:
    app: gemini-balance
  ports:
  - port: 80
    targetPort: 8001
    protocol: TCP
  type: ClusterIP

---
# 负载均衡器服务 (Oracle Cloud Load Balancer)
apiVersion: v1
kind: Service
metadata:
  name: gemini-balance-lb
  namespace: gemini-balance
  annotations:
    service.beta.kubernetes.io/oci-load-balancer-shape: "flexible"
    service.beta.kubernetes.io/oci-load-balancer-shape-flex-min: "10"
    service.beta.kubernetes.io/oci-load-balancer-shape-flex-max: "100"
spec:
  type: LoadBalancer
  selector:
    app: gemini-balance
  ports:
  - port: 80
    targetPort: 8001
    protocol: TCP
  - port: 443
    targetPort: 8001
    protocol: TCP
