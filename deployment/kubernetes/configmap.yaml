apiVersion: v1
kind: ConfigMap
metadata:
  name: gemini-balance-config
  namespace: gemini-balance
data:
  # 数据库配置
  DATABASE_TYPE: "mysql"
  MYSQL_HOST: "mysql-service"
  MYSQL_PORT: "3306"
  MYSQL_DATABASE: "gemini_balance"
  
  # 应用配置
  BASE_URL: "https://generativelanguage.googleapis.com/v1beta"
  MAX_FAILURES: "10"
  MAX_RETRIES: "3"
  CHECK_INTERVAL_HOURS: "1"
  TIMEZONE: "Asia/Shanghai"
  TIME_OUT: "300"
  
  # 功能开关
  MODEL_SWITCH_ENABLED: "true"
  M<PERSON>EL_SWITCH_INTELLIGENT_SELECTION: "true"
  QUOTA_MONITORING_ENABLED: "true"
  QUOTA_WARNING_THRESHOLD: "0.8"
  INTELLIGENT_CACHE_ENABLED: "true"
  
  # 缓存配置
  CACHE_MAX_SIZE: "1000"
  CACHE_DEFAULT_TTL: "3600"
  
  # 日志配置
  LOG_LEVEL: "info"
  AUTO_DELETE_ERROR_LOGS_ENABLED: "true"
  AUTO_DELETE_ERROR_LOGS_DAYS: "7"
  
  # 流式配置
  FAKE_STREAM_ENABLED: "true"
  FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS: "5"
  
  # 安全配置
  URL_CONTEXT_ENABLED: "true"
  TOOLS_CODE_EXECUTION_ENABLED: "false"
  SHOW_SEARCH_LINK: "true"
  SHOW_THINKING_PROCESS: "true"
