# 🚀 Oracle Cloud Infrastructure Kubernetes 部署指南

## 📋 **部署概述**

本指南将帮助您将 Gemini Balance 部署到 Oracle Cloud Infrastructure (OCI) Kubernetes Engine (OKE)。

## 🔧 **前置要求**

### **1. OCI 账户和权限**
- Oracle Cloud Infrastructure 账户
- 创建 Kubernetes 集群的权限
- Container Registry 访问权限

### **2. 本地工具**
```bash
# 安装 OCI CLI
curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh | bash

# 安装 kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"

# 安装 Docker
# 根据您的操作系统安装 Docker
```

## 🏗️ **部署步骤**

### **步骤 1: 创建 OKE 集群**

1. **登录 OCI 控制台**
2. **导航到 Developer Services > Kubernetes Clusters (OKE)**
3. **创建集群**:
   - 集群名称: `gemini-balance-cluster`
   - Kubernetes 版本: 最新稳定版
   - 节点池配置:
     - 形状: `VM.Standard.E4.Flex` (2 OCPU, 16GB RAM)
     - 节点数量: 3 (高可用)
     - 启用自动扩缩容

### **步骤 2: 配置 kubectl**

```bash
# 配置 OCI CLI
oci setup config

# 获取 kubeconfig
oci ce cluster create-kubeconfig \
  --cluster-id <your-cluster-ocid> \
  --file $HOME/.kube/config \
  --region <your-region> \
  --token-version 2.0.0

# 验证连接
kubectl get nodes
```

### **步骤 3: 构建和推送 Docker 镜像**

```bash
# 登录到 OCI Container Registry
docker login <region-key>.ocir.io

# 构建镜像
docker build -f deployment/Dockerfile -t gemini-balance:latest .

# 标记镜像
docker tag gemini-balance:latest <region-key>.ocir.io/<tenancy-namespace>/gemini-balance:latest

# 推送镜像
docker push <region-key>.ocir.io/<tenancy-namespace>/gemini-balance:latest
```

### **步骤 4: 更新配置文件**

1. **编辑 `deployment/kubernetes/secret.yaml`**:
   ```yaml
   # 更新您的实际 API 密钥和密码
   API_KEYS: '["your-actual-api-key-1","your-actual-api-key-2"]'
   MYSQL_PASSWORD: "your-secure-database-password"
   AUTH_TOKEN: "your-secure-auth-token"
   ```

2. **编辑 `deployment/kubernetes/app.yaml`**:
   ```yaml
   # 更新镜像地址
   image: <region-key>.ocir.io/<tenancy-namespace>/gemini-balance:latest
   ```

3. **编辑 `deployment/kubernetes/ingress.yaml`**:
   ```yaml
   # 更新域名
   host: your-actual-domain.com
   ```

### **步骤 5: 部署应用**

```bash
# 创建命名空间
kubectl apply -f deployment/kubernetes/namespace.yaml

# 部署配置和密钥
kubectl apply -f deployment/kubernetes/configmap.yaml
kubectl apply -f deployment/kubernetes/secret.yaml

# 部署数据库
kubectl apply -f deployment/kubernetes/mysql.yaml

# 等待数据库就绪
kubectl wait --for=condition=ready pod -l app=mysql -n gemini-balance --timeout=300s

# 部署应用
kubectl apply -f deployment/kubernetes/app.yaml

# 配置 Ingress (可选)
kubectl apply -f deployment/kubernetes/ingress.yaml
```

### **步骤 6: 验证部署**

```bash
# 检查 Pod 状态
kubectl get pods -n gemini-balance

# 检查服务
kubectl get services -n gemini-balance

# 查看日志
kubectl logs -f deployment/gemini-balance -n gemini-balance

# 获取外部 IP (如果使用 LoadBalancer)
kubectl get service gemini-balance-lb -n gemini-balance
```

## 🔍 **访问应用**

### **方法 1: LoadBalancer (推荐)**
```bash
# 获取外部 IP
kubectl get service gemini-balance-lb -n gemini-balance

# 访问应用
http://<EXTERNAL-IP>/config
```

### **方法 2: 端口转发 (测试用)**
```bash
# 端口转发
kubectl port-forward service/gemini-balance-service 8001:80 -n gemini-balance

# 访问应用
http://localhost:8001/config
```

### **方法 3: Ingress (生产环境)**
```bash
# 配置域名解析到 Ingress IP
# 访问应用
https://your-domain.com/config
```

## 📊 **监控和维护**

### **查看资源使用情况**
```bash
# 查看 Pod 资源使用
kubectl top pods -n gemini-balance

# 查看节点资源使用
kubectl top nodes
```

### **扩缩容**
```bash
# 手动扩容
kubectl scale deployment gemini-balance --replicas=5 -n gemini-balance

# 配置自动扩缩容 (HPA)
kubectl autoscale deployment gemini-balance --cpu-percent=70 --min=3 --max=10 -n gemini-balance
```

### **更新应用**
```bash
# 构建新镜像
docker build -f deployment/Dockerfile -t gemini-balance:v2 .
docker tag gemini-balance:v2 <region-key>.ocir.io/<tenancy-namespace>/gemini-balance:v2
docker push <region-key>.ocir.io/<tenancy-namespace>/gemini-balance:v2

# 滚动更新
kubectl set image deployment/gemini-balance gemini-balance=<region-key>.ocir.io/<tenancy-namespace>/gemini-balance:v2 -n gemini-balance

# 查看更新状态
kubectl rollout status deployment/gemini-balance -n gemini-balance
```

## 🛡️ **安全最佳实践**

1. **使用 OCI Vault 管理密钥**
2. **启用网络安全组 (NSG)**
3. **配置 RBAC 权限**
4. **定期更新镜像和依赖**
5. **启用审计日志**

## 💰 **成本优化**

1. **使用 Spot 实例** (适用于开发环境)
2. **配置自动扩缩容**
3. **使用 OCI 免费层资源**
4. **监控资源使用情况**

## 🔧 **故障排除**

### **常见问题**

1. **Pod 无法启动**:
   ```bash
   kubectl describe pod <pod-name> -n gemini-balance
   kubectl logs <pod-name> -n gemini-balance
   ```

2. **数据库连接失败**:
   ```bash
   kubectl exec -it <mysql-pod> -n gemini-balance -- mysql -u root -p
   ```

3. **镜像拉取失败**:
   ```bash
   # 检查镜像仓库权限
   kubectl describe pod <pod-name> -n gemini-balance
   ```

## 🎉 **部署完成**

部署成功后，您将拥有：

- ✅ **高可用的 Kubernetes 集群**
- ✅ **自动扩缩容能力**
- ✅ **负载均衡和故障转移**
- ✅ **持久化数据存储**
- ✅ **SSL/TLS 加密**
- ✅ **监控和日志收集**

访问您的监控面板：`https://your-domain.com/config` 🚀
