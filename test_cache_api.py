#!/usr/bin/env python3
"""
测试缓存管理API端点
"""

import asyncio
import aiohttp
import json

async def test_cache_api():
    """测试缓存管理API"""
    base_url = "http://127.0.0.1:8001"
    headers = {
        'Authorization': 'Bearer sk-123456',
        'Content-Type': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        # 测试获取用户组列表（用于缓存管理）
        print("🔍 测试缓存管理的用户组列表...")
        async with session.get(f"{base_url}/admin/groups", headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 用户组列表获取成功，共 {data.get('total', 0)} 个用户组")
                for group in data.get('user_groups', []):
                    print(f"   - {group.get('group_id')}: {group.get('group_name')}")
                    
                    # 测试获取用户组缓存统计
                    group_id = group.get('group_id')
                    print(f"\n📊 测试用户组 {group_id} 的缓存统计...")
                    async with session.get(f"{base_url}/cache/groups/{group_id}/statistics", headers=headers) as cache_response:
                        if cache_response.status == 200:
                            cache_data = await cache_response.json()
                            print(f"✅ 缓存统计获取成功")
                            print(f"   命中率: {cache_data.get('hit_rate', 0)}%")
                            print(f"   缓存大小: {cache_data.get('cache_size', 0)}")
                        else:
                            print(f"❌ 缓存统计获取失败，状态码: {cache_response.status}")
                    break  # 只测试第一个用户组
            else:
                print(f"❌ 用户组列表获取失败，状态码: {response.status}")
                text = await response.text()
                print(f"   错误信息: {text}")

if __name__ == "__main__":
    asyncio.run(test_cache_api())
