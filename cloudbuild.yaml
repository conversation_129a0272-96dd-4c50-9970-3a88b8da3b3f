# Google Cloud Build 配置文件
steps:
  # 构建Docker镜像
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/gemini-balance', '.']
  
  # 推送镜像到Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/gemini-balance']
  
  # 部署到Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'gemini-balance'
      - '--image'
      - 'gcr.io/$PROJECT_ID/gemini-balance'
      - '--region'
      - 'asia-east1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'PORT=8080,GOOGLE_CLOUD_PROJECT=$PROJECT_ID'

# 构建选项
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# 构建超时
timeout: '1200s'

# 镜像
images:
  - 'gcr.io/$PROJECT_ID/gemini-balance'
