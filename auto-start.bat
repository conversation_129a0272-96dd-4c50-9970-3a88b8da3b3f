@echo off
chcp 65001 >nul
title Gemini Balance - 自动启动

echo.
echo ========================================
echo    🚀 Gemini Balance 自动启动脚本
echo ========================================
echo.

:: 检查 Python 是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Python，请先安装 Python 3.8+
    pause
    exit /b 1
)

:: 显示 Python 版本
echo ✅ Python 版本检查:
python --version

:: 检查是否在正确的目录
if not exist "start.py" (
    echo ❌ 错误: 未找到 start.py 文件，请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

:: 检查并创建虚拟环境
if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

:: 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

:: 升级 pip
echo 📦 升级 pip...
python -m pip install --upgrade pip --quiet

:: 安装依赖
echo 📦 安装项目依赖...
python -m pip install -r requirements.txt --quiet
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

:: 初始化项目
echo ⚙️ 初始化项目设置...
python start.py --setup
if %errorlevel% neq 0 (
    echo ❌ 项目初始化失败
    pause
    exit /b 1
)

:: 启动开发服务器
echo.
echo 🚀 启动开发服务器...
echo 📍 服务器地址: http://localhost:8001
echo 📍 管理面板: http://localhost:8001/admin
echo 📍 API 文档: http://localhost:8001/docs
echo.
echo 💡 提示: 按 Ctrl+C 停止服务器
echo.

python start.py --reload --host 0.0.0.0 --port 8001

echo.
echo 👋 服务器已停止
pause
