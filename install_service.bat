@echo off
chcp 65001 >nul
title 安装 Gemini Balance 服务

echo.
echo ========================================
echo    📦 Gemini Balance 服务安装程序
echo ========================================
echo.

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 需要管理员权限来安装服务
    echo.
    echo 请右键点击此脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

:: 检查 Python 是否可用
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Python，请先安装 Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python 版本检查:
py --version
echo.

:: 检查项目文件
if not exist "start.py" (
    echo ❌ 错误: 未找到 start.py 文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

if not exist "gemini_balance_service.py" (
    echo ❌ 错误: 未找到 gemini_balance_service.py 文件
    echo 请确保服务脚本文件存在
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过
echo.

:: 安装 pywin32 依赖
echo 📦 检查并安装 pywin32 依赖...
py -c "import win32serviceutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 pywin32...
    py -m pip install pywin32
    if %errorlevel% neq 0 (
        echo ❌ pywin32 安装失败
        pause
        exit /b 1
    )
    echo ✅ pywin32 安装成功
) else (
    echo ✅ pywin32 已安装
)
echo.

:: 安装项目依赖
echo 📦 安装项目依赖...
py -m pip install -r requirements.txt --quiet
if %errorlevel% neq 0 (
    echo ❌ 项目依赖安装失败
    pause
    exit /b 1
)
echo ✅ 项目依赖安装成功
echo.

:: 创建日志目录
if not exist "logs" (
    mkdir logs
    echo ✅ 创建日志目录
)

:: 安装服务
echo 🔧 正在安装 Gemini Balance 服务...
py gemini_balance_service.py install
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    🎉 服务安装成功！
    echo ========================================
    echo.
    echo 服务信息:
    echo   服务名称: GeminiBalance
    echo   显示名称: Gemini Balance API Service
    echo   描述: Gemini API 代理和负载均衡服务
    echo.
    echo 管理服务:
    echo   启动服务: py gemini_balance_service.py start
    echo   停止服务: py gemini_balance_service.py stop
    echo   重启服务: py gemini_balance_service.py restart
    echo   卸载服务: py gemini_balance_service.py uninstall
    echo.
    echo 或者使用服务管理器: service_manager.bat
    echo.
    
    :: 询问是否立即启动服务
    set /p start_now=是否立即启动服务？(Y/N): 
    if /i "%start_now%"=="Y" (
        echo.
        echo 🚀 正在启动服务...
        py gemini_balance_service.py start
        if %errorlevel% equ 0 (
            echo ✅ 服务启动成功！
            echo.
            echo 🌐 服务地址: http://localhost:8001
            echo 📚 API文档: http://localhost:8001/docs
            echo 📋 服务日志: logs\service.log
        ) else (
            echo ❌ 服务启动失败，请检查日志
        )
    )
    
) else (
    echo ❌ 服务安装失败
)

echo.
pause
