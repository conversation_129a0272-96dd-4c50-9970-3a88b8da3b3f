#!/usr/bin/env python3
"""
简化版Web界面测试脚本
"""

import requests
import time
import sys

def test_web_interfaces():
    """测试Web界面"""
    base_url = "http://localhost:8001"

    # 创建会话并设置认证
    session = requests.Session()

    # 先进行认证
    try:
        auth_response = session.post(f"{base_url}/auth", data={"auth_token": "sk-123456"}, allow_redirects=False)
        print(f"🔐 认证状态: {auth_response.status_code}")
    except Exception as e:
        print(f"⚠️  认证失败: {e}")

    # 测试页面列表
    test_pages = [
        ("/web", "主控制台"),
        ("/web/security", "安全策略中心"),
        ("/web/cache", "缓存优化中心"),
        ("/web/routing", "路由策略中心"),
        ("/web/analytics", "分析报告中心"),
        ("/web/settings", "系统设置中心"),
        ("/web/monitoring", "监控大屏"),
        ("/config", "配置中心"),
    ]

    print("🚀 开始Web界面测试...")
    print("=" * 50)

    passed = 0
    failed = 0

    for path, name in test_pages:
        try:
            start_time = time.time()
            response = session.get(f"{base_url}{path}", timeout=10, allow_redirects=True)
            duration = time.time() - start_time

            if response.status_code == 200:
                print(f"✅ {name:<20} 正常 ({duration:.2f}s)")
                passed += 1
            elif response.status_code in [302, 307]:  # 重定向到登录页面
                print(f"🔒 {name:<20} 需要认证 ({duration:.2f}s)")
                passed += 1  # 认为这是正常的，因为页面存在
            else:
                print(f"❌ {name:<20} 错误: {response.status_code}")
                failed += 1

        except Exception as e:
            print(f"❌ {name:<20} 异常: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: 通过 {passed}, 失败 {failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print(f"⚠️  有 {failed} 个测试失败")
        return False

if __name__ == "__main__":
    success = test_web_interfaces()
    sys.exit(0 if success else 1)
