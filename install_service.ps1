# Gemini Balance 服务安装脚本 (PowerShell)
# 需要以管理员身份运行

Write-Host ""
Write-Host "========================================"
Write-Host "   📦 Gemini Balance 服务安装"
Write-Host "========================================"
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限！" -ForegroundColor Red
    Write-Host "请右键点击 PowerShell，选择'以管理员身份运行'，然后执行此脚本"
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 管理员权限检查通过" -ForegroundColor Green
Write-Host ""

# 切换到脚本目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

# 检查必要文件
if (-not (Test-Path "gemini_balance_service.py")) {
    Write-Host "❌ 未找到 gemini_balance_service.py 文件" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 安装服务
Write-Host "🔧 正在安装服务..." -ForegroundColor Yellow
try {
    $result = & py gemini_balance_service.py install 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ 服务安装成功！" -ForegroundColor Green
        Write-Host ""
        Write-Host "服务名称: GeminiBalance"
        Write-Host "显示名称: Gemini Balance API Service"
        Write-Host ""
        
        # 启动服务
        Write-Host "🚀 正在启动服务..." -ForegroundColor Yellow
        $startResult = & py gemini_balance_service.py start 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 服务启动成功！" -ForegroundColor Green
            Write-Host ""
            Write-Host "🌐 服务地址: http://localhost:8001"
            Write-Host "📚 API文档: http://localhost:8001/docs"
            Write-Host ""
            Write-Host "管理命令:"
            Write-Host "  启动: py gemini_balance_service.py start"
            Write-Host "  停止: py gemini_balance_service.py stop"
            Write-Host "  重启: py gemini_balance_service.py restart"
            Write-Host "  卸载: py gemini_balance_service.py uninstall"
        } else {
            Write-Host "❌ 服务启动失败" -ForegroundColor Red
            Write-Host "错误信息: $startResult"
            Write-Host "请检查日志: logs\service.log"
        }
    } else {
        Write-Host "❌ 服务安装失败" -ForegroundColor Red
        Write-Host "错误信息: $result"
    }
} catch {
    Write-Host "❌ 安装过程中出现异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "按任意键退出"
