@echo off
chcp 65001 >nul
title 安装 Gemini Balance 服务

echo.
echo ========================================
echo    📦 Gemini Balance 服务安装
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限！
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

:: 安装服务
echo 🔧 正在安装服务...
py gemini_balance_service.py install

if %errorlevel% equ 0 (
    echo.
    echo ✅ 服务安装成功！
    echo.
    echo 服务名称: GeminiBalance
    echo 显示名称: Gemini Balance API Service
    echo.
    
    :: 启动服务
    echo 🚀 正在启动服务...
    py gemini_balance_service.py start
    
    if %errorlevel% equ 0 (
        echo ✅ 服务启动成功！
        echo.
        echo 🌐 服务地址: http://localhost:8001
        echo 📚 API文档: http://localhost:8001/docs
        echo.
        echo 管理命令:
        echo   启动: py gemini_balance_service.py start
        echo   停止: py gemini_balance_service.py stop
        echo   重启: py gemini_balance_service.py restart
        echo   卸载: py gemini_balance_service.py uninstall
    ) else (
        echo ❌ 服务启动失败
        echo 请检查日志: logs\service.log
    )
) else (
    echo ❌ 服务安装失败
)

echo.
pause
