@echo off
chcp 65001 >nul
title Install Gemini Balance Service

:: 切换到脚本所在目录
cd /d "%~dp0"

echo.
echo ========================================
echo    Gemini Balance Service Install
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this script and select "Run as administrator"
    pause
    exit /b 1
)

echo OK: Administrator privileges verified
echo.

:: 检查项目文件
if not exist "gemini_balance_service.py" (
    echo ERROR: gemini_balance_service.py not found
    echo Please ensure you are in the correct project directory
    pause
    exit /b 1
)

echo OK: Project files verified
echo.

:: 安装服务
echo Installing service...
py gemini_balance_service.py install

if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Service installed successfully!
    echo.
    echo Service Name: GeminiBalance
    echo Display Name: Gemini Balance API Service
    echo.

    :: 启动服务
    echo Starting service...
    py gemini_balance_service.py start

    if %errorlevel% equ 0 (
        echo SUCCESS: Service started successfully!
        echo.
        echo Service URL: http://localhost:8001
        echo API Docs: http://localhost:8001/docs
        echo.
        echo Management commands:
        echo   Start:     py gemini_balance_service.py start
        echo   Stop:      py gemini_balance_service.py stop
        echo   Restart:   py gemini_balance_service.py restart
        echo   Uninstall: py gemini_balance_service.py uninstall
    ) else (
        echo ERROR: Service start failed
        echo Check logs: logs\service.log
    )
) else (
    echo ERROR: Service installation failed
)

echo.
pause
