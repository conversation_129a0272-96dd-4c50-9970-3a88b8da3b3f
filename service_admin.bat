@echo off
chcp 65001 >nul
title Gemini Balance 服务管理 (管理员)

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 需要管理员权限
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 切换到脚本目录
cd /d "%~dp0"

echo.
echo ========================================
echo    Gemini Balance 服务管理 (管理员)
echo ========================================
echo.

:menu
echo 请选择操作:
echo.
echo [1] 安装服务
echo [2] 启动服务
echo [3] 停止服务
echo [4] 重启服务
echo [5] 卸载服务
echo [6] 查看服务状态
echo [7] 查看服务日志
echo [8] 测试应用程序
echo [9] 检查端口状态
echo [0] 退出
echo.
set /p choice=请输入选项 (0-9): 

if "%choice%"=="1" goto install
if "%choice%"=="2" goto start
if "%choice%"=="3" goto stop
if "%choice%"=="4" goto restart
if "%choice%"=="5" goto uninstall
if "%choice%"=="6" goto status
if "%choice%"=="7" goto logs
if "%choice%"=="8" goto test
if "%choice%"=="9" goto port
if "%choice%"=="0" goto exit
goto menu

:install
echo.
echo [安装服务]
echo.
py gemini_balance_service.py install
if %errorlevel% equ 0 (
    echo.
    echo 设置服务为自动启动...
    sc config GeminiBalance start= auto
    echo.
    echo 服务安装完成！
) else (
    echo 服务安装失败！
)
pause
goto menu

:start
echo.
echo [启动服务]
echo.
sc start GeminiBalance
if %errorlevel% equ 0 (
    echo 服务启动命令已发送，等待启动...
    timeout /t 5 /nobreak >nul
    sc query GeminiBalance
) else (
    echo 服务启动失败！
)
pause
goto menu

:stop
echo.
echo [停止服务]
echo.
sc stop GeminiBalance
if %errorlevel% equ 0 (
    echo 服务停止命令已发送，等待停止...
    timeout /t 3 /nobreak >nul
    sc query GeminiBalance
) else (
    echo 服务停止失败！
)
pause
goto menu

:restart
echo.
echo [重启服务]
echo.
echo 正在停止服务...
sc stop GeminiBalance
timeout /t 5 /nobreak >nul
echo 正在启动服务...
sc start GeminiBalance
timeout /t 5 /nobreak >nul
sc query GeminiBalance
pause
goto menu

:uninstall
echo.
echo [卸载服务]
echo.
set /p confirm=确定要卸载服务吗？(Y/N): 
if /i "%confirm%"=="Y" (
    echo 正在停止服务...
    sc stop GeminiBalance >nul 2>&1
    timeout /t 3 /nobreak >nul
    echo 正在卸载服务...
    py gemini_balance_service.py remove
    if %errorlevel% equ 0 (
        echo 服务卸载成功！
    ) else (
        echo 服务卸载失败！
    )
) else (
    echo 取消卸载
)
pause
goto menu

:status
echo.
echo [服务状态]
echo.
sc query GeminiBalance
echo.
echo 详细状态:
sc queryex GeminiBalance
pause
goto menu

:logs
echo.
echo [服务日志]
echo.
if exist "logs\service.log" (
    echo 服务日志 (最后20行):
    echo ----------------------------------------
    powershell "Get-Content logs\service.log -Tail 20 -Encoding UTF8"
    echo ----------------------------------------
) else (
    echo 服务日志文件不存在
)
echo.
if exist "logs\app_stdout.log" (
    echo 应用输出日志 (最后10行):
    echo ----------------------------------------
    powershell "Get-Content logs\app_stdout.log -Tail 10 -Encoding UTF8"
    echo ----------------------------------------
) else (
    echo 应用输出日志文件不存在
)
echo.
if exist "logs\app_stderr.log" (
    echo 应用错误日志 (最后10行):
    echo ----------------------------------------
    powershell "Get-Content logs\app_stderr.log -Tail 10 -Encoding UTF8"
    echo ----------------------------------------
) else (
    echo 应用错误日志文件不存在
)
pause
goto menu

:test
echo.
echo [测试应用程序]
echo.
echo 正在测试应用程序直接启动...
echo 按 Ctrl+C 停止测试
echo.
py start.py
pause
goto menu

:port
echo.
echo [端口状态]
echo.
echo 检查端口8001状态:
netstat -ano | findstr :8001
if %errorlevel% neq 0 (
    echo 端口8001未被占用
) else (
    echo 端口8001正在使用中
)
echo.
echo 检查所有Python进程:
tasklist | findstr python
pause
goto menu

:exit
echo.
echo 感谢使用 Gemini Balance 服务管理器！
echo.
exit /b 0
