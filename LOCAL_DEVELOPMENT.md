# 🖥️ 本地开发指南

## 🚀 快速开始

### 方式一：一键启动（推荐）

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x start-local.sh

# 启动本地开发环境
./start-local.sh
```

#### Windows
```cmd
# 双击运行或命令行执行
start-local.bat
```

#### Python直接启动
```bash
# 初始化本地环境
python start.py --setup

# 启动开发服务器
python start.py --reload

# 检查本地服务
python start.py --check-services
```

### 方式二：Docker Compose

```bash
# 启动完整开发环境
docker-compose -f docker-compose.local.yml up -d

# 启动基础服务
docker-compose -f docker-compose.local.yml up -d gemini-balance-local redis-local mysql-local

# 仅启动外部服务
docker-compose -f docker-compose.local.yml up -d redis-local mysql-local
```

## 📋 环境配置

### 1. 本地配置文件

项目提供了专门的本地开发配置：

- **`.env.local`** - 本地开发环境配置模板
- **`.env`** - 实际使用的配置文件（从.env.local复制）

### 2. 配置说明

```env
# 开发模式配置
DEBUG=true
RELOAD=true
LOG_LEVEL=DEBUG

# 本地数据库
DATABASE_URL=sqlite:///./data/gemini_balance_local.db

# 内存缓存（无需Redis）
CACHE_BACKEND=memory

# 宽松的安全设置
ALLOWED_HOSTS=*
CORS_ORIGINS=*
```

### 3. 数据库选择

#### SQLite（推荐）
```env
DATABASE_URL=sqlite:///./data/gemini_balance_local.db
```
- ✅ 无需额外安装
- ✅ 文件数据库，便于开发
- ✅ 支持所有功能

#### MySQL（可选）
```env
DATABASE_URL=mysql+aiomysql://gemini_user:gemini_pass@localhost:3306/gemini_balance
```
- 🐳 Docker: `docker-compose up -d mysql-local`
- 📦 本地安装: 需要手动安装MySQL

#### PostgreSQL（可选）
```env
DATABASE_URL=postgresql+asyncpg://gemini_user:gemini_pass@localhost:5432/gemini_balance
```
- 🐳 Docker: `docker-compose up -d postgres-local`
- 📦 本地安装: 需要手动安装PostgreSQL

## 🛠️ 开发工具

### 1. 内置服务

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | http://localhost:8001/ | API服务 |
| API文档 | http://localhost:8001/docs | Swagger文档 |
| 连接池监控 | http://localhost:8001/connection-pool/health | 连接池状态 |
| 缓存分析 | http://localhost:8001/cache/enhanced/analytics | 缓存性能 |
| 智能路由 | http://localhost:8001/routing/health | 路由状态 |
| 系统指标 | http://localhost:8001/metrics | Prometheus指标 |

### 2. 外部服务（Docker）

| 服务 | 地址 | 凭据 |
|------|------|------|
| MySQL | localhost:3306 | gemini_user/gemini_pass |
| PostgreSQL | localhost:5432 | gemini_user/gemini_pass |
| Redis | localhost:6379 | 无密码 |
| Nginx | http://localhost/ | 反向代理 |

### 3. 监控工具（可选）

| 工具 | 地址 | 凭据 |
|------|------|------|
| Prometheus | http://localhost:9090/ | 指标收集 |
| Grafana | http://localhost:3000/ | admin/admin123 |
| Kibana | http://localhost:5601/ | 日志分析 |

## 🔧 开发命令

### Python环境管理

```bash
# 检查Python版本
python start.py --version

# 安装依赖
python start.py --install

# 检查本地服务
python start.py --check-services

# 初始化数据库
python start.py --init-db

# 运行测试
python start.py --test
```

### 服务管理

```bash
# 启动开发服务器（热重载）
python start.py --reload

# 启动生产模式
python start.py

# 指定端口和主机
python start.py --host 0.0.0.0 --port 8001

# 健康检查
python start.py --health
```

### Docker管理

```bash
# 启动所有服务
docker-compose -f docker-compose.local.yml up -d

# 查看服务状态
docker-compose -f docker-compose.local.yml ps

# 查看日志
docker-compose -f docker-compose.local.yml logs -f

# 停止服务
docker-compose -f docker-compose.local.yml down

# 重建服务
docker-compose -f docker-compose.local.yml up -d --build
```

## 📊 开发模式特性

### 1. 热重载
- ✅ 代码修改自动重启
- ✅ 配置文件变更自动加载
- ✅ 静态文件自动更新

### 2. 调试功能
- 🐛 详细错误信息
- 📝 调试日志输出
- 🔍 SQL查询日志
- ⚡ 性能分析

### 3. 开发工具
- 📚 自动API文档生成
- 🧪 内置测试端点
- 📊 实时性能监控
- 🔧 配置热更新

## 🧪 测试和验证

### 1. 功能测试

```bash
# 运行所有测试
python start.py --test

# 单独测试
python test_connection_pool.py
python test_cache_optimization.py
python test_intelligent_routing.py
```

### 2. 性能测试

```bash
# 压力测试
ab -n 1000 -c 10 http://localhost:8001/

# 连接池测试
curl http://localhost:8001/connection-pool/health

# 缓存测试
curl http://localhost:8001/cache/enhanced/analytics
```

### 3. API测试

```bash
# 健康检查
curl http://localhost:8001/

# API文档
curl http://localhost:8001/docs

# 监控端点
curl http://localhost:8001/metrics
```

## 🔍 故障排除

### 1. 常见问题

**端口被占用**
```bash
# 查找占用进程
lsof -i :8001  # Linux/macOS
netstat -ano | findstr :8001  # Windows

# 终止进程
kill -9 <PID>  # Linux/macOS
taskkill /PID <PID> /F  # Windows
```

**依赖安装失败**
```bash
# 升级pip
python -m pip install --upgrade pip

# 清理缓存
pip cache purge

# 重新安装
pip install -r requirements.txt --no-cache-dir
```

**数据库连接失败**
```bash
# 检查数据库文件
ls -la data/

# 重新初始化
python start.py --init-db

# 检查配置
cat .env | grep DATABASE_URL
```

### 2. 日志分析

```bash
# 查看应用日志
tail -f logs/local.log

# 查看错误日志
grep -i error logs/local.log

# 查看Docker日志
docker-compose -f docker-compose.local.yml logs -f
```

### 3. 性能调试

```bash
# 检查内存使用
ps aux | grep python

# 检查端口监听
netstat -tulpn | grep :8001

# 检查磁盘空间
df -h
```

## 🚀 生产部署准备

### 1. 环境变量检查

```bash
# 检查必要的环境变量
python -c "
import os
required = ['DATABASE_URL', 'SECRET_KEY', 'LOG_LEVEL']
for var in required:
    print(f'{var}: {os.getenv(var, \"NOT SET\")}')
"
```

### 2. 安全配置

```env
# 生产环境配置
DEBUG=false
SECRET_KEY=<strong-secret-key>
ALLOWED_HOSTS=your-domain.com
CORS_ORIGINS=https://your-frontend.com
```

### 3. 性能优化

```env
# 生产性能配置
CONNECTION_POOL_SIZE=100
CACHE_TTL=3600
LOG_LEVEL=INFO
ENABLE_METRICS=true
```

## 📖 开发最佳实践

### 1. 代码规范
- 使用类型提示
- 编写文档字符串
- 遵循PEP 8规范
- 添加单元测试

### 2. 配置管理
- 使用环境变量
- 分离开发/生产配置
- 不提交敏感信息
- 使用配置验证

### 3. 调试技巧
- 使用日志而非print
- 设置合适的日志级别
- 使用调试器断点
- 监控性能指标

## 🎉 开始开发

现在您已经准备好开始本地开发了！

1. **选择启动方式**: 脚本启动或Docker
2. **配置环境**: 复制.env.local为.env
3. **启动服务**: 运行启动脚本
4. **验证功能**: 访问监控端点
5. **开始编码**: 享受热重载开发体验

**祝您开发愉快！** 🎉
