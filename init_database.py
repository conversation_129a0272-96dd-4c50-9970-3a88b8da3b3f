#!/usr/bin/env python3
"""
数据库初始化脚本
用于初始化SQLite数据库并创建必要的表结构
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.sqlite_init import init_database
from app.database.sqlite_operations import create_user_group, list_user_groups
from app.log.logger import get_logger

logger = get_logger(__name__)

async def main():
    """主函数"""
    try:
        print("🚀 开始初始化数据库...")
        
        # 初始化数据库表结构
        await init_database()
        print("✅ 数据库表结构创建成功")
        
        # 验证数据库连接
        groups = await list_user_groups()
        print(f"✅ 数据库连接验证成功，当前用户组数量: {len(groups)}")
        
        print("🎉 数据库初始化完成！")
        print(f"📁 数据库文件位置: data/gemini_balance.db")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        logger.error(f"Database initialization failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
