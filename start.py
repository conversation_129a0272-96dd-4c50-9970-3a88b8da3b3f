import uvicorn
import os
import sys
from dotenv import load_dotenv

# 将项目根目录添加到 sys.path
# 这能确保 'app' 模块可以被正确导入
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """
    启动 FastAPI 应用程序。
    """
    # 优先加载 .env.local，如果不存在则加载 .env
    dotenv_path = os.path.join(os.path.dirname(__file__), '.env.local')
    if not os.path.exists(dotenv_path):
        dotenv_path = os.path.join(os.path.dirname(__file__), '.env')

    load_dotenv(dotenv_path=dotenv_path)
    
    # 从环境变量或默认值获取主机和端口
    # 默认端口设置为 8001，与您 app/main.py 中的配置一致
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8001))

    print(f"Gemini-Balance is starting...")
    print(f"Server will be running at: http://{host}:{port}")
    print("API documentation is available at: http://127.0.0.1:8001/docs")

    # 使用 uvicorn 启动服务器
    # reload=True 会在代码变更后自动重启服务器，非常适合开发环境
    # 在生产环境中，建议将 reload 设置为 False
    # app.main:app 字符串参数能确保在重载时正确地重新加载应用
    uvicorn.run("app.main:app", host=host, port=port, reload=True)

if __name__ == "__main__":
    main()