#!/usr/bin/env python3
"""
测试用户组API端点
"""

import asyncio
import aiohttp
import json

async def test_user_group_api():
    """测试用户组API"""
    base_url = "http://127.0.0.1:8001"
    headers = {
        'Authorization': 'Bearer sk-123456',
        'Content-Type': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        # 测试获取用户组列表
        print("🔍 测试获取用户组列表...")
        async with session.get(f"{base_url}/admin/groups", headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 用户组列表获取成功，共 {data.get('total', 0)} 个用户组")
                for group in data.get('user_groups', []):
                    print(f"   - {group.get('group_id')}: {group.get('group_name')}")
            else:
                print(f"❌ 用户组列表获取失败，状态码: {response.status}")
                text = await response.text()
                print(f"   错误信息: {text}")
        
        # 测试获取统计信息
        print("\n📊 测试获取统计信息...")
        async with session.get(f"{base_url}/admin/groups/statistics/overview", headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 统计信息获取成功")
                print(f"   总用户组: {data.get('total_groups', 0)}")
                print(f"   活跃用户组: {data.get('active_groups', 0)}")
                print(f"   总请求数: {data.get('total_requests', 0)}")
            else:
                print(f"❌ 统计信息获取失败，状态码: {response.status}")
                text = await response.text()
                print(f"   错误信息: {text}")

if __name__ == "__main__":
    asyncio.run(test_user_group_api())
