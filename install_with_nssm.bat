@echo off
title Install Gemini Balance Service with NSSM

:: Change to script directory
cd /d "%~dp0"

echo.
echo ========================================
echo    Gemini Balance Service (NSSM)
echo ========================================
echo.

:: Check admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this script and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator privileges verified
echo.

:: Check if NSSM exists
if not exist "nssm.exe" (
    echo [INFO] NSSM not found, downloading...
    echo Please download NSSM from: https://nssm.cc/download
    echo Extract nssm.exe to this directory and run this script again
    pause
    exit /b 1
)

echo [OK] NSSM found
echo.

:: Remove existing service if it exists
echo [INFO] Removing existing service if it exists...
nssm remove GeminiBalance confirm >nul 2>&1

:: Get Python path
for /f "tokens=*" %%i in ('where py') do set PYTHON_PATH=%%i
if "%PYTHON_PATH%"=="" (
    echo ERROR: Python not found in PATH
    pause
    exit /b 1
)

echo [OK] Python found: %PYTHON_PATH%
echo.

:: Install service
echo [INFO] Installing service...
nssm install GeminiBalance "%PYTHON_PATH%" "start.py"
nssm set GeminiBalance AppDirectory "%CD%"
nssm set GeminiBalance DisplayName "Gemini Balance API Service"
nssm set GeminiBalance Description "Gemini API proxy and load balancer service"
nssm set GeminiBalance Start SERVICE_AUTO_START

:: Set service to restart on failure
nssm set GeminiBalance AppExit Default Restart
nssm set GeminiBalance AppRestartDelay 5000

:: Set logging
nssm set GeminiBalance AppStdout "%CD%\logs\service_stdout.log"
nssm set GeminiBalance AppStderr "%CD%\logs\service_stderr.log"

:: Create logs directory
if not exist "logs" mkdir logs

echo [SUCCESS] Service installed successfully!
echo.
echo Service Name: GeminiBalance
echo Display Name: Gemini Balance API Service
echo.

:: Start service
echo [INFO] Starting service...
nssm start GeminiBalance

:: Wait a moment for service to start
timeout /t 3 /nobreak >nul

:: Check service status
nssm status GeminiBalance
if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Service is running!
    echo.
    echo Service URL: http://localhost:8001
    echo API Documentation: http://localhost:8001/docs
    echo.
    echo Management Commands:
    echo   Start:     nssm start GeminiBalance
    echo   Stop:      nssm stop GeminiBalance
    echo   Restart:   nssm restart GeminiBalance
    echo   Remove:    nssm remove GeminiBalance confirm
    echo.
    echo Logs:
    echo   stdout: logs\service_stdout.log
    echo   stderr: logs\service_stderr.log
) else (
    echo [ERROR] Service failed to start
    echo Check logs in: logs\service_stderr.log
)

echo.
pause
