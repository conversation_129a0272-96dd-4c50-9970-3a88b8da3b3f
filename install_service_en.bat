@echo off
title Install Gemini Balance Service

:: Change to script directory
cd /d "%~dp0"

echo.
echo ========================================
echo    Gemini Balance Service Installer
echo ========================================
echo.

:: Check admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this script and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator privileges verified
echo.

:: Check Python
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ first
    pause
    exit /b 1
)

echo [OK] Python found:
py --version
echo.

:: Check project files
if not exist "gemini_balance_service.py" (
    echo ERROR: gemini_balance_service.py not found
    echo Current directory: %CD%
    echo Please ensure you are in the correct project directory
    pause
    exit /b 1
)

echo [OK] Project files verified
echo Current directory: %CD%
echo.

:: Install service
echo [INFO] Installing service...
py gemini_balance_service.py install

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Service installed successfully!
    echo.
    echo Service Name: GeminiBalance
    echo Display Name: Gemini Balance API Service
    echo.
    
    :: Start service
    echo [INFO] Starting service...
    py gemini_balance_service.py start
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] Service started successfully!
        echo.
        echo Service URL: http://localhost:8001
        echo API Documentation: http://localhost:8001/docs
        echo.
        echo Management Commands:
        echo   Start:     py gemini_balance_service.py start
        echo   Stop:      py gemini_balance_service.py stop
        echo   Restart:   py gemini_balance_service.py restart
        echo   Uninstall: py gemini_balance_service.py uninstall
        echo.
        echo You can also use Windows Services Manager (services.msc)
    ) else (
        echo [ERROR] Service start failed
        echo Check service logs: logs\service.log
    )
) else (
    echo [ERROR] Service installation failed
    echo Make sure you have administrator privileges
)

echo.
echo Press any key to exit...
pause >nul
