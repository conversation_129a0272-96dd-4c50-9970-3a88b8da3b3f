# Core Framework - Python 3.13 兼容版本
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
pydantic>=2.6.0
pydantic-settings>=2.2.0
starlette>=0.35.0

# HTTP Client and Networking
httpx[socks]>=0.25.2
requests>=2.31.0

# Database
sqlalchemy>=2.0.23
databases[aiosqlite]>=0.8.0
databases[aiomysql]>=0.8.0
aiomysql>=0.2.0
pymysql>=1.1.0
aiosqlite>=0.19.0

# Caching and Storage
redis>=5.0.1
aioredis>=2.0.1

# Template Engine
jinja2>=3.1.2

# File Upload Support
python-multipart>=0.0.6

# Cryptography and Security
cryptography>=41.0.8
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# AI/ML APIs
openai>=1.3.7
google-generativeai>=0.3.2

# Scheduling and Background Tasks
apscheduler>=3.10.4

# Utilities
packaging>=23.2
python-dotenv>=1.0.0

# Web Server (Production)
hypercorn>=0.14.4
gunicorn>=21.2.0

# Development and Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1

# Logging and Monitoring
structlog>=23.2.0

# Data Processing
pandas>=2.1.4
numpy>=1.25.2

# Configuration Management
pyyaml>=6.0.1
toml>=0.10.2

# Date and Time
python-dateutil>=2.8.2

# Async Support
asyncio-mqtt>=0.16.1
aiofiles>=23.2.1

# Process Management
psutil>=5.9.6

# Environment Detection
distro>=1.8.0

# JSON Processing
orjson>=3.9.10

# Validation
email-validator>=2.1.0

# CLI Tools
click>=8.1.7
rich>=13.7.0

# Monitoring
prometheus-client>=0.19.0

# Health Checks
healthcheck>=1.3.3
